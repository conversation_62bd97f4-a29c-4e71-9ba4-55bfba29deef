package com.xiaojinzi.tally.module.main.module.main.domain

import android.util.Log
import androidx.annotation.DrawableRes
import com.xiaojinzi.component.impl.routeApi
import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.reactive.template.domain.BusinessUseCase
import com.xiaojinzi.reactive.template.domain.BusinessUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.reactive.template.domain.CommonUseCaseImpl
import com.xiaojinzi.support.activity_stack.ActivityStack
import com.xiaojinzi.support.annotation.StateHotObservable
import com.xiaojinzi.support.annotation.ViewModelLayer
import com.xiaojinzi.support.bean.StringItemDto
import com.xiaojinzi.support.ktx.DAY_MS
import com.xiaojinzi.support.ktx.awaitIgnoreException
import com.xiaojinzi.support.ktx.launchIgnoreError
import com.xiaojinzi.support.ktx.sharedStateIn
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.tally.lib.res.model.tally.MoneyFen
import com.xiaojinzi.tally.lib.res.model.tally.TallyAccountInsertDto
import com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryInsertDto
import com.xiaojinzi.tally.module.base.support.AppRouterMainApi
import com.xiaojinzi.tally.module.base.support.AppRouterUserApi
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.core.supabase.SupabaseConnector
import com.xiaojinzi.tally.module.core.supabase.SupabaseDataService
import com.xiaojinzi.tally.module.core.supabase.SupabaseConfig
import com.xiaojinzi.tally.module.datasource.storage.db.tally.TallyDb
import com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

enum class MainTabDto(
    @DrawableRes
    val iconRsd: Int,
    val nameStringItem: StringItemDto,
) {

    Bill(
        iconRsd = com.xiaojinzi.tally.lib.res.R.drawable.res_book1,
        nameStringItem = "账单".toStringItemDto(),
    ),

    Assets(
        iconRsd = com.xiaojinzi.tally.lib.res.R.drawable.res_account1,
        nameStringItem = "资产".toStringItemDto(),
    ),

    Calendar(
        iconRsd = com.xiaojinzi.tally.lib.res.R.drawable.res_calendar1,
        nameStringItem = "日历".toStringItemDto(),
    ),

    Statistics(
        iconRsd = com.xiaojinzi.tally.lib.res.R.drawable.res_analysis1,
        nameStringItem = "统计".toStringItemDto(),
    ),

    My(
        iconRsd = com.xiaojinzi.tally.lib.res.R.drawable.res_people1,
        nameStringItem = "我的".toStringItemDto(),
    ),

}

sealed class MainIntent {

    data object Hello : MainIntent()

    data class TabChanged(
        val value: MainTabDto,
    ) : MainIntent()

}

@ViewModelLayer
interface MainUseCase : BusinessUseCase {

    /**
     * tab list
     */
    @StateHotObservable
    val tabListStateOb: Flow<List<MainTabDto>>

    /**
     * 选中的 tab
     */
    @StateHotObservable
    val tabSelectedStateOb: Flow<MainTabDto>

}

@ViewModelLayer
class MainUseCaseImpl(
    private val commonUseCase: CommonUseCase = CommonUseCaseImpl(),
) : BusinessUseCaseImpl(
    commonUseCase = commonUseCase,
), MainUseCase {

    companion object {
        private const val TAG = "MainUseCase"
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override val tabListStateOb = AppServices
        .appConfigSpi
        .isShowAssetsTabStateOb
        .flatMapLatest { isShowAssetsTab ->
            flowOf(
                buildList {
                    add(MainTabDto.Bill)
                    add(MainTabDto.Calendar)
                    if (isShowAssetsTab) {
                        add(MainTabDto.Assets)
                    }
                    add(MainTabDto.Statistics)
                    add(MainTabDto.My)
                }
            )
        }.sharedStateIn(
            scope = scope,
        )

    override val tabSelectedStateOb = MutableStateFlow(
        value = MainTabDto.Bill,
    )

    @IntentProcess
    private suspend fun tabChanged(
        intent: MainIntent.TabChanged,
    ) {
        tabSelectedStateOb.emit(
            value = intent.value,
        )
    }

    /**
     * 公共方法：检查并初始化用户数据
     * 供 FirstSyncUseCase 调用
     */
    suspend fun checkAndInitializeUserData() {
        try {
            val userId = AppServices.userSpi.requiredLastUserId()
            println("[MainUseCase] 🔄 开始检查并初始化用户数据: userId=$userId")

            // 检查 Supabase 连接
            val supabaseConnector = SupabaseConnector.getInstance()
            if (!supabaseConnector.isInitialized) {
                supabaseConnector.initialize()
            }

            // 进行 Supabase 数据同步
            performSupabaseDataSync(userId)

        } catch (e: Exception) {
            println("[MainUseCase] ❌ 用户数据初始化失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 执行 Supabase 数据同步
     */
    private suspend fun performSupabaseDataSync(userId: String) {
        try {
            val supabaseConnector = SupabaseConnector.getInstance()
            // 确保连接器已初始化
            supabaseConnector.initialize()

            // 使用BookSyncUseCase同步账本数据
            val bookSyncResult = BookSyncUseCase.checkAndSelectAppropriateBook()
            if (bookSyncResult.isSuccess) {
                val selectedBook = bookSyncResult.getOrNull()
                if (selectedBook != null) {
                    println("[MainUseCase] ✅ 账本同步完成，选中账本: ${selectedBook.name} (${selectedBook.id})")
                    
                    // 同步类别数据到本地
                    try {
                        syncSupabaseCategoriesToLocal(selectedBook.id)
                        println("[MainUseCase] ✅ 类别数据同步完成")
                    } catch (syncError: Exception) {
                        println("[MainUseCase] ⚠️ 类别同步失败: ${syncError.message}")
                    }
                    
                    // 执行类别显示问题诊断
                    try {
                        diagnoseCategoryIssue()
                    } catch (diagError: Exception) {
                        println("[MainUseCase] ⚠️ 类别诊断失败: ${diagError.message}")
                    }
                } else {
                    println("[MainUseCase] ⚠️ 没有找到可用的账本")
                }
            } else {
                println("[MainUseCase] ❌ 账本同步失败: ${bookSyncResult.exceptionOrNull()?.message}")
            }
        } catch (error: Exception) {
            println("[MainUseCase] ⚠️ Supabase 数据同步异常: ${error.message}")
            throw error
        }
    }

    /**
     * 检查并初始化默认数据（类别和账户）
     * 优先使用 Supabase 云端初始化，本地初始化作为兜底
     * 修复：即使isInitData()返回true，也要检查类别数据完整性
     */
    private suspend fun initializeDefaultDataIfNeeded(bookId: String, userId: String) {
        try {
            println("[MainUseCase] 检查本地数据初始化状态: userId=$userId, bookId=$bookId")

            // 检查本地数据是否已初始化
            val isInitData = AppServices.tallyDataSourceSpi.isInitData()
            println("[MainUseCase] 📊 isInitData: $isInitData")

            // 即使数据已初始化，也要检查类别数据完整性
            val categories = AppServices.tallyDataSourceSpi.getCategoryByBookId(bookId)
                .filter { !it.isDeleted }
            println("[MainUseCase] 📊 当前账本类别数量: ${categories.size}")

            // 如果没有类别数据，强制进行初始化
            if (categories.isEmpty()) {
                println("[MainUseCase] ⚠️ 发现类别数据缺失，强制进行初始化...")

                // 尝试 Supabase 同步
                try {
                    println("[MainUseCase] 🔄 尝试 Supabase 同步恢复类别数据...")
                    performSupabaseDataSync(userId)

                    // 再次检查类别数据
                    val newCategories = AppServices.tallyDataSourceSpi.getCategoryByBookId(bookId)
                        .filter { !it.isDeleted }

                    if (newCategories.isEmpty()) {
                        println("[MainUseCase] ⚠️ Supabase 同步后仍无类别数据，使用本地兜底方案")
                        initializeLocalDataIfNeeded(bookId, userId)
                    } else {
                        println("[MainUseCase] ✅ Supabase 同步成功恢复 ${newCategories.size} 个类别")
                    }

                } catch (error: Exception) {
                    println("[MainUseCase] ⚠️ Supabase 同步失败，使用本地兜底方案: ${error.message}")
                    // 本地兜底初始化方案
                    initializeLocalDataIfNeeded(bookId, userId)
                }
            } else if (!isInitData) {
                // 如果有类别但isInitData为false，仍然尝试完整同步
                println("[MainUseCase] 🔄 数据未完全初始化，尝试 Supabase 同步...")
                try {
                    performSupabaseDataSync(userId)
                } catch (error: Exception) {
                    println("[MainUseCase] ⚠️ Supabase 同步失败: ${error.message}")
                    initializeLocalDataIfNeeded(bookId, userId)
                }
            } else {
                println("[MainUseCase] ✅ 数据已完整初始化，类别数量: ${categories.size}")
            }

        } catch (e: Exception) {
            println("[MainUseCase] ❌ 数据初始化失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 从 Supabase 同步类别数据到本地
     */
    private suspend fun syncSupabaseCategoriesToLocal(bookId: String) {
        try {
            println("[MainUseCase] 🔄 开始从 Supabase 同步类别数据...")

            // 获取当前用户ID
            val userId = AppServices.userSpi.requiredLastUserId()

            // 从 Supabase 获取类别数据
            val categoriesResult = SupabaseDataService.getBookCategories(bookId)
            if (categoriesResult.isSuccess) {
                val supabaseCategories = categoriesResult.getOrNull() ?: emptyList()
                println("[MainUseCase] 📥 从 Supabase 获取到 ${supabaseCategories.size} 个类别")

                if (supabaseCategories.isNotEmpty()) {
                    // 批量插入到本地数据库
                    supabaseCategories.forEachIndexed { index, category ->
                        // 直接转换为插入DTO（supabaseCategories 已经是 TallyCategoryDto 类型）
                        val insertDto = com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryInsertDto(
                            id = category.id,
                            userId = category.userId,
                            bookId = category.bookId,
                            parentId = category.parentId,
                            type = category.type.dbStr,
                            name = category.name,
                            iconName = category.iconName,
                            sort = category.sort,
                            timeCreate = category.timeCreate,
                            timeModify = category.timeModify,
                            isDeleted = category.isDeleted,
                            isSync = true
                        )

                        try {
                            val insertedId = AppServices.tallyDataSourceSpi.insertCategory(
                                target = insertDto
                            )
                            println("[MainUseCase] 📝 插入类别成功: ${category.name} (${insertedId})")
                        } catch (e: Exception) {
                            println("[MainUseCase] ❌ 插入类别失败: ${category.name} - ${e.message}")
                            e.printStackTrace()
                        }
                    }

                    println("[MainUseCase] ✅ 成功同步 ${supabaseCategories.size} 个类别到本地")

                    // 验证数据是否真的插入成功
                    try {
                        val localCategories = AppServices.tallyDataSourceSpi.subscribeAllCategory(
                            bookIdList = listOf(bookId)
                        ).first()
                        println("[MainUseCase] 🔍 验证本地类别数量: ${localCategories.size}")
                        println("[MainUseCase] 🔍 本地类别列表: ${localCategories.map { "${it.name}(${it.type.dbStr})" }}")
                    } catch (e: Exception) {
                        println("[MainUseCase] ❌ 验证本地数据失败: ${e.message}")
                    }
                } else {
                    println("[MainUseCase] ⚠️ Supabase 中没有类别数据")
                }
            } else {
                println("[MainUseCase] ❌ 从 Supabase 获取类别失败: ${categoriesResult.exceptionOrNull()?.message}")
            }
        } catch (e: Exception) {
            println("[MainUseCase] ❌ 同步类别数据异常: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 本地数据初始化检查（兜底方案）
     */
    private suspend fun initializeLocalDataIfNeeded(bookId: String, userId: String) {
        try {
            val tallyDataSourceSpi = AppServices.tallyDataSourceSpi

            // 检查是否有类别
            val categories = tallyDataSourceSpi.getCategoryByBookId(bookId)
                .filter { !it.isDeleted }

            if (categories.isEmpty()) {
                println("[MainUseCase] 账本缺少类别，开始本地初始化默认类别...")
                initializeDefaultCategories(bookId, userId)
            } else {
                println("[MainUseCase] 账本已有 ${categories.size} 个类别")
            }

            // 检查是否有账户
            val accounts = tallyDataSourceSpi.getAccountByBookId(
                bookId = bookId,
                isExcludeDeleted = true
            )

            if (accounts.isEmpty()) {
                println("[MainUseCase] 账本缺少账户，开始本地初始化默认账户...")
                initializeDefaultAccount(bookId, userId)
            } else {
                println("[MainUseCase] 账本已有 ${accounts.size} 个账户")
            }

        } catch (e: Exception) {
            println("[MainUseCase] ❌ 本地数据初始化失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 初始化默认类别
     */
    private suspend fun initializeDefaultCategories(bookId: String, userId: String) {
        try {
            val defaultCategories = createDefaultCategories(bookId, userId)

            AppServices.tallyDataSourceSpi.insertOrUpdateCategoryList(
                targetList = defaultCategories
            )

            println("[MainUseCase] ✅ 成功初始化 ${defaultCategories.size} 个默认类别")
        } catch (e: Exception) {
            println("[MainUseCase] ❌ 初始化默认类别失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 初始化默认账户
     */
    private suspend fun initializeDefaultAccount(bookId: String, userId: String) {
        try {
            val defaultAccount = TallyAccountInsertDto(
                userId = userId,
                bookId = bookId,
                iconName = "cash1",
                name = "现金",
                balanceInit = MoneyFen(0),
                isExcluded = false,
                isDefault = true,
            )

            AppServices.tallyDataSourceSpi.insertOrUpdateAccount(defaultAccount)

            println("[MainUseCase] ✅ 成功初始化默认账户: 现金")
        } catch (e: Exception) {
            println("[MainUseCase] ❌ 初始化默认账户失败: ${e.message}")
            e.printStackTrace()
        }
    }

    /**
     * 创建默认类别列表
     */
    private fun createDefaultCategories(bookId: String, userId: String): List<TallyCategoryInsertDto> {
        val currentTime = System.currentTimeMillis()

        return listOf(
            // 支出类别
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "食品餐饮",
                iconName = "chopsticksFork1",
                sort = currentTime - 1,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "购物消费",
                iconName = "shopping1",
                sort = currentTime - 2,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "交通出行",
                iconName = "car1",
                sort = currentTime - 3,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "娱乐休闲",
                iconName = "gamepad1",
                sort = currentTime - 4,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "医疗健康",
                iconName = "medical1",
                sort = currentTime - 5,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "spending",
                name = "居住缴费",
                iconName = "house1",
                sort = currentTime - 6,
            ),
            // 收入类别
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "income",
                name = "工资",
                iconName = "wage1",
                sort = currentTime - 7,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "income",
                name = "奖金",
                iconName = "bonus1",
                sort = currentTime - 8,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "income",
                name = "投资收益",
                iconName = "investment1",
                sort = currentTime - 9,
            ),
            TallyCategoryInsertDto(
                userId = userId,
                bookId = bookId,
                parentId = null,
                type = "income",
                name = "其他收入",
                iconName = "money1",
                sort = currentTime - 10,
            ),
        )
    }

    /**
     * 创建测试账单用于验证同步功能
     */
    suspend fun createTestBillForSync() {
        try {
            val currentUserInfo = AppServices.userSpi?.requiredUserInfo() ?: return
            val currentBookInfo = AppServices.tallyDataSourceSpi.requiredSelectedBookInfo()

            Log.d(TAG, "🧪 开始创建测试账单用于同步验证")

            val testBillId = AppServices.tallyDataSourceSpi.insertBill(
                target = com.xiaojinzi.tally.lib.res.model.tally.TallyBillInsertDto(
                    userId = currentUserInfo.id,
                    bookId = currentBookInfo.id,
                    type = com.xiaojinzi.tally.lib.res.model.tally.TallyBillDto.Type.NORMAL.value,
                    time = System.currentTimeMillis(),
                    amount = MoneyFen(-8800), // -88.00元
                    note = "测试账单 - 序列化修复验证 ${System.currentTimeMillis()}",
                    isNotCalculate = false,
                    isSync = false // 设置为未同步，触发同步流程
                ),
                labelIdList = emptyList(),
                imageUrlList = emptyList(),
                isNeedSync = true // 启用同步
            )

            Log.d(TAG, "✅ 测试账单创建成功: ID=$testBillId")
            Log.d(TAG, "🔄 同步流程已触发，请查看同步日志")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 创建测试账单失败: ${e.message}", e)
        }
    }

    override fun destroy() {
        super.destroy()
        commonUseCase.destroy()
    }

    init {
        // 尝试更新一下小部件
        AppServices
            .appWidgetSpi?.apply {
                this.tryUpdateWidget()
            }
        // 使用智能账本选择逻辑
        scope.launchIgnoreError {
            val userInfo =
                AppServices.userSpi.userInfoStateOb.firstOrNull() ?: return@launchIgnoreError
            
            println("[MainUseCase] 🔄 开始智能选择账本...")
            
            // 使用BookSyncUseCase的智能选择逻辑，优先选择有更多类别的账本
            val selectResult = BookSyncUseCase.checkAndSelectAppropriateBook()
            
            if (selectResult.isSuccess) {
                val selectedBook = selectResult.getOrNull()
                if (selectedBook != null) {
                    println("[MainUseCase] ✅ 智能选择账本: ${selectedBook.name} (${selectedBook.id})")
                    
                    // 检查并初始化默认数据
                    initializeDefaultDataIfNeeded(selectedBook.id, userInfo.id)
                } else {
                    println("[MainUseCase] ⚠️ 没有找到合适的账本")
                }
            } else {
                println("[MainUseCase] ❌ 智能选择账本失败: ${selectResult.exceptionOrNull()?.message}")
                
                // 降级到原有逻辑
                val tallyDataSourceSpi = AppServices.tallyDataSourceSpi
                val selectBook = tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
                if (selectBook != null) {
                    initializeDefaultDataIfNeeded(selectBook.id, userInfo.id)
                }
            }
        }
        // 用户信息有变更就刷新, 第一次进来也更新
        AppServices
            .userSpi
            .userInfoStateOb
            .filterNotNull()
            .onEach { _ ->
                // 更新会员信息
                AppServices
                    .userSpi
                    .updateVipInfoAction()
                    .awaitIgnoreException()
            }
            .launchIn(scope = scope)
        /**
         * 尝试做一些事
         * -. 更新 Token 信息
         * -. 更新会员信息
         * -. 更新账本信息(开启同步之后， 如果账本不存在, 这会被删除)
         * -. 开启同步
         */
        scope.launchIgnoreError {
            // 续费提醒
            val isTipRechargeVip = AppServices
                .appConfigSpi
                .isTipBeforeVipExpireStateOb
                .first()
            if (isTipRechargeVip) {
                AppServices
                    .userSpi
                    .vipInfoStateOb
                    .firstOrNull()
                    ?.let { vipInfo ->
                        val currentTime = System.currentTimeMillis()
                        if (vipInfo.expiredTime > currentTime && (vipInfo.expiredTime - currentTime) < 7 * DAY_MS) {
                            ActivityStack
                                .topAlive
                                ?.let { topAct ->
                                    AppRouterUserApi::class
                                        .routeApi()
                                        .toVipExpireRemindView(
                                            context = topAct,
                                        )
                                }
                        }
                    }
            }
            // 检查更新
            ActivityStack
                .topAlive
                ?.let { topAct ->
                    AppRouterMainApi::class
                        .routeApi()
                        .toAppUpdateView(
                            context = topAct,
                        )
                }
            // 刚启动, 做的事情可能有点多, 迟点开启同步
            delay(2000)
            // 开启同步
            AppServices
                .tallyDataSyncSpi
                .apply {
                    this?.setSyncSwitch(
                        enable = true,
                    )
                    // 启动时主动同步未同步的数据
                    println("[MainUseCase] 🔄 启动时触发数据同步...")
                    this?.trySync()
                }
        }
    }

    /**
     * 类别数据显示问题诊断
     * 问题：Supabase同步了50个类别到本地，但APP界面只显示11个
     */
    private suspend fun diagnoseCategoryIssue() {
        println("[诊断] 开始诊断类别显示问题...")
        println("[诊断] 问题描述: Supabase同步50个类别到本地，但APP界面只显示11个")
        
        try {
            // 1. 检查当前选中账本
            val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
            println("[诊断] 当前选中账本: ${selectedBook?.let { "${it.name}(${it.id})" } ?: "无选中账本"}")
            
            // 2. 检查数据库中的类别总数
            val allCategories = TallyDb.database.categoryDao().getAll()
            println("[诊断] 数据库中总类别数量: ${allCategories.count()}")
            
            // 3. 如果有选中账本，检查该账本的类别数据
            if (selectedBook != null) {
                val bookCategories = AppServices.tallyDataSourceSpi.subscribeAllCategory(
                    bookIdList = listOf(selectedBook.id)
                ).first()
                
                println("[诊断] 通过subscribeAllCategory获取的类别数量: ${bookCategories.count()}")
                
                val activeCategories = bookCategories.filter { !it.isDeleted }
                println("[诊断] 过滤未删除后的类别数量: ${activeCategories.count()}")
                
                val parentCategories = activeCategories.filter { it.parentId.isNullOrEmpty() }
                println("[诊断] 父类别数量: ${parentCategories.count()}")
                
                println("[诊断] 前5个类别:")
                activeCategories.take(5).forEach { category ->
                    println("[诊断]   - ${category.name}(${category.type}) parentId=${category.parentId}")
                }
            }
            
            // 4. 调用详细诊断函数
            debugCategoryBookIdDistribution()
            
            // 5. 检查并删除错误的本地账本
            deleteWrongLocalBookIfNeeded()
            
        } catch (e: Exception) {
            println("[诊断] ❌ 诊断过程中出现异常: ${e.message}")
            e.printStackTrace()
        }
    }
    
    /**
     * 诊断类别数据的bookId分布情况
     */
    private suspend fun debugCategoryBookIdDistribution() {
        println("\n[诊断] ========== 类别数据BookId分布诊断 ==========")
        
        // 获取所有类别数据
        val allCategories = TallyDb.database.categoryDao().getAll()
        println("[诊断] 数据库中总类别数量: ${allCategories.size}")
        
        // 按bookId分组统计
        val categoryByBookId = allCategories.groupBy { it.bookId }
        println("\n[诊断] 类别数据按账本分布:")
        categoryByBookId.forEach { (bookId, categories) ->
            println("[诊断]   账本ID: $bookId")
            println("[诊断]     类别数量: ${categories.size}")
            println("[诊断]     已删除数量: ${categories.count { it.isDeleted }}")
            println("[诊断]     未删除数量: ${categories.count { !it.isDeleted }}")
            
            // 显示前3个类别名称
            val sampleCategories = categories.take(3)
            sampleCategories.forEach { category ->
                println("[诊断]       - ${category.name}(${category.type}) deleted=${category.isDeleted}")
            }
            println()
        }
        
        // 获取当前选中账本
        val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
        if (selectedBook != null) {
            println("[诊断] 当前选中账本: ${selectedBook.name}(${selectedBook.id})")
            
            val selectedBookCategories = categoryByBookId[selectedBook.id] ?: emptyList()
            println("[诊断] 选中账本的类别数量: ${selectedBookCategories.size}")
            println("[诊断] 选中账本未删除类别数量: ${selectedBookCategories.count { !it.isDeleted }}")
            
            // 检查是否有其他账本有更多类别
            val otherBooksWithMoreCategories = categoryByBookId.filter { (bookId, categories) ->
                bookId != selectedBook.id && categories.count { !it.isDeleted } > selectedBookCategories.count { !it.isDeleted }
            }
            
            if (otherBooksWithMoreCategories.isNotEmpty()) {
                println("[诊断] ⚠️ 发现其他账本有更多类别:")
                otherBooksWithMoreCategories.forEach { (bookId, categories) ->
                    println("[诊断]   账本 $bookId: ${categories.count { !it.isDeleted }} 个未删除类别")
                }
            }
        } else {
            println("[诊断] ❌ 未选中任何账本")
        }
    }
    
    /**
     * 检查并删除错误的本地账本
     * 目的：删除只有11个类别的错误账本，保留有50个类别的正确账本
     */
    private suspend fun deleteWrongLocalBookIfNeeded() {
        println("\n[删除错误账本] ========== 开始检查错误账本 ==========")
        
        try {
            // 获取所有账本和类别数据
            val allBooks = TallyDb.database.bookDao().getAll()
            val allCategories = TallyDb.database.categoryDao().getAll()
            
            println("[删除错误账本] 当前账本数量: ${allBooks.size}")
            
            // 按bookId分组统计类别
            val categoryByBookId = allCategories.groupBy { it.bookId }
            
            // 查找错误账本（只有11个类别的账本）
            val wrongBooks = mutableListOf<String>()
            val correctBooks = mutableListOf<String>()
            
            categoryByBookId.forEach { (bookId, categories) ->
                val activeCategoryCount = categories.count { !it.isDeleted }
                when {
                    activeCategoryCount == 11 -> {
                        wrongBooks.add(bookId)
                        println("[删除错误账本] 发现错误账本: $bookId (${activeCategoryCount}个类别)")
                    }
                    activeCategoryCount == 50 -> {
                        correctBooks.add(bookId)
                        println("[删除错误账本] 发现正确账本: $bookId (${activeCategoryCount}个类别)")
                    }
                    else -> {
                        println("[删除错误账本] 其他账本: $bookId (${activeCategoryCount}个类别)")
                    }
                }
            }
            
            // 如果同时存在错误账本和正确账本，删除错误账本
            if (wrongBooks.isNotEmpty() && correctBooks.isNotEmpty()) {
                println("[删除错误账本] ✅ 确认删除条件满足：")
                println("[删除错误账本]   - 错误账本数量: ${wrongBooks.size}")
                println("[删除错误账本]   - 正确账本数量: ${correctBooks.size}")
                
                wrongBooks.forEach { wrongBookId ->
                    println("[删除错误账本] 🗑️ 删除错误账本: $wrongBookId")
                    
                    // 使用现有的clearAllDataByBookId方法删除账本及其所有相关数据
                    AppServices.tallyDataSourceSpi.clearAllDataByBookId(wrongBookId)
                    
                    println("[删除错误账本] ✅ 账本 $wrongBookId 删除完成")
                }
                
                // 验证删除结果
                val remainingBooks = TallyDb.database.bookDao().getAll()
                val remainingCategories = TallyDb.database.categoryDao().getAll()
                
                println("[删除错误账本] 删除后验证:")
                println("[删除错误账本]   - 剩余账本数量: ${remainingBooks.size}")
                println("[删除错误账本]   - 剩余类别数量: ${remainingCategories.size}")
                
                // 检查正确账本的类别数量
                correctBooks.forEach { correctBookId ->
                    val correctBookCategories = remainingCategories.filter { 
                        it.bookId == correctBookId && !it.isDeleted 
                    }
                    println("[删除错误账本]   - 正确账本 $correctBookId 类别数量: ${correctBookCategories.size}")
                }
                
                println("[删除错误账本] 🎉 错误账本删除完成！应用重启后将显示正确的50个类别")
                
            } else {
                println("[删除错误账本] ⚠️ 不满足删除条件：")
                println("[删除错误账本]   - 错误账本数量: ${wrongBooks.size}")
                println("[删除错误账本]   - 正确账本数量: ${correctBooks.size}")
                println("[删除错误账本] 跳过删除操作")
            }
            
        } catch (e: Exception) {
            println("[删除错误账本] ❌ 删除过程中出现异常: ${e.message}")
            e.printStackTrace()
        }
    }

}