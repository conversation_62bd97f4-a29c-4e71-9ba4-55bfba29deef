package com.xiaojinzi.tally.module.main.module.main.domain

import android.util.Log
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.core.supabase.SupabaseDataService
import com.xiaojinzi.tally.lib.res.model.tally.TallyBookDto
import com.xiaojinzi.tally.lib.res.model.tally.toInsertDto
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first

/**
 * 账本同步UseCase
 * 负责从Supabase查询和同步账本数据
 */
object BookSyncUseCase {
    
    private const val TAG = "BookSyncUseCase"
    
    /**
     * 从Supabase查询用户账本并同步到本地
     */
    suspend fun syncUserBooksFromSupabase(): Result<List<TallyBookDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始从Supabase同步用户账本...")
            
            // 获取当前用户ID
            val userId = AppServices.userSpi.requiredLastUserId()
            Log.d(TAG, "📋 当前用户ID: $userId")
            
            // 从Supabase查询用户账本
            val userBooksResult = SupabaseDataService.getUserBooks(userId)
            
            if (userBooksResult.isFailure) {
                val error = userBooksResult.exceptionOrNull()
                Log.e(TAG, "❌ 从Supabase查询账本失败: ${error?.message}", error)
                return@withContext Result.failure(error ?: Exception("查询账本失败"))
            }
            
            val userBooks = userBooksResult.getOrNull() ?: emptyList()
            Log.d(TAG, "📊 从Supabase查询到 ${userBooks.size} 个账本")
            
            // 获取本地所有账本
            val localBooks = AppServices.tallyDataSourceSpi.getAllBookList()
            Log.d(TAG, "📊 本地现有 ${localBooks.size} 个账本")
            
            // 清理本地存在但远程不存在的账本
            val remoteBookIds = userBooks.map { it.id }.toSet()
            Log.d(TAG, "📋 远程账本ID列表: $remoteBookIds")
            
            val orphanedBooks = localBooks.filter { it.id !in remoteBookIds }
            Log.d(TAG, "🔍 检查孤立账本: 本地${localBooks.size}个，远程${userBooks.size}个，孤立${orphanedBooks.size}个")
            
            // 详细列出所有账本
            localBooks.forEach { book ->
                val isOrphaned = book.id !in remoteBookIds
                Log.d(TAG, "📖 本地账本: ${book.name}(${book.id}) - ${if (isOrphaned) "孤立" else "正常"}")
            }
            
            if (orphanedBooks.isNotEmpty()) {
                Log.w(TAG, "🗑️ 发现 ${orphanedBooks.size} 个孤立账本，准备清理...")
                orphanedBooks.forEach { orphanedBook ->
                    try {
                        Log.d(TAG, "🗑️ 删除孤立账本: ${orphanedBook.name} (${orphanedBook.id})")
                        AppServices.tallyDataSourceSpi.clearAllDataByBookId(orphanedBook.id)
                        Log.d(TAG, "✅ 孤立账本删除成功: ${orphanedBook.name}")
                    } catch (e: Exception) {
                        Log.e(TAG, "❌ 删除孤立账本失败: ${orphanedBook.name}, 错误: ${e.message}", e)
                    }
                }
            } else {
                Log.d(TAG, "✅ 没有发现孤立账本")
            }
            
            if (userBooks.isEmpty()) {
                Log.w(TAG, "⚠️ Supabase中没有找到用户账本")
                return@withContext Result.success(emptyList())
            }
            
            // 同步账本到本地数据库
            userBooks.forEach { book ->
                try {
                    Log.d(TAG, "💾 同步账本到本地: ${book.name} (${book.id})")
                    
                    // 检查本地是否已存在该账本
                    val existingBooks = AppServices.tallyDataSourceSpi.getAllBookList()
                    val existingBook = existingBooks.find { it.id == book.id }

                    if (existingBook == null) {
                        // 本地不存在，插入新账本
                        AppServices.tallyDataSourceSpi.insertBookList(
                            targetList = listOf(book.toInsertDto())
                        )
                        Log.d(TAG, "✅ 账本插入成功: ${book.name}")
                    } else {
                        Log.d(TAG, "📝 账本已存在，跳过: ${book.name}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 同步账本失败: ${book.name}, 错误: ${e.message}", e)
                }
            }
            
            Log.d(TAG, "✅ 账本同步完成")
            Result.success(userBooks)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步用户账本失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查并自动选择合适的账本
     */
    suspend fun checkAndSelectAppropriateBook(): Result<TallyBookDto?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 检查并选择合适的账本...")
            
            // 先同步Supabase账本
            val syncResult = syncUserBooksFromSupabase()
            if (syncResult.isFailure) {
                Log.w(TAG, "⚠️ Supabase账本同步失败，使用本地账本")
            }
            
            // 获取所有本地账本
            val allBooks = AppServices.tallyDataSourceSpi.getAllBookList()
            Log.d(TAG, "📊 本地账本总数: ${allBooks.size}")

            if (allBooks.isEmpty()) {
                Log.w(TAG, "⚠️ 没有找到任何账本")
                return@withContext Result.success(null)
            }
            
            // 获取当前选中的账本
            val currentSelectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.first()
            Log.d(TAG, "📖 当前选中账本: ${currentSelectedBook?.name} (${currentSelectedBook?.id})")
            
            // 总是检查所有账本，选择类别数量最多的账本
            var bestBook: TallyBookDto? = null
            var maxCategoryCount = 0
            var currentBookCategoryCount = 0

            for (book in allBooks) {
                try {
                    val categories = AppServices.tallyDataSourceSpi.subscribeAllCategory(
                        bookIdList = listOf(book.id)
                    ).first()
                    val categoryCount = categories.size
                    Log.d(TAG, "📊 账本 ${book.name}(${book.id}) 有 $categoryCount 个类别")
                    
                    // 记录当前选中账本的类别数量
                    if (book.id == currentSelectedBook?.id) {
                        currentBookCategoryCount = categoryCount
                    }
                    
                    if (categoryCount > maxCategoryCount) {
                        maxCategoryCount = categoryCount
                        bestBook = book
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 查询账本类别失败: ${book.name}, 错误: ${e.message}")
                }
            }
            
            // 如果没有找到有类别的账本，选择第一个
            val targetBook = bestBook ?: allBooks.first()
            
            Log.d(TAG, "🎯 最佳账本: ${targetBook.name}(${targetBook.id}) 有 $maxCategoryCount 个类别")
            Log.d(TAG, "📋 当前账本类别数: $currentBookCategoryCount, 最佳账本类别数: $maxCategoryCount")
            
            // 切换到目标账本
            if (targetBook.id != currentSelectedBook?.id) {
                Log.d(TAG, "🔄 切换到账本: ${targetBook.name} (${targetBook.id})")
                AppServices.tallyDataSourceSpi.switchBook(
                    bookId = targetBook.id,
                    isTipAfterSwitch = false
                )
            }
            
            Log.d(TAG, "✅ 账本选择完成: ${targetBook.name}")
            Result.success(targetBook)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查和选择账本失败: ${e.message}", e)
            Result.failure(e)
        }
    }
}