package com.xiaojinzi.tally.module.main.module.main.view

import com.xiaojinzi.component.impl.RouterInterceptor
import com.xiaojinzi.component.impl.RouterResult
import com.xiaojinzi.support.ktx.awaitIgnoreException
import com.xiaojinzi.tally.lib.res.model.exception.NotLoggedInException
import com.xiaojinzi.tally.module.base.support.AppRouterConfig
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.main.module.main.domain.MainUseCaseImpl
import kotlinx.coroutines.flow.firstOrNull

/**
 * 进入主界面有可能是没有登录状态的
 * 如果没同步数据, 就会去同步数据
 */
class MainRouterInterceptor : RouterInterceptor {

    override suspend fun intercept(chain: RouterInterceptor.Chain): RouterResult {
        try {
            println("[MainRouterInterceptor] 开始主页面路由拦截检查...")

            val isInitData = AppServices
                .tallyDataSourceSpi
                .isInitData()
            val userInfo = AppServices
                .userSpi
                .userInfoStateOb
                .firstOrNull()

            println("[MainRouterInterceptor] 状态检查 - isInitData: $isInitData, userInfo: ${userInfo?.id}")

            return if (isInitData) {
                println("[MainRouterInterceptor] 数据已初始化，进入正常流程")
                if (userInfo != null) {
                    println("[MainRouterInterceptor] 用户已登录，更新账本列表...")
                    // 更新账本
                    AppServices
                        .tallyDataSourceSpi
                        .tryRefreshBookList(
                            userId = userInfo.id,
                        )
                        .awaitIgnoreException()
                    println("[MainRouterInterceptor] 账本列表更新完成")

                    // 强制检查并初始化类别数据 - 修复类别丢失问题
                    try {
                        println("[MainRouterInterceptor] 🔄 强制检查类别数据完整性...")

                        // 获取当前选中的账本
                        val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
                        if (selectedBook != null) {
                            // 检查当前账本是否有类别
                            val categories = AppServices.tallyDataSourceSpi.getCategoryByBookId(selectedBook.id)
                                .filter { !it.isDeleted }

                            println("[MainRouterInterceptor] 📊 当前账本 ${selectedBook.name} 有 ${categories.size} 个类别")

                            if (categories.isEmpty()) {
                                println("[MainRouterInterceptor] ⚠️ 发现类别数据丢失，开始强制初始化...")

                                // 强制调用本地初始化方法
                                val mainUseCase = MainUseCaseImpl()
                                mainUseCase.checkAndInitializeUserData()

                                // 再次检查类别数据
                                val newCategories = AppServices.tallyDataSourceSpi.getCategoryByBookId(selectedBook.id)
                                    .filter { !it.isDeleted }
                                println("[MainRouterInterceptor] ✅ 类别初始化完成，现在有 ${newCategories.size} 个类别")
                            } else {
                                println("[MainRouterInterceptor] ✅ 类别数据完整，无需初始化")
                            }
                        } else {
                            println("[MainRouterInterceptor] ⚠️ 未找到选中的账本，尝试同步数据...")
                            MainUseCaseImpl().checkAndInitializeUserData()
                        }

                    } catch (e: Exception) {
                        println("[MainRouterInterceptor] ❌ 类别数据检查失败: ${e.message}")
                        e.printStackTrace()
                    }
                } else {
                    println("[MainRouterInterceptor] ⚠️ 数据已初始化但用户未登录")
                }

                val result = chain.proceed(
                    request = chain.request(),
                )
                println("[MainRouterInterceptor] ✅ 主页面路由成功")
                result
            } else {
                println("[MainRouterInterceptor] 数据未初始化，检查用户登录状态...")
                if (userInfo == null) {
                    println("[MainRouterInterceptor] ❌ 用户未登录且数据未初始化，抛出NotLoggedInException")
                    throw NotLoggedInException()
                } else {
                    println("[MainRouterInterceptor] 用户已登录但数据未初始化，跳转到首次同步页面")
                    val result = chain.proceed(
                        request = chain.request()
                            .toBuilder()
                            .hostAndPath(
                                hostAndPath = AppRouterConfig.CORE_FIRST_SYNC,
                            )
                            .build(),
                    )
                    println("[MainRouterInterceptor] ✅ 首次同步页面跳转成功")
                    result
                }
            }
        } catch (e: NotLoggedInException) {
            println("[MainRouterInterceptor] ❌ 捕获到NotLoggedInException，重新抛出")
            throw e
        } catch (e: Exception) {
            println("[MainRouterInterceptor] ❌ 路由拦截过程中发生异常: ${e.message}")
            e.printStackTrace()
            throw RuntimeException("主页面路由拦截失败: ${e.message}", e)
        }
    }

}