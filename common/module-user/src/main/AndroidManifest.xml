<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <activity
            android:name=".module.log_off_confirm.view.LogOffConfirmAct"
            android:theme="@style/Theme.Tally.App.Transparent"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.privacy_agreement.view.PrivacyAgreementAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.bind_phone.view.BindPhoneAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.user_info.view.UserInfoAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.about_us.view.AboutUsAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.vip_buy.view.VipBuyAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.vip_expire_remind.view.VipExpireRemindAct"
            android:theme="@style/Theme.Tally.App.Transparent"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name=".module.login.view.LoginAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />



        <activity
            android:name=".module.email_password_login.view.EmailPasswordLoginAct"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />



    </application>

</manifest>