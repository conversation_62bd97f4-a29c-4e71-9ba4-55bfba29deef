package com.xiaojinzi.tally.module.user.module.email_password_login.domain

import android.content.Context
import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.support.annotation.StateHotObservable

/**
 * 邮箱密码登录意图
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
sealed class EmailPasswordLoginIntent {

    /**
     * 参数初始化
     */
    data class ParameterInit(
        val isFromSplash: Boolean = false,
    ) : EmailPasswordLoginIntent()

    /**
     * 邮箱输入变化
     */
    data class EmailChange(
        val email: String,
    ) : EmailPasswordLoginIntent()

    /**
     * 密码输入变化
     */
    data class PasswordChange(
        val password: String,
    ) : EmailPasswordLoginIntent()

    /**
     * 执行登录
     */
    data class Login(
        val context: Context,
    ) : EmailPasswordLoginIntent()
}
