package com.xiaojinzi.tally.module.user.module.email_password_login.view

import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.xiaojinzi.reactive.template.view.BusinessContentView
import com.xiaojinzi.support.compose.util.circleClip
import com.xiaojinzi.support.ktx.nothing
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.tally.lib.res.ui.APP_PADDING_LARGE
import com.xiaojinzi.tally.lib.res.ui.APP_PADDING_NORMAL
import com.xiaojinzi.tally.lib.res.ui.APP_PADDING_SMALL
import com.xiaojinzi.tally.module.base.theme.AppTheme
import com.xiaojinzi.tally.module.user.module.email_password_login.domain.EmailPasswordLoginIntent


/**
 * 邮箱密码登录页面视图
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
@ExperimentalMaterial3Api
@ExperimentalAnimationApi
@ExperimentalFoundationApi
@Composable
fun EmailPasswordLoginViewWrap() {
    BusinessContentView<EmailPasswordLoginViewModel>(
        needInit = true,
    ) { vm ->
        EmailPasswordLoginView(vm = vm)
    }
}

@ExperimentalMaterial3Api
@ExperimentalAnimationApi
@ExperimentalFoundationApi
@Composable
private fun EmailPasswordLoginView(
    vm: EmailPasswordLoginViewModel,
) {
    val context = LocalContext.current
    
    val email by vm.emailStateOb.collectAsState()
    val password by vm.passwordStateOb.collectAsState()
    val isLoading by vm.isLoadingStateOb.collectAsState()
    val canLogin by vm.canLoginStateOb.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = MaterialTheme.colorScheme.surface)
            .verticalScroll(rememberScrollState())
            .padding(horizontal = APP_PADDING_LARGE.dp)
            .padding(top = 60.dp, bottom = APP_PADDING_LARGE.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        
        // 标题区域
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(bottom = 40.dp)
        ) {
            // Logo
            Image(
                painter = painterResource(id = com.xiaojinzi.tally.lib.res.R.drawable.logo_03),
                contentDescription = null,
                modifier = Modifier
                    .size(120.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "欢迎回来",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "使用邮箱和密码登录您的账户",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                textAlign = TextAlign.Center
            )
        }

        // 输入区域
        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(APP_PADDING_NORMAL.dp)
        ) {
            // 邮箱输入框
            OutlinedTextField(
                value = email,
                onValueChange = { newEmail ->
                    vm.addIntent(
                        EmailPasswordLoginIntent.EmailChange(email = newEmail)
                    )
                },
                label = { Text("邮箱地址") },
                placeholder = { Text("请输入您的邮箱") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading,
                singleLine = true,
                shape = RoundedCornerShape(12.dp)
            )

            // 密码输入框
            OutlinedTextField(
                value = password,
                onValueChange = { newPassword ->
                    vm.addIntent(
                        EmailPasswordLoginIntent.PasswordChange(password = newPassword)
                    )
                },
                label = { Text("密码") },
                placeholder = { Text("请输入您的密码") },
                visualTransformation = PasswordVisualTransformation(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
                modifier = Modifier.fillMaxWidth(),
                enabled = !isLoading,
                singleLine = true,
                shape = RoundedCornerShape(12.dp)
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // 登录按钮
        Button(
            onClick = {
                vm.addIntent(
                    EmailPasswordLoginIntent.Login(context = context)
                )
            },
            enabled = canLogin,
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            shape = RoundedCornerShape(12.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = Color.White,
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("登录中...")
            } else {
                Text(
                    text = "登录",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        Spacer(modifier = Modifier.height(16.dp))

        // 提示文本
        Text(
            text = "登录即表示您同意我们的服务条款和隐私政策",
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(horizontal = APP_PADDING_NORMAL.dp)
        )
    }
}

@ExperimentalMaterial3Api
@ExperimentalAnimationApi
@ExperimentalFoundationApi
@Preview
@Composable
private fun EmailPasswordLoginViewPreview() {
    AppTheme {
        EmailPasswordLoginView(
            vm = viewModel<EmailPasswordLoginViewModel>().apply {
                nothing()
            }
        )
    }
}
