package com.xiaojinzi.tally.module.user.module.email_password_login.view

import com.xiaojinzi.reactive.view.BaseViewModel
import com.xiaojinzi.support.annotation.ViewLayer
import com.xiaojinzi.support.annotation.ViewModelLayer
import com.xiaojinzi.tally.module.user.module.email_password_login.domain.EmailPasswordLoginUseCase
import com.xiaojinzi.tally.module.user.module.email_password_login.domain.EmailPasswordLoginUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.reactive.template.domain.CommonUseCaseImpl

/**
 * 邮箱密码登录 ViewModel
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
@ViewModelLayer
class EmailPasswordLoginViewModel(
    private val commonUseCase: CommonUseCase = CommonUseCaseImpl(),
    private val useCase: EmailPasswordLoginUseCase = EmailPasswordLoginUseCaseImpl(commonUseCase),
) : BaseViewModel(),
    EmailPasswordLoginUseCase by useCase {


}
