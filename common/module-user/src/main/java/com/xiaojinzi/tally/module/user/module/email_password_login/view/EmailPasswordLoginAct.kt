package com.xiaojinzi.tally.module.user.module.email_password_login.view

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.core.view.WindowCompat
import com.xiaojinzi.component.anno.AttrValueAutowiredAnno
import com.xiaojinzi.component.anno.RouterAnno
import com.xiaojinzi.support.annotation.ViewLayer
import com.xiaojinzi.support.compose.StateBar
import com.xiaojinzi.support.ktx.initOnceUseViewModel
import com.xiaojinzi.support.ktx.translateStatusBar
import com.xiaojinzi.tally.module.base.theme.AppTheme
import com.xiaojinzi.tally.module.base.view.BaseBusinessAct
import com.xiaojinzi.tally.module.user.module.email_password_login.domain.EmailPasswordLoginIntent

/**
 * 邮箱密码登录页面
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
@RouterAnno(
    hostAndPath = "user/emailPasswordLogin",
)
@RouterAnno(
    hostAndPath = "user/emailLogin",
)
@ViewLayer
class EmailPasswordLoginAct : BaseBusinessAct<EmailPasswordLoginViewModel>() {

    @AttrValueAutowiredAnno("isFromSplash")
    var isFromSplash: Boolean? = null

    override fun getViewModelClass(): Class<EmailPasswordLoginViewModel> {
        return EmailPasswordLoginViewModel::class.java
    }

    @OptIn(
        ExperimentalMaterial3Api::class,
        ExperimentalAnimationApi::class,
        ExperimentalFoundationApi::class,
    )
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window.translateStatusBar()
        WindowCompat.setDecorFitsSystemWindows(window, false)

        initOnceUseViewModel {
            requiredViewModel().addIntent(
                intent = EmailPasswordLoginIntent.ParameterInit(
                    isFromSplash = isFromSplash ?: false,
                )
            )
        }

        setContent {
            AppTheme {
                StateBar {
                    EmailPasswordLoginViewWrap()
                }
            }
        }
    }
}
