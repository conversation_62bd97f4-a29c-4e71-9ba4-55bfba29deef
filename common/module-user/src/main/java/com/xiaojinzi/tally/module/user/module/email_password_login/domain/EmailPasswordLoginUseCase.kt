package com.xiaojinzi.tally.module.user.module.email_password_login.domain

import android.util.Log
import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.reactive.template.domain.BusinessUseCase
import com.xiaojinzi.reactive.template.domain.BusinessUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.support.annotation.StateHotObservable
import com.xiaojinzi.support.ktx.ErrorIgnoreContext
import com.xiaojinzi.support.ktx.launchIgnoreError
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.tally.lib.res.model.user.UserInfoDto
import com.xiaojinzi.tally.module.base.support.AppRouterMainApi
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.component.impl.routeApi
import com.xiaojinzi.tally.module.core.supabase.SupabaseConfig
import com.xiaojinzi.tally.module.core.supabase.UserDataInitializer
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.auth.providers.builtin.Email
import io.github.jan.supabase.auth.user.UserSession
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.SharingStarted

/**
 * 邮箱密码登录业务逻辑
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
interface EmailPasswordLoginUseCase : BusinessUseCase {

    @StateHotObservable
    val emailStateOb: StateFlow<String>

    @StateHotObservable
    val passwordStateOb: StateFlow<String>

    @StateHotObservable
    val isLoadingStateOb: StateFlow<Boolean>

    @StateHotObservable
    val canLoginStateOb: StateFlow<Boolean>
}

class EmailPasswordLoginUseCaseImpl(
    private val commonUseCase: CommonUseCase,
) : BusinessUseCaseImpl(
    commonUseCase = commonUseCase,
), EmailPasswordLoginUseCase {

    companion object {
        private const val TAG = "EmailPasswordLogin"
    }

    private val supabase = SupabaseConfig.client

    private val _emailStateOb = MutableStateFlow(value = "")
    override val emailStateOb: StateFlow<String> = _emailStateOb.asStateFlow()

    private val _passwordStateOb = MutableStateFlow(value = "")
    override val passwordStateOb: StateFlow<String> = _passwordStateOb.asStateFlow()

    private val _isLoadingStateOb = MutableStateFlow(value = false)
    override val isLoadingStateOb: StateFlow<Boolean> = _isLoadingStateOb.asStateFlow()

    override val canLoginStateOb: StateFlow<Boolean> = combine(
        emailStateOb,
        passwordStateOb,
        isLoadingStateOb,
    ) { email, password, isLoading ->
        !isLoading && email.isNotBlank() && password.isNotBlank() && isValidEmail(email)
    }.stateIn(
        scope = scope,
        started = SharingStarted.WhileSubscribed(),
        initialValue = false
    )

    @IntentProcess
    private suspend fun parameterInit(intent: EmailPasswordLoginIntent.ParameterInit) {
        // 初始化参数，暂时无需特殊处理
        Log.d(TAG, "参数初始化: isFromSplash=${intent.isFromSplash}")
    }

    @IntentProcess
    private suspend fun emailChange(intent: EmailPasswordLoginIntent.EmailChange) {
        _emailStateOb.value = intent.email
    }

    @IntentProcess
    private suspend fun passwordChange(intent: EmailPasswordLoginIntent.PasswordChange) {
        _passwordStateOb.value = intent.password
    }

    @IntentProcess
    private suspend fun login(intent: EmailPasswordLoginIntent.Login) {
        val email = emailStateOb.value.trim()
        val password = passwordStateOb.value.trim()

        if (!isValidEmail(email)) {
            tip(content = "请输入有效的邮箱地址".toStringItemDto())
            return
        }

        if (password.length < 6) {
            tip(content = "密码长度不能少于6位".toStringItemDto())
            return
        }

        _isLoadingStateOb.value = true

        try {
            Log.d(TAG, "========== 邮箱密码登录开始 ==========")
            Log.d(TAG, "邮箱: $email")

            // 1. 使用 Supabase 进行邮箱密码登录
            Log.d(TAG, "开始 Supabase 邮箱密码认证...")
            supabase.auth.signInWith(Email) {
                this.email = email
                this.password = password
            }

            // 获取当前会话和用户信息
            val session = supabase.auth.currentSessionOrNull()
            if (session == null) {
                Log.e(TAG, "❌ 登录失败：会话信息为空")
                tip(content = "登录失败，请检查邮箱和密码".toStringItemDto())
                return
            }

            val user = session.user
            if (user == null) {
                Log.e(TAG, "❌ 登录失败：用户信息为空")
                tip(content = "登录失败，请检查邮箱和密码".toStringItemDto())
                return
            }

            Log.d(TAG, "✅ Supabase 认证成功")
            Log.d(TAG, "用户ID: ${user.id}")
            Log.d(TAG, "用户邮箱: ${user.email}")

            // 2. 构建用户信息
            val userInfo = UserInfoDto(
                id = user.id,
                name = user.email ?: email,
                timeCreate = System.currentTimeMillis()
            )

            // 3. 保存用户信息到本地
            Log.d(TAG, "保存用户信息到本地...")
            val userSpiImpl = AppServices.userSpi as com.xiaojinzi.tally.module.user.spi.UserSpiImpl
            userSpiImpl.userInfoStateOb.emit(userInfo)
            userSpiImpl.latestUserIdStateOb.emit(user.id)
            Log.d(TAG, "✅ 用户信息保存成功")

            // 4. 初始化数据库
            Log.d(TAG, "开始初始化数据库...")
            try {
                AppServices.tallyDataSourceInitSpi.initTallyDataBase(userId = user.id)
                Log.d(TAG, "✅ 数据库初始化成功")
            } catch (e: Exception) {
                Log.e(TAG, "❌ 数据库初始化失败: ${e.message}", e)
                // 数据库初始化失败不应该阻止登录
            }

            // 5. 初始化 Supabase 用户数据
            Log.d(TAG, "开始初始化 Supabase 用户数据...")
            try {
                val initResult = UserDataInitializer.ensureUserDataInitialized(
                    userId = user.id,
                    userName = user.email ?: "用户"
                ).getOrThrow()

                if (initResult) {
                    Log.d(TAG, "✅ Supabase 用户数据初始化成功")

                    // 验证初始化结果
                    val checkResult = UserDataInitializer.checkUserInitialized(user.id).getOrNull()
                    if (checkResult != null) {
                        Log.d(TAG, "📊 初始化验证结果:")
                        Log.d(TAG, "  - 账本数量: ${checkResult.bookCount}")
                        Log.d(TAG, "  - 类别数量: ${checkResult.categoryCount}")
                        Log.d(TAG, "  - 账户数量: ${checkResult.accountCount}")
                    }
                } else {
                    Log.w(TAG, "⚠️ Supabase 用户数据初始化失败，但不影响登录")
                }
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ Supabase 用户数据初始化异常: ${e.message}，但不影响登录")
            }

            // 6. 跳转到主页
            Log.d(TAG, "登录成功，跳转到主页...")
            AppRouterMainApi::class
                .routeApi()
                .toMainView(
                    context = intent.context,
                )

            Log.d(TAG, "========== 邮箱密码登录完成 ==========")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 登录失败: ${e.message}", e)
            tip(content = "登录失败: ${e.message}".toStringItemDto())
        } finally {
            _isLoadingStateOb.value = false
        }
    }

    /**
     * 验证邮箱格式
     */
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
}
