package com.xiaojinzi.tally.module.base.support

object AppRouterConfig {

    const val xxx: String = "xxx/xxx"

    private const val HOST_BASE = "base"
    const val BASE_DATE_TIME_SELECT = "$HOST_BASE/dateTimeSelect"
    const val BASE_BOTTOM_MENU_SELECT = "$HOST_BASE/menuSelect"
    const val BASE_CENTER_MENU_SELECT = "$HOST_BASE/centerMenuSelect"
    const val BASE_WEB = "$HOST_BASE/web"

    private const val HOST_MAIN = "main"
    const val MAIN_MAIN = "$HOST_MAIN/main"
    const val MAIN_BILL = "$HOST_MAIN/bill"
    const val MAIN_CALENDAR = "$HOST_MAIN/calendar"
    const val MAIN_SETTING = "$HOST_MAIN/setting"
    const val MAIN_THEME_SELECT = "$HOST_MAIN/themeSelect"
    const val MAIN_APP_UPDATE = "$HOST_MAIN/appUpdate"
    const val MAIN_APP_SHARE = "$HOST_MAIN/appShare"

    private const val HOST_USER = "user"
    const val USER_PRIVACY_AGREEMENT = "$HOST_USER/privacyAgreement"
    const val USER_LOADING = "$HOST_USER/loading"
    const val USER_LOGIN = "$HOST_USER/login"
    const val USER_MY = "$HOST_USER/my"
    const val USER_VIP_BUY = "$HOST_USER/vipBuy"
    const val USER_VIP_EXPIRE_REMIND = "$HOST_USER/vipExpireRemind"
    const val USER_ABOUT_US = "$HOST_USER/aboutUs"
    const val USER_INFO = "$HOST_USER/info"
    const val USER_BIND_PHONE = "$HOST_USER/bindPhone"
    const val USER_LOG_OFF_CONFIRM = "$HOST_USER/logOffConfirm"
    const val USER_EMAIL_LOGIN = "$HOST_USER/emailLogin"
    const val USER_EMAIL_PASSWORD_LOGIN = "$HOST_USER/emailPasswordLogin"

    private const val HOST_CORE = "core"
    const val CORE_FIRST_SYNC = "$HOST_CORE/firstSync"
    const val CORE_LABEL_INFO = "$HOST_CORE/labelInfo"
    const val CORE_LABEL_CRUD = "$HOST_CORE/labelCrud"
    const val CORE_LABEL_SELECT = "$HOST_CORE/labelSelect"
    const val CORE_CATEGORY_INFO = "$HOST_CORE/categoryInfo"
    const val CORE_CATEGORY_SUB_INFO = "$HOST_CORE/categorySubInfo"
    const val CORE_CATEGORY_SELECT = "$HOST_CORE/categorySelect"
    const val CORE_CATEGORY_SELECT1 = "$HOST_CORE/categorySelect1"
    const val CORE_CATEGORY_CRUD = "$HOST_CORE/categoryCrud"
    const val CORE_BOOK_INFO = "$HOST_CORE/bookInfo"
    const val CORE_BOOK_CRUD = "$HOST_CORE/bookCrud"
    const val CORE_BOOK_SWITCH = "$HOST_CORE/bookSelect"
    const val CORE_BOOK_SELECT1 = "$HOST_CORE/bookSelect1"
    const val CORE_BOOK_MEMBER = "$HOST_CORE/bookMember"
    const val CORE_BOOK_INVITE = "$HOST_CORE/bookInvite"
    const val CORE_ACCOUNT_INFO = "$HOST_CORE/accountInfo"
    const val CORE_ACCOUNT_DETAIL = "$HOST_CORE/accountDetail"
    const val CORE_ACCOUNT_CRUD = "$HOST_CORE/accountCrud"
    const val CORE_ACCOUNT_SELECT = "$HOST_CORE/accountSelect"
    const val CORE_ACCOUNT_SELECT1 = "$HOST_CORE/accountSelect1"
    const val CORE_ACCOUNT_ICON_SELECT = "$HOST_CORE/accountIconSelect"
    const val CORE_BILL_SEARCH = "$HOST_CORE/billSearch"
    const val CORE_BILL_IMAGE_CURD = "$HOST_CORE/billImageCrud"
    const val CORE_BILL_CURD = "$HOST_CORE/billCrud"
    const val CORE_BILL_DETAIL = "$HOST_CORE/billDetail"
    const val CORE_BILL_LIST = "$HOST_CORE/billList"
    const val CORE_BILL_ALBUM = "$HOST_CORE/billAlbum"
    const val CORE_PRICE_CALCULATE = "$HOST_CORE/priceCalculate"
    const val CORE_ICON_SELECT = "$HOST_CORE/iconSelect"
    const val CORE_BILL_CYCLE = "$HOST_CORE/billCycle"
    const val CORE_BILL_CYCLE_CRUD = "$HOST_CORE/billCycleCrud"
    const val CORE_BILL_CYCLE_REPEAT_COUNT = "$HOST_CORE/billCycleRepeatCount"
    const val CORE_BILL_CYCLE_NOTE = "$HOST_CORE/billCycleNote"
    const val CORE_SYNC_LOG = "$HOST_CORE/syncLog"
    const val CORE_AI_BILL_CREATE = "$HOST_CORE/aiBillCreate"
    const val CORE_AI_BILL_CHAT = "$HOST_CORE/aiBillChat"

    private const val HOST_CUSTOM = "custom"
    const val CUSTOM_SYSTEM_SHARE = "$HOST_CUSTOM/systemShare"

    private const val HOST_IMAGE_PREVIEW = "imagePreview"
    const val IMAGE_PREVIEW_MAIN = "$HOST_IMAGE_PREVIEW/main"

    private const val HOST_TEST = "test"
    const val TEST_TEST1 = "$HOST_TEST/test1"

}