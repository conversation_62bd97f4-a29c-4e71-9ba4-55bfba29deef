[versions]
compileSdk = "35"
targetSdk = "35"
minSdk = "21"
xiaojinzi-android-support = "1.0-beta225"
xiaojinzi-android-module = "1.0-beta51"
# xiaojinzi-android-module = "1a1a77bb69"
# xiaojinzi-android-reactive = "v1.0.0-rc1"
xiaojinzi-android-reactive = "v1.0.1"
compose = "1.7.5"
lifecycle = "2.8.7"
lottie = "6.6.0"
kcomponent = "2.0.0"
# kcomponent = "8bc16660a9"
kotlin = "2.0.21"
ksp = "2.0.21-1.0.26"
agp = "8.10.1"
junit = "4.13.2"
androidx-test-ext-junit = "1.2.1"
espresso-core = "3.6.1"
material = "1.12.0"
retrofit = "2.11.0"
room = "2.6.1"
hilt = "2.51.1"
glance = "1.1.1"
work-manager = "2.10.0"
koin = "4.0.0"
supabase = "3.0.2"

[libraries]
kotlin-parcelize-runtime = { group = "org.jetbrains.kotlin", name = "kotlin-parcelize-runtime", version.ref = "kotlin" }
androidx-annotation = { group = "androidx.annotation", name = "annotation", version = "1.9.1" }
androidx-core = { group = "androidx.core", name = "core-ktx", version = "1.15.0" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version = "1.7.0" }
androidx-work-manager-ktx = { group = "androidx.work", name = "work-runtime-ktx", version.ref = "work-manager" }

xiaojinzi-android-support-ktx = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-ktx", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-ktx-retrofit = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-ktx-retrofit", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-bean = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-bean", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-annotation = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-annotation", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-activitystack = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-activity-stack", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-init = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-init", version.ref = "xiaojinzi-android-support" }
xiaojinzi-android-support-compose = { group = "com.github.xiaojinzi123.AndroidSupport", name = "lib-compose", version.ref = "xiaojinzi-android-support" }

xiaojinzi-android-module-storage = { group = "com.github.xiaojinzi123.AndroidModule", name = "module-storage", version.ref = "xiaojinzi-android-module" }
xiaojinzi-android-module-ffmpeg = { group = "com.github.xiaojinzi123.AndroidModule", name = "module-ffmpeg", version.ref = "xiaojinzi-android-module" }
xiaojinzi-android-module-ali-oss = { group = "com.github.xiaojinzi123.AndroidModule", name = "module-ali-oss", version.ref = "xiaojinzi-android-module" }
xiaojinzi-android-module-ali-pay = { group = "com.github.xiaojinzi123.AndroidModule", name = "module-ali-pay", version.ref = "xiaojinzi-android-module" }
xiaojinzi-android-module-wx-sdk = { group = "com.github.xiaojinzi123.AndroidModule", name = "module-wx-sdk", version.ref = "xiaojinzi-android-module" }

xiaojinzi-android-reactive-core = { group = "com.github.xiaojinzi123.AndroidReactive", name = "reactive-core", version.ref = "xiaojinzi-android-reactive" }
xiaojinzi-android-reactive-template = { group = "com.github.xiaojinzi123.AndroidReactive", name = "reactive-template", version.ref = "xiaojinzi-android-reactive" }
xiaojinzi-android-reactive-template-compose = { group = "com.github.xiaojinzi123.AndroidReactive", name = "reactive-template-compose", version.ref = "xiaojinzi-android-reactive" }
compose-runtime = { group = "androidx.compose.runtime", name = "runtime", version.ref = "compose" }
compose-runtime-android = { group = "androidx.compose.runtime", name = "runtime-android", version.ref = "compose" }
compose-ui-android = { group = "androidx.compose.ui", name = "ui-android", version.ref = "compose" }
compose-foundation-android = { group = "androidx.compose.foundation", name = "foundation-android", version.ref = "compose" }
compose-foundation-layout-android = { group = "androidx.compose.foundation", name = "foundation-layout-android", version.ref = "compose" }
compose-material-android = { group = "androidx.compose.material", name = "material-android", version.ref = "compose" }
compose-material3 = { group = "androidx.compose.material3", name = "material3", version = "1.3.1" }
glance-appwidget = { group = "androidx.glance", name = "glance-appwidget", version.ref = "glance" }
glance-material3 = { group = "androidx.glance", name = "glance-material3", version.ref = "glance" }
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
lifecycle-viewmodel-compose = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-compose", version.ref = "lifecycle" }
lottie-compose = { group = "com.airbnb.android", name = "lottie-compose", version.ref = "lottie" }
kcomponent-core = { group = "com.github.xiaojinzi123.KComponent", name = "kcomponent", version.ref = "kcomponent" }
kcomponent-compiler = { group = "com.github.xiaojinzi123.KComponent", name = "kcomponent-compiler", version.ref = "kcomponent" }
kcomponent-gradleplugin = { group = "com.github.xiaojinzi123.KComponent", name = "kcomponent-plugin", version.ref = "kcomponent" }
okhttp3 = { group = "com.squareup.okhttp3", name = "okhttp", version = "4.12.0" }
retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }

gradle-kotlin-dsl-plugin = { group = "org.gradle.kotlin", name = "gradle-kotlin-dsl-plugins", version = "4.5.0" }
android-gradlePlugin = { group = "com.android.tools.build", name = "gradle", version.ref = "agp" }
kotlin-gradlePlugin-api = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin-api", version.ref = "kotlin" }
kotlin-gradlePlugin = { group = "org.jetbrains.kotlin", name = "kotlin-gradle-plugin", version.ref = "kotlin" }
ksp-gradlePlugin = { group = "com.google.devtools.ksp", name = "symbol-processing-gradle-plugin", version.ref = "ksp" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-test-ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "androidx-test-ext-junit" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espresso-core" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hilt" }
hilt = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
koin-android = { group = "io.insert-koin", name = "koin-android", version.ref = "koin" }
supabase-bom = { group = "io.github.jan-tennert.supabase", name = "bom", version.ref = "supabase" }
supabase-postgrest = { group = "io.github.jan-tennert.supabase", name = "postgrest-kt", version.ref = "supabase" }
supabase-auth = { group = "io.github.jan-tennert.supabase", name = "auth-kt", version.ref = "supabase" }
supabase-compose-auth = { group = "io.github.jan-tennert.supabase", name = "compose-auth", version.ref = "supabase" }
supabase-compose-auth-ui = { group = "io.github.jan-tennert.supabase", name = "compose-auth-ui", version.ref = "supabase" }
ktor-client-android = { group = "io.ktor", name = "ktor-client-android", version = "3.1.1" }

[plugins]
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }