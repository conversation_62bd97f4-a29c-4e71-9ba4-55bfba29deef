package com.xiaojinzi.tally.lib.res.model.tally

import androidx.annotation.Keep
import com.xiaojinzi.support.annotation.ModelForNetwork
import com.xiaojinzi.support.ktx.newUUid
import com.xiaojinzi.tally.lib.res.model.user.UserInfoDto

@Keep
@ModelForNetwork
data class TallyRemoteCategoryRes(
    val id: String,
    val userId: String,
    val bookId: String,
    val parentId: String?,
    val type: String?,
    val name: String?,
    val iconName: String?,
    val sort: Long,
    val timeCreate: Long,
    val timeModify: Long,
    val isDeleted: Boolean,
) {

    companion object {

        const val CATEGORY_TYPE_INCOME = TallyCategoryDto.TYPE_INCOME
        const val CATEGORY_TYPE_SPENDING = TallyCategoryDto.TYPE_SPENDING

        fun createForOpenSource(): List<TallyRemoteCategoryRes> {

            val BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI = newUUid()
            val BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN = newUUid()
            val BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG = newUUid()
            val BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE = newUUid()
            val BOOK_NORMAL_SPENDING_REN_QING_SHI_GU = newUUid()
            val BOOK_NORMAL_SPENDING_JIAN_KANG_YI_LIAO = newUUid()
            val BOOK_NORMAL_SPENDING_JU_JIA_SHENG_HUO = newUUid()
            val BOOK_NORMAL_SPENDING_QI_TA = newUUid()
            val CURRENT_TIME = System.currentTimeMillis()

            return listOf(
                TallyRemoteCategoryRes(
                    id = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "shopping1",
                    name = "购物消费",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "nightstand1",
                    name = "日常家居",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "lipstick1",
                    name = "个护美妆",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "airpods1",
                    name = "手机数码",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "money1",
                    name = "虚拟充值",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "washingMachine1",
                    name = "生活电器",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "watch1",
                    name = "配饰腕表",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "babyBottle1",
                    name = "母婴玩具",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "cardigan1",
                    name = "服饰运动",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "dog1",
                    name = "宠物用品",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "printer1",
                    name = "办公用品",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = UserInfoDto.TEST_ID,
                    bookId = TallyBookDto.TEST_ID,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = BOOK_NORMAL_SPENDING_GOU_WU_XIAO_FEI,
                    iconName = "brush1",
                    name = "装修装饰",
                    sort = 1,
                    timeCreate = CURRENT_TIME,
                    timeModify = CURRENT_TIME,
                    isDeleted = false,
                ),
            ) + // 占位
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "chopsticksFork1",
                            name = "食品餐饮",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "bread1",
                            name = "早餐",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "rice1",
                            name = "午餐",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "noodles1",
                            name = "晚餐",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "cocktail1",
                            name = "饮料酒水",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "candy1",
                            name = "休闲零食",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "croissant1",
                            name = "生鲜食品",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "cookingPot1",
                            name = "请客吃饭",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_SHI_PIN_CAN_YIN,
                            iconName = "bottle1",
                            name = "粮油调味",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "road1",
                            name = "出行交通",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            iconName = "taxi1",
                            name = "打车",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            iconName = "subway1",
                            name = "公共交通",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            iconName = "parking1",
                            name = "停车费",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            iconName = "gasStation1",
                            name = "加油",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_CHU_XING_JIAO_TONG,
                            iconName = "repair1",
                            name = "维修保养",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "gamePad1",
                            name = "休闲娱乐",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE,
                            iconName = "microphone1",
                            name = "唱歌",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE,
                            iconName = "movie1",
                            name = "电影",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE,
                            iconName = "dumbbell1",
                            name = "运动健身",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_XIU_XIAN_YU_LE,
                            iconName = "chess1",
                            name = "棋牌桌游",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_REN_QING_SHI_GU,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "gift1",
                            name = "人情世故",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_REN_QING_SHI_GU,
                            iconName = "gift2",
                            name = "孝敬长辈",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_REN_QING_SHI_GU,
                            iconName = "redPacket1",
                            name = "红包",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_JIAN_KANG_YI_LIAO,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "stethoscope1",
                            name = "健康医疗",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_JIAN_KANG_YI_LIAO,
                            iconName = "hospital1",
                            name = "医院",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_JU_JIA_SHENG_HUO,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "house1",
                            name = "居家生活",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = BOOK_NORMAL_SPENDING_JU_JIA_SHENG_HUO,
                            iconName = "waterElectricityCharge1",
                            name = "水电费",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = BOOK_NORMAL_SPENDING_QI_TA,
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_SPENDING,
                            parentId = null,
                            iconName = "more2",
                            name = "其他",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    ) +
                    listOf(
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_INCOME,
                            parentId = null,
                            iconName = "wage1",
                            name = "工资",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_INCOME,
                            parentId = null,
                            iconName = "bonus1",
                            name = "奖金",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_INCOME,
                            parentId = null,
                            iconName = "partTimeJob1",
                            name = "兼职外快",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                        TallyRemoteCategoryRes(
                            id = newUUid(),
                            userId = UserInfoDto.TEST_ID,
                            bookId = TallyBookDto.TEST_ID,
                            type = CATEGORY_TYPE_INCOME,
                            parentId = null,
                            iconName = "income1",
                            name = "借入",
                            sort = 1,
                            timeCreate = CURRENT_TIME,
                            timeModify = CURRENT_TIME,
                            isDeleted = false,
                        ),
                    )

        }

        /**
         * 为开源版本创建类别数据，支持动态用户ID和账本ID
         */
        fun createForOpenSourceWithUserId(userId: String): List<TallyRemoteCategoryRes> {
            val bookId = userId // 直接使用用户ID作为账本ID，与TallyBookModel保持一致
            val currentTime = System.currentTimeMillis()

            // 支出类别
            val spendingCategories = listOf(
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "shopping1",
                    name = "购物消费",
                    sort = 1,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "chopsticksFork1",
                    name = "食品餐饮",
                    sort = 2,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "car1",
                    name = "出行交通",
                    sort = 3,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "gamepad1",
                    name = "休闲娱乐",
                    sort = 4,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "medical1",
                    name = "健康医疗",
                    sort = 5,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "house1",
                    name = "居家生活",
                    sort = 6,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_SPENDING,
                    parentId = null,
                    iconName = "other1",
                    name = "其他支出",
                    sort = 7,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                )
            )

            // 收入类别
            val incomeCategories = listOf(
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_INCOME,
                    parentId = null,
                    iconName = "salary1",
                    name = "工资收入",
                    sort = 1,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_INCOME,
                    parentId = null,
                    iconName = "bonus1",
                    name = "奖金补贴",
                    sort = 2,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_INCOME,
                    parentId = null,
                    iconName = "investment1",
                    name = "投资收益",
                    sort = 3,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                ),
                TallyRemoteCategoryRes(
                    id = newUUid(),
                    userId = userId,
                    bookId = bookId,
                    type = CATEGORY_TYPE_INCOME,
                    parentId = null,
                    iconName = "other1",
                    name = "其他收入",
                    sort = 4,
                    timeCreate = currentTime,
                    timeModify = currentTime,
                    isDeleted = false,
                )
            )

            return spendingCategories + incomeCategories
        }
    }

}

@Keep
@ModelForNetwork
data class TallyRemoteCategoryReq(
    val id: String,
    val userId: String,
    val bookId: String,
    val parentId: String?,
    val type: String?,
    val name: String?,
    val iconName: String?,
    val sort: Long,
    val timeCreate: Long,
    val isDeleted: Boolean,
)

@Keep
data class TallyCategoryInsertDto(
    val id: String? = null,
    val userId: String,
    val bookId: String,
    val parentId: String? = null,
    val type: String?,
    val name: String?,
    val iconName: String?,
    val sort: Long = System.currentTimeMillis(),
    val timeCreate: Long = System.currentTimeMillis(),
    val timeModify: Long? = null,
    val isDeleted: Boolean = false,
    val isSync: Boolean = false,
)

@Keep
data class TallyCategoryDto(
    val id: String,
    val userId: String,
    val bookId: String,
    val parentId: String? = null,
    val type: TallyCategoryType,
    val name: String?,
    val iconName: String?,
    val sort: Long,
    val timeCreate: Long,
    val timeModify: Long?,
    val isDeleted: Boolean,
    val isSync: Boolean,
) {

    companion object {

        const val TYPE_INCOME = "income"
        const val TYPE_SPENDING = "spending"

        enum class TallyCategoryType(
            val moneyTransform: Int,
            val dbStr: String,
        ) {
            UNKNOW(
                moneyTransform = -1,
                dbStr = "",
            ),
            INCOME(
                moneyTransform = 1,
                dbStr = TYPE_INCOME,
            ),
            SPENDING(
                moneyTransform = -1,
                dbStr = TYPE_SPENDING,
            );

            companion object {
                fun fromDbStr(dbStr: String?): TallyCategoryType {
                    return when (dbStr) {
                        TYPE_INCOME -> INCOME
                        TYPE_SPENDING -> SPENDING
                        else -> UNKNOW
                    }
                }
            }
        }

    }

    val getAdapter: TallyCategoryDto?
        get() = if (isDeleted) {
            null
        } else {
            this
        }

}

fun TallyRemoteCategoryRes.toInsertDto(): TallyCategoryInsertDto = TallyCategoryInsertDto(
    id = this.id,
    userId = this.userId,
    bookId = this.bookId,
    name = this.name,
    type = this.type,
    parentId = this.parentId,
    iconName = this.iconName,
    sort = this.sort,
    timeCreate = this.timeCreate,
    timeModify = this.timeModify,
    isDeleted = this.isDeleted,
    isSync = true,
)

fun TallyCategoryDto.toInsertDto() = TallyCategoryInsertDto(
    id = id,
    userId = userId,
    bookId = bookId,
    parentId = parentId,
    type = type.dbStr,
    name = name,
    iconName = iconName,
    sort = sort,
    timeCreate = timeCreate,
    timeModify = timeModify,
    isDeleted = isDeleted,
    isSync = isSync,
)

fun TallyRemoteCategoryRes.toDto() = TallyCategoryDto(
    id = id,
    userId = userId,
    bookId = bookId,
    parentId = parentId,
    type = TallyCategoryDto.Companion.TallyCategoryType.fromDbStr(type),
    name = name,
    iconName = iconName,
    sort = sort,
    timeCreate = timeCreate,
    timeModify = timeModify,
    isDeleted = isDeleted,
    isSync = true,
)