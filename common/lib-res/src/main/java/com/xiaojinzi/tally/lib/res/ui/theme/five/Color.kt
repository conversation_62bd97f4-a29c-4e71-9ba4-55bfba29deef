package com.xiaojinzi.tally.lib.res.ui.theme.five
import androidx.compose.ui.graphics.Color

val TitianRed_md_theme_light_primary = Color(0xFF984711)
val TitianRed_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val TitianRed_md_theme_light_primaryContainer = Color(0xFFFFDBCA)
val TitianRed_md_theme_light_onPrimaryContainer = Color(0xFF331100)
val TitianRed_md_theme_light_secondary = Color(0xFF765848)
val TitianRed_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val TitianRed_md_theme_light_secondaryContainer = Color(0xFFFFDBCA)
val TitianRed_md_theme_light_onSecondaryContainer = Color(0xFF2B160A)
val TitianRed_md_theme_light_tertiary = Color(0xFF646032)
val TitianRed_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val TitianRed_md_theme_light_tertiaryContainer = Color(0xFFEBE4AA)
val TitianRed_md_theme_light_onTertiaryContainer = Color(0xFF1E1C00)
val TitianRed_md_theme_light_error = Color(0xFFBA1A1A)
val TitianRed_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val TitianRed_md_theme_light_onError = Color(0xFFFFFFFF)
val TitianRed_md_theme_light_onErrorContainer = Color(0xFF410002)
val TitianRed_md_theme_light_background = Color(0xFFFFFBFF)
val TitianRed_md_theme_light_onBackground = Color(0xFF201A17)
val TitianRed_md_theme_light_surface = Color(0xFFFFFBFF)
val TitianRed_md_theme_light_onSurface = Color(0xFF201A17)
val TitianRed_md_theme_light_surfaceVariant = Color(0xFFF4DED4)
val TitianRed_md_theme_light_onSurfaceVariant = Color(0xFF52443D)
val TitianRed_md_theme_light_outline = Color(0xFF85746B)
val TitianRed_md_theme_light_inverseOnSurface = Color(0xFFFBEEE9)
val TitianRed_md_theme_light_inverseSurface = Color(0xFF362F2C)
val TitianRed_md_theme_light_inversePrimary = Color(0xFFFFB68F)
val TitianRed_md_theme_light_shadow = Color(0xFF000000)
val TitianRed_md_theme_light_surfaceTint = Color(0xFF984711)
val TitianRed_md_theme_light_outlineVariant = Color(0xFFD7C2B9)
val TitianRed_md_theme_light_scrim = Color(0xFF000000)

val TitianRed_md_theme_dark_primary = Color(0xFFFFB68F)
val TitianRed_md_theme_dark_onPrimary = Color(0xFF542100)
val TitianRed_md_theme_dark_primaryContainer = Color(0xFF773200)
val TitianRed_md_theme_dark_onPrimaryContainer = Color(0xFFFFDBCA)
val TitianRed_md_theme_dark_secondary = Color(0xFFE6BEAB)
val TitianRed_md_theme_dark_onSecondary = Color(0xFF432B1D)
val TitianRed_md_theme_dark_secondaryContainer = Color(0xFF5C4132)
val TitianRed_md_theme_dark_onSecondaryContainer = Color(0xFFFFDBCA)
val TitianRed_md_theme_dark_tertiary = Color(0xFFCEC891)
val TitianRed_md_theme_dark_onTertiary = Color(0xFF343108)
val TitianRed_md_theme_dark_tertiaryContainer = Color(0xFF4B481D)
val TitianRed_md_theme_dark_onTertiaryContainer = Color(0xFFEBE4AA)
val TitianRed_md_theme_dark_error = Color(0xFFFFB4AB)
val TitianRed_md_theme_dark_errorContainer = Color(0xFF93000A)
val TitianRed_md_theme_dark_onError = Color(0xFF690005)
val TitianRed_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val TitianRed_md_theme_dark_background = Color(0xFF201A17)
val TitianRed_md_theme_dark_onBackground = Color(0xFFECE0DB)
val TitianRed_md_theme_dark_surface = Color(0xFF201A17)
val TitianRed_md_theme_dark_onSurface = Color(0xFFECE0DB)
val TitianRed_md_theme_dark_surfaceVariant = Color(0xFF52443D)
val TitianRed_md_theme_dark_onSurfaceVariant = Color(0xFFD7C2B9)
val TitianRed_md_theme_dark_outline = Color(0xFF9F8D84)
val TitianRed_md_theme_dark_inverseOnSurface = Color(0xFF201A17)
val TitianRed_md_theme_dark_inverseSurface = Color(0xFFECE0DB)
val TitianRed_md_theme_dark_inversePrimary = Color(0xFF984711)
val TitianRed_md_theme_dark_shadow = Color(0xFF000000)
val TitianRed_md_theme_dark_surfaceTint = Color(0xFFFFB68F)
val TitianRed_md_theme_dark_outlineVariant = Color(0xFF52443D)
val TitianRed_md_theme_dark_scrim = Color(0xFF000000)


val TitianRed_seed = Color(0xFFB05923)
