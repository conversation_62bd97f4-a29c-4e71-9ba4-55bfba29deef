package com.xiaojinzi.tally.lib.res.ui.theme.three
import androidx.compose.ui.graphics.Color

val OliveGreen_md_theme_light_primary = Color(0xFF596400)
val OliveGreen_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val OliveGreen_md_theme_light_primaryContainer = Color(0xFFDDEB76)
val OliveGreen_md_theme_light_onPrimaryContainer = Color(0xFF1A1E00)
val OliveGreen_md_theme_light_secondary = Color(0xFF5E6145)
val OliveGreen_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val OliveGreen_md_theme_light_secondaryContainer = Color(0xFFE3E5C2)
val OliveGreen_md_theme_light_onSecondaryContainer = Color(0xFF1A1D07)
val OliveGreen_md_theme_light_tertiary = Color(0xFF3B665B)
val OliveGreen_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val OliveGreen_md_theme_light_tertiaryContainer = Color(0xFFBEECDD)
val OliveGreen_md_theme_light_onTertiaryContainer = Color(0xFF002019)
val OliveGreen_md_theme_light_error = Color(0xFFBA1A1A)
val OliveGreen_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val OliveGreen_md_theme_light_onError = Color(0xFFFFFFFF)
val OliveGreen_md_theme_light_onErrorContainer = Color(0xFF410002)
val OliveGreen_md_theme_light_background = Color(0xFFFFFCF3)
val OliveGreen_md_theme_light_onBackground = Color(0xFF1C1C17)
val OliveGreen_md_theme_light_surface = Color(0xFFFFFCF3)
val OliveGreen_md_theme_light_onSurface = Color(0xFF1C1C17)
val OliveGreen_md_theme_light_surfaceVariant = Color(0xFFE4E3D2)
val OliveGreen_md_theme_light_onSurfaceVariant = Color(0xFF47483B)
val OliveGreen_md_theme_light_outline = Color(0xFF78786A)
val OliveGreen_md_theme_light_inverseOnSurface = Color(0xFFF3F1E8)
val OliveGreen_md_theme_light_inverseSurface = Color(0xFF31312B)
val OliveGreen_md_theme_light_inversePrimary = Color(0xFFC1CF5D)
val OliveGreen_md_theme_light_shadow = Color(0xFF000000)
val OliveGreen_md_theme_light_surfaceTint = Color(0xFF596400)
val OliveGreen_md_theme_light_outlineVariant = Color(0xFFC8C7B7)
val OliveGreen_md_theme_light_scrim = Color(0xFF000000)

val OliveGreen_md_theme_dark_primary = Color(0xFFC1CF5D)
val OliveGreen_md_theme_dark_onPrimary = Color(0xFF2E3400)
val OliveGreen_md_theme_dark_primaryContainer = Color(0xFF434B00)
val OliveGreen_md_theme_dark_onPrimaryContainer = Color(0xFFDDEB76)
val OliveGreen_md_theme_dark_secondary = Color(0xFFC7C9A7)
val OliveGreen_md_theme_dark_onSecondary = Color(0xFF2F321A)
val OliveGreen_md_theme_dark_secondaryContainer = Color(0xFF46492F)
val OliveGreen_md_theme_dark_onSecondaryContainer = Color(0xFFE3E5C2)
val OliveGreen_md_theme_dark_tertiary = Color(0xFFA2D0C2)
val OliveGreen_md_theme_dark_onTertiary = Color(0xFF06372D)
val OliveGreen_md_theme_dark_tertiaryContainer = Color(0xFF224E43)
val OliveGreen_md_theme_dark_onTertiaryContainer = Color(0xFFBEECDD)
val OliveGreen_md_theme_dark_error = Color(0xFFFFB4AB)
val OliveGreen_md_theme_dark_errorContainer = Color(0xFF93000A)
val OliveGreen_md_theme_dark_onError = Color(0xFF690005)
val OliveGreen_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val OliveGreen_md_theme_dark_background = Color(0xFF1C1C17)
val OliveGreen_md_theme_dark_onBackground = Color(0xFFE5E2DA)
val OliveGreen_md_theme_dark_surface = Color(0xFF1C1C17)
val OliveGreen_md_theme_dark_onSurface = Color(0xFFE5E2DA)
val OliveGreen_md_theme_dark_surfaceVariant = Color(0xFF47483B)
val OliveGreen_md_theme_dark_onSurfaceVariant = Color(0xFFC8C7B7)
val OliveGreen_md_theme_dark_outline = Color(0xFF929283)
val OliveGreen_md_theme_dark_inverseOnSurface = Color(0xFF1C1C17)
val OliveGreen_md_theme_dark_inverseSurface = Color(0xFFE5E2DA)
val OliveGreen_md_theme_dark_inversePrimary = Color(0xFF596400)
val OliveGreen_md_theme_dark_shadow = Color(0xFF000000)
val OliveGreen_md_theme_dark_surfaceTint = Color(0xFFC1CF5D)
val OliveGreen_md_theme_dark_outlineVariant = Color(0xFF47483B)
val OliveGreen_md_theme_dark_scrim = Color(0xFF000000)


val OliveGreen_seed = Color(0xFF89962A)
