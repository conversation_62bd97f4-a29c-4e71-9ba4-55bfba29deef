package com.xiaojinzi.tally.lib.res.model.support

@Retention(
    value = AnnotationRetention.SOURCE
)
@Target(
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.PROPERTY_SETTER,
    AnnotationTarget.FIELD,
    AnnotationTarget.PROPERTY,
)
annotation class MoneyFenAnno

@Retention(
    value = AnnotationRetention.SOURCE
)
@Target(
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.PROPERTY_SETTER,
    AnnotationTarget.FIELD,
    AnnotationTarget.PROPERTY,
)
annotation class MoneyYuanAnno