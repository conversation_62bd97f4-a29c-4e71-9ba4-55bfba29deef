package com.xiaojinzi.tally.lib.res.ui.theme.two
import androidx.compose.ui.graphics.Color

val KleinBlue_md_theme_light_primary = Color(0xFF002FA7)
val KleinBlue_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val KleinBlue_md_theme_light_primaryContainer = Color(0xFFDDE1FF)
val KleinBlue_md_theme_light_onPrimaryContainer = Color(0xFF001355)
val KleinBlue_md_theme_light_secondary = Color(0xFF5A5D72)
val KleinBlue_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val KleinBlue_md_theme_light_secondaryContainer = Color(0xFFDFE1F9)
val KleinBlue_md_theme_light_onSecondaryContainer = Color(0xFF171B2C)
val KleinBlue_md_theme_light_tertiary = Color(0xFF76546E)
val KleinBlue_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val KleinBlue_md_theme_light_tertiaryContainer = Color(0xFFFFD7F3)
val Klein<PERSON>lue_md_theme_light_onTertiaryContainer = Color(0xFF2C1229)
val KleinBlue_md_theme_light_error = Color(0xFFBA1A1A)
val KleinBlue_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val KleinBlue_md_theme_light_onError = Color(0xFFFFFFFF)
val KleinBlue_md_theme_light_onErrorContainer = Color(0xFF410002)
val KleinBlue_md_theme_light_background = Color(0xFFFEFBFF)
val KleinBlue_md_theme_light_onBackground = Color(0xFF1B1B1F)
val KleinBlue_md_theme_light_surface = Color(0xFFFEFBFF)
val KleinBlue_md_theme_light_onSurface = Color(0xFF1B1B1F)
val KleinBlue_md_theme_light_surfaceVariant = Color(0xFFE2E1EC)
val KleinBlue_md_theme_light_onSurfaceVariant = Color(0xFF45464F)
val KleinBlue_md_theme_light_outline = Color(0xFF767680)
val KleinBlue_md_theme_light_inverseOnSurface = Color(0xFFF2F0F4)
val KleinBlue_md_theme_light_inverseSurface = Color(0xFF303034)
val KleinBlue_md_theme_light_inversePrimary = Color(0xFFB8C3FF)
val KleinBlue_md_theme_light_shadow = Color(0xFF000000)
val KleinBlue_md_theme_light_surfaceTint = Color(0xFF3654C8)
val KleinBlue_md_theme_light_outlineVariant = Color(0xFFC6C5D0)
val KleinBlue_md_theme_light_scrim = Color(0xFF000000)

val KleinBlue_md_theme_dark_primary = Color(0xFFB8C3FF)
val KleinBlue_md_theme_dark_onPrimary = Color(0xFF002487)
val KleinBlue_md_theme_dark_primaryContainer = Color(0xFF1639AF)
val KleinBlue_md_theme_dark_onPrimaryContainer = Color(0xFFDDE1FF)
val KleinBlue_md_theme_dark_secondary = Color(0xFFC3C5DD)
val KleinBlue_md_theme_dark_onSecondary = Color(0xFF2C2F42)
val KleinBlue_md_theme_dark_secondaryContainer = Color(0xFF424659)
val KleinBlue_md_theme_dark_onSecondaryContainer = Color(0xFFDFE1F9)
val KleinBlue_md_theme_dark_tertiary = Color(0xFFE4BAD9)
val KleinBlue_md_theme_dark_onTertiary = Color(0xFF44273F)
val KleinBlue_md_theme_dark_tertiaryContainer = Color(0xFF5C3D56)
val KleinBlue_md_theme_dark_onTertiaryContainer = Color(0xFFFFD7F3)
val KleinBlue_md_theme_dark_error = Color(0xFFFFB4AB)
val KleinBlue_md_theme_dark_errorContainer = Color(0xFF93000A)
val KleinBlue_md_theme_dark_onError = Color(0xFF690005)
val KleinBlue_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val KleinBlue_md_theme_dark_background = Color(0xFF1B1B1F)
val KleinBlue_md_theme_dark_onBackground = Color(0xFFE4E1E6)
val KleinBlue_md_theme_dark_surface = Color(0xFF1B1B1F)
val KleinBlue_md_theme_dark_onSurface = Color(0xFFE4E1E6)
val KleinBlue_md_theme_dark_surfaceVariant = Color(0xFF45464F)
val KleinBlue_md_theme_dark_onSurfaceVariant = Color(0xFFC6C5D0)
val KleinBlue_md_theme_dark_outline = Color(0xFF90909A)
val KleinBlue_md_theme_dark_inverseOnSurface = Color(0xFF1B1B1F)
val KleinBlue_md_theme_dark_inverseSurface = Color(0xFFE4E1E6)
val KleinBlue_md_theme_dark_inversePrimary = Color(0xFF3654C8)
val KleinBlue_md_theme_dark_shadow = Color(0xFF000000)
val KleinBlue_md_theme_dark_surfaceTint = Color(0xFFB8C3FF)
val KleinBlue_md_theme_dark_outlineVariant = Color(0xFF45464F)
val KleinBlue_md_theme_dark_scrim = Color(0xFF000000)


val KleinBlue_seed = Color(0xFF002EA6)
