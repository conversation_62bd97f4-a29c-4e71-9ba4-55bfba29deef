package com.xiaojinzi.tally.lib.res.ui.theme.custom1
import androidx.compose.ui.graphics.Color

val Custom1_md_theme_light_primary = Color(0xFFFF7200)
val Custom1_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val Custom1_md_theme_light_primaryContainer = Color(0xFFFFE7D6)
val Custom1_md_theme_light_onPrimaryContainer = Color(0xFF410000)
val Custom1_md_theme_light_secondary = Color(0xFFFFA733)
val Custom1_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val Custom1_md_theme_light_secondaryContainer = Color(0xFFFFEFD2)
val Custom1_md_theme_light_onSecondaryContainer = Color(0xFF2C1512)
val Custom1_md_theme_light_tertiary = Color(0xFFFF9400)
val Custom1_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val Custom1_md_theme_light_tertiaryContainer = Color(0xFFFFD9B3)
val Custom1_md_theme_light_onTertiaryContainer = Color(0xFF251A00)
val Custom1_md_theme_light_error = Color(0xFFFF3333)
val Custom1_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val Custom1_md_theme_light_onError = Color(0xFFFFFFFF)
val Custom1_md_theme_light_onErrorContainer = Color(0xFF410002)
val Custom1_md_theme_light_background = Color(0xFFFFFBFF)
val Custom1_md_theme_light_onBackground = Color(0xFF201A19)
val Custom1_md_theme_light_surface = Color(0xFFFFFBFF)
val Custom1_md_theme_light_onSurface = Color(0xFF201A19)
val Custom1_md_theme_light_surfaceVariant = Color(0xFFF5DDDA)
val Custom1_md_theme_light_onSurfaceVariant = Color(0xFF534341)
val Custom1_md_theme_light_outline = Color(0xFF857370)
val Custom1_md_theme_light_inverseOnSurface = Color(0xFFFBEEEC)
val Custom1_md_theme_light_inverseSurface = Color(0xFF362F2E)
val Custom1_md_theme_light_inversePrimary = Color(0xFFFFB4A8)
val Custom1_md_theme_light_shadow = Color(0xFF000000)
val Custom1_md_theme_light_surfaceTint = Color(0xFFC00000)
val Custom1_md_theme_light_outlineVariant = Color(0xFFD8C2BE)
val Custom1_md_theme_light_scrim = Color(0xFF000000)

val Custom1_md_theme_dark_primary = Color(0xFF804000)
val Custom1_md_theme_dark_onPrimary = Color(0xFF690000)
val Custom1_md_theme_dark_primaryContainer = Color(0xFFC08060)
val Custom1_md_theme_dark_onPrimaryContainer = Color(0xFFFFDAD4)
val Custom1_md_theme_dark_secondary = Color(0xFF805522)
val Custom1_md_theme_dark_onSecondary = Color(0xFF442925)
val Custom1_md_theme_dark_secondaryContainer = Color(0xFFA07050)
val Custom1_md_theme_dark_onSecondaryContainer = Color(0xFFFFDAD4)
val Custom1_md_theme_dark_tertiary = Color(0xFF603000)
val Custom1_md_theme_dark_onTertiary = Color(0xFF402000)
val Custom1_md_theme_dark_tertiaryContainer = Color(0xFF906040)
val Custom1_md_theme_dark_onTertiaryContainer = Color(0xFFFBDFA6)
val Custom1_md_theme_dark_error = Color(0xFF882222)
val Custom1_md_theme_dark_errorContainer = Color(0xFF93000A)
val Custom1_md_theme_dark_onError = Color(0xFF690005)
val Custom1_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val Custom1_md_theme_dark_background = Color(0xFF201A19)
val Custom1_md_theme_dark_onBackground = Color(0xFFEDE0DD)
val Custom1_md_theme_dark_surface = Color(0xFF201A19)
val Custom1_md_theme_dark_onSurface = Color(0xFFEDE0DD)
val Custom1_md_theme_dark_surfaceVariant = Color(0xFF534341)
val Custom1_md_theme_dark_onSurfaceVariant = Color(0xFFD8C2BE)
val Custom1_md_theme_dark_outline = Color(0xFFA08C89)
val Custom1_md_theme_dark_inverseOnSurface = Color(0xFF201A19)
val Custom1_md_theme_dark_inverseSurface = Color(0xFFEDE0DD)
val Custom1_md_theme_dark_inversePrimary = Color(0xFFC00000)
val Custom1_md_theme_dark_shadow = Color(0xFF000000)
val Custom1_md_theme_dark_surfaceTint = Color(0xFFFFB4A8)
val Custom1_md_theme_dark_outlineVariant = Color(0xFF534341)
val Custom1_md_theme_dark_scrim = Color(0xFF000000)


val Custom1_seed = Color(0xFFE60000)
