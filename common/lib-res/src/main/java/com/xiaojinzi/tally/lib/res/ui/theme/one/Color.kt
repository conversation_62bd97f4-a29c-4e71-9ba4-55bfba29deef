package com.xiaojinzi.tally.lib.res.ui.theme.one
import androidx.compose.ui.graphics.Color

val ChinaRed_md_theme_light_primary = Color(0xFFE60000)
val ChinaRed_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val ChinaRed_md_theme_light_primaryContainer = Color(0xFFFFDAD4)
val ChinaRed_md_theme_light_onPrimaryContainer = Color(0xFF410000)
val ChinaRed_md_theme_light_secondary = Color(0xFF775651)
val ChinaRed_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val ChinaRed_md_theme_light_secondaryContainer = Color(0xFFFFDAD4)
val ChinaRed_md_theme_light_onSecondaryContainer = Color(0xFF2C1512)
val ChinaRed_md_theme_light_tertiary = Color(0xFF705C2E)
val ChinaRed_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val ChinaRed_md_theme_light_tertiaryContainer = Color(0xFFFBDFA6)
val ChinaRed_md_theme_light_onTertiaryContainer = Color(0xFF251A00)
val ChinaRed_md_theme_light_error = Color(0xFFBA1A1A)
val ChinaRed_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val ChinaRed_md_theme_light_onError = Color(0xFFFFFFFF)
val ChinaRed_md_theme_light_onErrorContainer = Color(0xFF410002)
val ChinaRed_md_theme_light_background = Color(0xFFFFFBFF)
val ChinaRed_md_theme_light_onBackground = Color(0xFF201A19)
val ChinaRed_md_theme_light_surface = Color(0xFFFFFBFF)
val ChinaRed_md_theme_light_onSurface = Color(0xFF201A19)
val ChinaRed_md_theme_light_surfaceVariant = Color(0xFFF5DDDA)
val ChinaRed_md_theme_light_onSurfaceVariant = Color(0xFF534341)
val ChinaRed_md_theme_light_outline = Color(0xFF857370)
val ChinaRed_md_theme_light_inverseOnSurface = Color(0xFFFBEEEC)
val ChinaRed_md_theme_light_inverseSurface = Color(0xFF362F2E)
val ChinaRed_md_theme_light_inversePrimary = Color(0xFFFFB4A8)
val ChinaRed_md_theme_light_shadow = Color(0xFF000000)
val ChinaRed_md_theme_light_surfaceTint = Color(0xFFC00000)
val ChinaRed_md_theme_light_outlineVariant = Color(0xFFD8C2BE)
val ChinaRed_md_theme_light_scrim = Color(0xFF000000)

val ChinaRed_md_theme_dark_primary = Color(0xFFFFB4A8)
val ChinaRed_md_theme_dark_onPrimary = Color(0xFF690000)
val ChinaRed_md_theme_dark_primaryContainer = Color(0xFF930100)
val ChinaRed_md_theme_dark_onPrimaryContainer = Color(0xFFFFDAD4)
val ChinaRed_md_theme_dark_secondary = Color(0xFFE7BDB6)
val ChinaRed_md_theme_dark_onSecondary = Color(0xFF442925)
val ChinaRed_md_theme_dark_secondaryContainer = Color(0xFF5D3F3B)
val ChinaRed_md_theme_dark_onSecondaryContainer = Color(0xFFFFDAD4)
val ChinaRed_md_theme_dark_tertiary = Color(0xFFDEC48C)
val ChinaRed_md_theme_dark_onTertiary = Color(0xFF3E2E04)
val ChinaRed_md_theme_dark_tertiaryContainer = Color(0xFF564419)
val ChinaRed_md_theme_dark_onTertiaryContainer = Color(0xFFFBDFA6)
val ChinaRed_md_theme_dark_error = Color(0xFFFFB4AB)
val ChinaRed_md_theme_dark_errorContainer = Color(0xFF93000A)
val ChinaRed_md_theme_dark_onError = Color(0xFF690005)
val ChinaRed_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val ChinaRed_md_theme_dark_background = Color(0xFF201A19)
val ChinaRed_md_theme_dark_onBackground = Color(0xFFEDE0DD)
val ChinaRed_md_theme_dark_surface = Color(0xFF201A19)
val ChinaRed_md_theme_dark_onSurface = Color(0xFFEDE0DD)
val ChinaRed_md_theme_dark_surfaceVariant = Color(0xFF534341)
val ChinaRed_md_theme_dark_onSurfaceVariant = Color(0xFFD8C2BE)
val ChinaRed_md_theme_dark_outline = Color(0xFFA08C89)
val ChinaRed_md_theme_dark_inverseOnSurface = Color(0xFF201A19)
val ChinaRed_md_theme_dark_inverseSurface = Color(0xFFEDE0DD)
val ChinaRed_md_theme_dark_inversePrimary = Color(0xFFC00000)
val ChinaRed_md_theme_dark_shadow = Color(0xFF000000)
val ChinaRed_md_theme_dark_surfaceTint = Color(0xFFFFB4A8)
val ChinaRed_md_theme_dark_outlineVariant = Color(0xFF534341)
val ChinaRed_md_theme_dark_scrim = Color(0xFF000000)


val ChinaRed_seed = Color(0xFFE60000)
