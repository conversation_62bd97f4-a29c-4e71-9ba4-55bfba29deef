package com.xiaojinzi.tally.lib.res.ui.theme.four
import androidx.compose.ui.graphics.Color

val Schenbrunn_Yellowmd_md_theme_light_primary = Color(0xFFFBD26A)
val Sc<PERSON>brunn_Yellowmd_md_theme_light_onPrimary = Color(0xFFFFFFFF)
val Schenbrunn_Yellowmd_md_theme_light_primaryContainer = Color(0xFFFFDF95)
val Schenbrunn_Yellowmd_md_theme_light_onPrimaryContainer = Color(0xFF251A00)
val Schenbrunn_Yellowmd_md_theme_light_secondary = Color(0xFF6A5D3F)
val Schenbrunn_Yellowmd_md_theme_light_onSecondary = Color(0xFFFFFFFF)
val Schenbrunn_Yellowmd_md_theme_light_secondaryContainer = Color(0xFFF3E1BB)
val Schenbrunn_Yellowmd_md_theme_light_onSecondaryContainer = Color(0xFF231A04)
val Schenbrunn_Yellowmd_md_theme_light_tertiary = Color(0xFF486549)
val <PERSON><PERSON>brunn_Yellowmd_md_theme_light_onTertiary = Color(0xFFFFFFFF)
val Schenbrunn_Yellowmd_md_theme_light_tertiaryContainer = Color(0xFFCAEBC7)
val Schenbrunn_Yellowmd_md_theme_light_onTertiaryContainer = Color(0xFF05210A)
val Schenbrunn_Yellowmd_md_theme_light_error = Color(0xFFBA1A1A)
val Schenbrunn_Yellowmd_md_theme_light_errorContainer = Color(0xFFFFDAD6)
val Schenbrunn_Yellowmd_md_theme_light_onError = Color(0xFFFFFFFF)
val Schenbrunn_Yellowmd_md_theme_light_onErrorContainer = Color(0xFF410002)
val Schenbrunn_Yellowmd_md_theme_light_background = Color(0xFFFFFBFF)
val Schenbrunn_Yellowmd_md_theme_light_onBackground = Color(0xFF1E1B16)
val Schenbrunn_Yellowmd_md_theme_light_surface = Color(0xFFFFFBFF)
val Schenbrunn_Yellowmd_md_theme_light_onSurface = Color(0xFF1E1B16)
val Schenbrunn_Yellowmd_md_theme_light_surfaceVariant = Color(0xFFECE1CF)
val Schenbrunn_Yellowmd_md_theme_light_onSurfaceVariant = Color(0xFF4C4639)
val Schenbrunn_Yellowmd_md_theme_light_outline = Color(0xFF7E7667)
val Schenbrunn_Yellowmd_md_theme_light_inverseOnSurface = Color(0xFFF7F0E7)
val Schenbrunn_Yellowmd_md_theme_light_inverseSurface = Color(0xFF33302A)
val Schenbrunn_Yellowmd_md_theme_light_inversePrimary = Color(0xFFEEC148)
val Schenbrunn_Yellowmd_md_theme_light_shadow = Color(0xFF000000)
val Schenbrunn_Yellowmd_md_theme_light_surfaceTint = Color(0xFF765B00)
val Schenbrunn_Yellowmd_md_theme_light_outlineVariant = Color(0xFFCFC5B4)
val Schenbrunn_Yellowmd_md_theme_light_scrim = Color(0xFF000000)

val Schenbrunn_Yellowmd_md_theme_dark_primary = Color(0xFFEEC148)
val Schenbrunn_Yellowmd_md_theme_dark_onPrimary = Color(0xFF3E2E00)
val Schenbrunn_Yellowmd_md_theme_dark_primaryContainer = Color(0xFF594400)
val Schenbrunn_Yellowmd_md_theme_dark_onPrimaryContainer = Color(0xFFFFDF95)
val Schenbrunn_Yellowmd_md_theme_dark_secondary = Color(0xFFD6C5A0)
val Schenbrunn_Yellowmd_md_theme_dark_onSecondary = Color(0xFF3A2F15)
val Schenbrunn_Yellowmd_md_theme_dark_secondaryContainer = Color(0xFF51452A)
val Schenbrunn_Yellowmd_md_theme_dark_onSecondaryContainer = Color(0xFFF3E1BB)
val Schenbrunn_Yellowmd_md_theme_dark_tertiary = Color(0xFFAECFAC)
val Schenbrunn_Yellowmd_md_theme_dark_onTertiary = Color(0xFF1B361E)
val Schenbrunn_Yellowmd_md_theme_dark_tertiaryContainer = Color(0xFF314D32)
val Schenbrunn_Yellowmd_md_theme_dark_onTertiaryContainer = Color(0xFFCAEBC7)
val Schenbrunn_Yellowmd_md_theme_dark_error = Color(0xFFFFB4AB)
val Schenbrunn_Yellowmd_md_theme_dark_errorContainer = Color(0xFF93000A)
val Schenbrunn_Yellowmd_md_theme_dark_onError = Color(0xFF690005)
val Schenbrunn_Yellowmd_md_theme_dark_onErrorContainer = Color(0xFFFFDAD6)
val Schenbrunn_Yellowmd_md_theme_dark_background = Color(0xFF1E1B16)
val Schenbrunn_Yellowmd_md_theme_dark_onBackground = Color(0xFFE9E1D9)
val Schenbrunn_Yellowmd_md_theme_dark_surface = Color(0xFF1E1B16)
val Schenbrunn_Yellowmd_md_theme_dark_onSurface = Color(0xFFE9E1D9)
val Schenbrunn_Yellowmd_md_theme_dark_surfaceVariant = Color(0xFF4C4639)
val Schenbrunn_Yellowmd_md_theme_dark_onSurfaceVariant = Color(0xFFCFC5B4)
val Schenbrunn_Yellowmd_md_theme_dark_outline = Color(0xFF989080)
val Schenbrunn_Yellowmd_md_theme_dark_inverseOnSurface = Color(0xFF1E1B16)
val Schenbrunn_Yellowmd_md_theme_dark_inverseSurface = Color(0xFFE9E1D9)
val Schenbrunn_Yellowmd_md_theme_dark_inversePrimary = Color(0xFF765B00)
val Schenbrunn_Yellowmd_md_theme_dark_shadow = Color(0xFF000000)
val Schenbrunn_Yellowmd_md_theme_dark_surfaceTint = Color(0xFFEEC148)
val Schenbrunn_Yellowmd_md_theme_dark_outlineVariant = Color(0xFF4C4639)
val Schenbrunn_Yellowmd_md_theme_dark_scrim = Color(0xFF000000)


val Schenbrunn_Yellowmd_seed = Color(0xFFFBD26A)
