package com.xiaojinzi.tally.lib.res.model.tally

import androidx.annotation.Keep
import com.xiaojinzi.support.annotation.ModelForNetwork

@Keep
@ModelForNetwork
data class TallyInitSyncRes(
    val bookList: List<TallyRemoteBookRes>,
    val categoryList: List<TallyRemoteCategoryRes>
) {

    companion object {
        fun createForOpenSource(): TallyInitSyncRes {
            val currentTime = System.currentTimeMillis()
            return TallyInitSyncRes(
                bookList = listOf(
                    TallyRemoteBookRes(
                        id = "1",
                        userId = "",
                        isSystem = true,
                        type = TallyBookDto.TYPE_NORMAL,
                        name = "默认账本",
                        iconName = "icon_name",
                        timeCreate = currentTime,
                        timeModify = currentTime
                    )
                ),
                categoryList = TallyRemoteCategoryRes.createForOpenSource(),
            )
        }

        /**
         * 为开源版本创建初始化数据，支持动态用户ID
         */
        fun createForOpenSourceWithUserId(userId: String): TallyInitSyncRes {
            val currentTime = System.currentTimeMillis()
            return TallyInitSyncRes(
                bookList = listOf(
                    TallyRemoteBookRes(
                        id = userId, // 直接使用用户ID作为账本ID，与其他模型保持一致
                        userId = userId,
                        isSystem = true,
                        type = TallyBookDto.TYPE_NORMAL,
                        name = "我的账本",
                        iconName = "book1",
                        timeCreate = currentTime,
                        timeModify = currentTime
                    )
                ),
                categoryList = TallyRemoteCategoryRes.createForOpenSourceWithUserId(userId),
            )
        }
    }

}
