<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="Theme.Tally.App" parent="Theme.AppCompat.Light.NoActionBar" />

    <style name="Theme.Tally.App.Transparent">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:activityOpenEnterAnimation">@anim/alpha_in</item>
        <item name="android:activityCloseExitAnimation">@anim/alpha_out</item>
    </style>

    <style name="Theme.Tally.App.Loading">
        <item name="android:windowBackground">@color/res_light_background</item>
    </style>

</resources>