plugins {
    id("moduleBusinessPlugin")
    id("org.jetbrains.kotlin.plugin.serialization")
}

android {
    namespace = "com.xiaojinzi.tally.module.core"
}

dependencies {
    // Ktor 网络客户端 (完整依赖) - 更新到与 Supabase 3.0.2 兼容的版本
    implementation("io.ktor:ktor-client-android:3.1.1")
    implementation("io.ktor:ktor-client-core:3.1.1")
    implementation("io.ktor:ktor-client-content-negotiation:3.1.1")
    implementation("io.ktor:ktor-client-plugins:3.1.1")
    implementation("io.ktor:ktor-serialization-kotlinx-json:3.1.1")

    // Supabase 依赖 - 使用 3.1.0 版本以兼容 Kotlin 2.0.21
    implementation("io.github.jan-tennert.supabase:postgrest-kt:3.1.0")
    implementation("io.github.jan-tennert.supabase:auth-kt:3.1.0")
    implementation("io.github.jan-tennert.supabase:realtime-kt:3.1.0")
}