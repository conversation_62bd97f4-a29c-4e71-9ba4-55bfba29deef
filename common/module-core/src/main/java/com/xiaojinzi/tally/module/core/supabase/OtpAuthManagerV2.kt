package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import io.github.jan.supabase.auth.user.UserSession
// AuthEvent 在 Supabase 3.1.0 中不可用，移除相关导入
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel

/**
 * OTP 认证管理器 V2
 * 
 * 基于 PowerSync 最佳实践重构的 OTP 认证管理器
 * 使用新的 SupabaseConnector 提供更强大的功能：
 * 
 * 主要改进：
 * - 统一的错误处理和重试机制
 * - 实时会话状态监听
 * - 自动连接管理
 * - 更好的日志记录
 * - 线程安全操作
 * - 向后兼容的 API
 * 
 * <AUTHOR> 4.0 sonnet 🐾 (基于 PowerSync 最佳实践)
 * @since 2025-01-21
 */
class OtpAuthManagerV2 {
    
    companion object {
        private const val TAG = "OtpAuthManagerV2"
        
        // 单例实例
        @Volatile
        private var INSTANCE: OtpAuthManagerV2? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(): OtpAuthManagerV2 {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: OtpAuthManagerV2().also { INSTANCE = it }
            }
        }
    }
    
    // Supabase 连接器
    private val connector = SupabaseConnector.getInstance()
    
    // 协程作用域
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 重新发送倒计时状态
    private val _resendCountdown = MutableStateFlow(0)
    val resendCountdown: StateFlow<Int> = _resendCountdown.asStateFlow()
    
    // 倒计时任务
    private var countdownJob: Job? = null
    
    /**
     * 会话状态流
     * 可用于 UI 层监听认证状态变化
     */
    val sessionState: StateFlow<SupabaseConnector.SessionState> = connector.sessionState
    
    /**
     * 连接状态流
     * 可用于监听连接状态变化
     */
    val connectionState: StateFlow<SupabaseConnector.ConnectionState> = connector.connectionState
    
    // 认证事件流在 Supabase 3.1.0 中不可用，移除相关代码
    // 在 3.1.0 版本中，应该通过监听 sessionState 来处理认证状态变化
    
    /**
     * 当前是否已认证
     */
    val isAuthenticated: Boolean
        get() = connector.isAuthenticated
    
    /**
     * 当前用户会话
     */
    val currentSession: UserSession?
        get() = connector.currentSession
    
    /**
     * 初始化认证管理器
     * 
     * @return 初始化结果
     */
    suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始初始化 OTP 认证管理器 V2 ===========")
            
            val result = connector.initialize()
            
            if (result.isSuccess) {
                Log.d(TAG, "✅ OTP 认证管理器 V2 初始化成功")
            } else {
                Log.e(TAG, "❌ OTP 认证管理器 V2 初始化失败: ${result.exceptionOrNull()?.message}")
            }
            
            Log.d(TAG, "========== OTP 认证管理器 V2 初始化完成 ===========")
            result
        } catch (e: Exception) {
            Log.e(TAG, "❌ 初始化过程中发生异常: ${e.message}", e)
            Result.failure(AuthException("认证管理器初始化失败: ${e.message}", e))
        }
    }
    
    /**
     * 发送邮箱验证码
     * 
     * @param email 邮箱地址
     * @param shouldCreateUser 如果用户不存在是否自动创建，默认为 true
     * @return 发送结果，成功返回 true
     * @throws AuthException 认证异常
     */
    suspend fun sendEmailOtp(
        email: String,
        shouldCreateUser: Boolean = true
    ): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始发送邮箱验证码 (V2) ===========")
            Log.d(TAG, "邮箱: $email, 创建用户: $shouldCreateUser")
            
            // 检查是否在倒计时中
            if (_resendCountdown.value > 0) {
                throw AuthException("请等待 ${_resendCountdown.value} 秒后再重新发送")
            }
            
            // 验证邮箱格式
            if (!isValidEmail(email)) {
                throw AuthException("邮箱格式不正确")
            }
            
            // 使用连接器发送 OTP
            val result = connector.sendEmailOtp(email, shouldCreateUser)
            
            if (result.isSuccess) {
                Log.d(TAG, "✅ 邮箱验证码发送成功 (V2)")
                
                // 启动60秒倒计时
                startResendCountdown()
                
                Log.d(TAG, "========== 验证码发送完成 (V2) ===========")
                return@withContext true
            } else {
                val exception = result.exceptionOrNull()
                Log.e(TAG, "❌ 邮箱验证码发送失败 (V2): ${exception?.message}", exception)
                
                // 转换为用户友好的错误消息
                val userFriendlyMessage = when {
                    exception?.message?.contains("rate limit", ignoreCase = true) == true -> 
                        "发送太频繁，请稍后再试"
                    exception?.message?.contains("invalid", ignoreCase = true) == true -> 
                        "邮箱地址格式不正确"
                    exception?.message?.contains("network", ignoreCase = true) == true -> 
                        "网络连接失败，请检查网络"
                    exception?.message?.contains("configuration", ignoreCase = true) == true -> 
                        "服务配置错误，请联系开发者"
                    else -> "发送验证码失败: ${exception?.message ?: "未知错误"}"
                }
                
                throw AuthException(userFriendlyMessage, exception)
            }
        } catch (e: AuthException) {
            // AuthException 直接重新抛出
            Log.w(TAG, "收到 AuthException: ${e.message}")
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "❌ 发送验证码时发生未预期的异常: ${e.message}", e)
            
            val userFriendlyMessage = when (e.javaClass.simpleName) {
                "UnknownHostException", "ConnectException" -> "网络连接失败，请检查网络设置"
                "SocketTimeoutException" -> "网络请求超时，请稍后重试"
                "SSLException" -> "安全连接失败，请检查网络环境"
                "IllegalStateException" -> "应用状态异常，请重启应用后重试"
                "IllegalArgumentException" -> "参数错误，请检查邮箱格式"
                else -> "发送验证码失败: ${e.message ?: "未知错误"}"
            }
            
            throw AuthException(userFriendlyMessage, e)
        }
    }
    
    /**
     * 启动重新发送倒计时（60秒）
     */
    private fun startResendCountdown() {
        // 取消之前的倒计时任务
        countdownJob?.cancel()
        
        countdownJob = scope.launch {
            try {
                Log.d(TAG, "开始60秒重新发送倒计时")
                _resendCountdown.value = 60
                
                for (i in 59 downTo 0) {
                    delay(1000) // 等待1秒
                    _resendCountdown.value = i
                }
                
                Log.d(TAG, "重新发送倒计时结束")
            } catch (e: Exception) {
                Log.w(TAG, "倒计时过程中发生异常: ${e.message}")
            }
        }
    }
    
    /**
     * 取消重新发送倒计时
     */
    fun cancelResendCountdown() {
        countdownJob?.cancel()
        _resendCountdown.value = 0
        Log.d(TAG, "重新发送倒计时已取消")
    }
    
    /**
     * 检查是否可以重新发送
     */
    val canResend: Boolean
        get() = _resendCountdown.value == 0
    
    /**
     * 验证邮箱验证码并登录
     * 
     * @param email 邮箱地址
     * @param token 6位验证码
     * @return 用户会话信息
     * @throws AuthException 认证异常
     */
    suspend fun verifyEmailOtp(
        email: String,
        token: String
    ): UserSession = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始验证邮箱验证码 (V2) ===========")
            Log.d(TAG, "邮箱: $email")
            
            // 验证输入参数
            if (!isValidEmail(email)) {
                throw AuthException("邮箱格式不正确")
            }
            
            if (token.isBlank() || token.length != 6 || !token.all { it.isDigit() }) {
                throw AuthException("验证码格式不正确，请输入6位数字")
            }
            
            // 使用连接器验证 OTP
            val result = connector.verifyEmailOtp(email, token)
            
            if (result.isSuccess) {
                val session = result.getOrThrow()
                Log.d(TAG, "✅ 邮箱验证码验证成功 (V2)")
                Log.d(TAG, "用户ID: ${session.user?.id}")
                Log.d(TAG, "用户邮箱: ${session.user?.email}")
                Log.d(TAG, "========== 验证码验证完成 (V2) ===========")
                return@withContext session
            } else {
                val exception = result.exceptionOrNull()
                Log.e(TAG, "❌ 邮箱验证码验证失败 (V2): ${exception?.message}", exception)
                
                // 转换为用户友好的错误消息
                val userFriendlyMessage = when {
                    exception?.message?.contains("invalid", ignoreCase = true) == true ||
                    exception?.message?.contains("expired", ignoreCase = true) == true -> 
                        "验证码无效或已过期，请重新获取"
                    exception?.message?.contains("too many", ignoreCase = true) == true -> 
                        "验证尝试次数过多，请稍后再试"
                    exception?.message?.contains("network", ignoreCase = true) == true -> 
                        "网络连接失败，请检查网络"
                    else -> "验证码验证失败: ${exception?.message ?: "未知错误"}"
                }
                
                throw AuthException(userFriendlyMessage, exception)
            }
        } catch (e: AuthException) {
            // AuthException 直接重新抛出
            Log.w(TAG, "收到 AuthException: ${e.message}")
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "❌ 验证验证码时发生未预期的异常: ${e.message}", e)
            throw AuthException("验证码验证失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取当前用户会话
     * 
     * @return 当前用户会话，如果未登录返回 null
     */
    suspend fun getCurrentSession(): UserSession? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取当前用户会话")
            val session = connector.currentSession
            
            if (session != null) {
                Log.d(TAG, "✅ 获取到当前会话，用户: ${session.user?.email}")
            } else {
                Log.d(TAG, "当前无用户会话")
            }
            
            session
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取当前会话失败: ${e.message}", e)
            null
        }
    }
    
    /**
     * 检查用户是否已登录
     * 
     * @return 是否已登录
     */
    suspend fun isLoggedIn(): Boolean = withContext(Dispatchers.IO) {
        try {
            val isLoggedIn = connector.isAuthenticated
            Log.d(TAG, "用户登录状态: $isLoggedIn")
            isLoggedIn
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查登录状态失败: ${e.message}", e)
            false
        }
    }
    
    /**
     * 登出当前用户
     */
    suspend fun signOut() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始用户登出 (V2) ===========")
            
            val result = connector.signOut()
            
            if (result.isSuccess) {
                Log.d(TAG, "✅ 用户登出成功 (V2)")
            } else {
                Log.w(TAG, "⚠️  用户登出失败，但继续执行: ${result.exceptionOrNull()?.message}")
                // 登出失败不抛出异常，因为连接器已经清除了本地状态
            }
            
            Log.d(TAG, "========== 用户登出完成 (V2) ===========")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 登出过程中发生异常: ${e.message}", e)
            // 登出异常不向上抛出，确保 UI 能正常处理
        }
    }
    
    /**
     * 刷新当前会话
     * 
     * @return 刷新后的用户会话
     * @throws AuthException 刷新失败
     */
    suspend fun refreshSession(): UserSession = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始刷新会话 (V2) ===========")
            
            val result = connector.refreshSession()
            
            if (result.isSuccess) {
                val session = result.getOrThrow()
                Log.d(TAG, "✅ 会话刷新成功 (V2)")
                Log.d(TAG, "========== 会话刷新完成 (V2) ===========")
                return@withContext session
            } else {
                val exception = result.exceptionOrNull()
                Log.e(TAG, "❌ 会话刷新失败 (V2): ${exception?.message}", exception)
                throw AuthException("会话刷新失败: ${exception?.message}", exception)
            }
        } catch (e: AuthException) {
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "❌ 刷新会话时发生异常: ${e.message}", e)
            throw AuthException("会话刷新失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取访问令牌
     * 
     * @return 访问令牌
     * @throws AuthException 获取失败
     */
    suspend fun getAccessToken(): String = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取访问令牌")
            
            val result = connector.getAccessToken()
            
            if (result.isSuccess) {
                val token = result.getOrThrow()
                Log.d(TAG, "✅ 访问令牌获取成功，长度: ${token.length}")
                return@withContext token
            } else {
                val exception = result.exceptionOrNull()
                Log.e(TAG, "❌ 访问令牌获取失败: ${exception?.message}", exception)
                throw AuthException("获取访问令牌失败: ${exception?.message}", exception)
            }
        } catch (e: AuthException) {
            throw e
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取访问令牌时发生异常: ${e.message}", e)
            throw AuthException("获取访问令牌失败: ${e.message}", e)
        }
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 是否为有效邮箱格式
     */
    fun isValidEmail(email: String): Boolean {
        return connector.isValidEmail(email)
    }
    
    /**
     * 销毁认证管理器
     * 清理资源，取消监听
     */
    fun destroy() {
        Log.d(TAG, "开始销毁 OTP 认证管理器 V2")
        
        // 取消倒计时任务
        countdownJob?.cancel()
        
        // 取消协程作用域
        scope.cancel()
        
        // 销毁连接器
        connector.destroy()
        
        Log.d(TAG, "✅ OTP 认证管理器 V2 销毁完成")
    }
    
    // ========== 向后兼容的方法 ==========
    
    /**
     * 向后兼容的发送 OTP 方法
     * 返回 Result 而不是抛出异常
     */
    suspend fun sendEmailOtpCompat(
        email: String,
        shouldCreateUser: Boolean = true
    ): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val success = sendEmailOtp(email, shouldCreateUser)
            Result.success(success)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 向后兼容的验证 OTP 方法
     * 返回 Result 而不是抛出异常
     */
    suspend fun verifyEmailOtpCompat(
        email: String,
        token: String
    ): Result<UserSession> = withContext(Dispatchers.IO) {
        try {
            val session = verifyEmailOtp(email, token)
            Result.success(session)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * 向后兼容的登出方法
     * 返回 Result 而不是抛出异常
     */
    suspend fun signOutCompat(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}