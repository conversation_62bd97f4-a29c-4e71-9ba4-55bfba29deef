package com.xiaojinzi.tally.module.core.supabase.model

import com.xiaojinzi.tally.lib.res.model.user.UserInfoCacheDto
import java.util.UUID

/**
 * 数据模型转换器
 * 
 * 用于在本地 SQLite 模型和 Supabase 模型之间进行转换
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */

// =====================================================
// 基础转换函数
// =====================================================

/**
 * UserInfoCacheDto -> SupabaseUser
 */
fun UserInfoCacheDto.toSupabaseUser(): SupabaseUser {
    return SupabaseUser(
        id = id,
        name = name,
        timeExpire = timeExpire,
        timeCreate = System.currentTimeMillis(),
        timeModify = null,
        isDeleted = false,
        isSync = false
    )
}

// =====================================================
// 工具函数
// =====================================================

/**
 * 生成新的 UUID
 */
fun generateId(): String = UUID.randomUUID().toString()

/**
 * 获取当前时间戳
 */
fun getCurrentTimestamp(): Long = System.currentTimeMillis()
