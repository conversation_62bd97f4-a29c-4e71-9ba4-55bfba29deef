-- =====================================================
-- 为账本表添加删除确认期相关字段
-- =====================================================

-- 1. 为 user_books 表添加删除相关字段
ALTER TABLE user_books 
ADD COLUMN IF NOT EXISTS is_pending_delete BOOLEAN DEFAULT FALSE;

ALTER TABLE user_books 
ADD COLUMN IF NOT EXISTS delete_requested_at TIMESTAMP WITH TIME ZONE;

-- 2. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_user_books_pending_delete 
ON user_books(is_pending_delete, delete_requested_at) 
WHERE is_pending_delete = TRUE;

-- 3. 创建清理过期删除请求的函数
CREATE OR REPLACE FUNCTION cleanup_expired_book_deletions()
RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER := 0;
    v_book_record RECORD;
BEGIN
    -- 查找所有过期的删除请求（超过24小时）
    FOR v_book_record IN 
        SELECT id, user_id, name 
        FROM user_books 
        WHERE is_pending_delete = TRUE 
        AND delete_requested_at <= NOW() - INTERVAL '24 hours'
    LOOP
        -- 删除账本相关的所有数据
        DELETE FROM book_categories WHERE book_id = v_book_record.id;
        DELETE FROM book_accounts WHERE book_id = v_book_record.id;
        -- 注意：如果有账单表，也需要删除相关账单
        
        -- 删除账本本身
        DELETE FROM user_books WHERE id = v_book_record.id;
        
        v_deleted_count := v_deleted_count + 1;
        
        RAISE NOTICE '已删除过期账本: % (用户: %)', v_book_record.name, v_book_record.user_id;
    END LOOP;
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. 创建请求删除账本的函数
CREATE OR REPLACE FUNCTION request_delete_book(p_book_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    v_book_count INTEGER;
BEGIN
    -- 检查用户是否有其他账本（不能删除最后一个账本）
    SELECT COUNT(*) INTO v_book_count
    FROM user_books 
    WHERE user_id = p_user_id 
    AND (is_pending_delete = FALSE OR is_pending_delete IS NULL);
    
    IF v_book_count <= 1 THEN
        RAISE EXCEPTION '不能删除最后一个账本';
    END IF;
    
    -- 标记账本为待删除状态
    UPDATE user_books 
    SET 
        is_pending_delete = TRUE,
        delete_requested_at = NOW(),
        updated_at = NOW()
    WHERE id = p_book_id AND user_id = p_user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. 创建取消删除账本的函数
CREATE OR REPLACE FUNCTION cancel_delete_book(p_book_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE user_books 
    SET 
        is_pending_delete = FALSE,
        delete_requested_at = NULL,
        updated_at = NOW()
    WHERE id = p_book_id AND user_id = p_user_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. 创建获取账本删除状态的函数
CREATE OR REPLACE FUNCTION get_book_delete_status(p_book_id UUID)
RETURNS TABLE(
    is_pending_delete BOOLEAN,
    delete_requested_at TIMESTAMP WITH TIME ZONE,
    hours_remaining INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ub.is_pending_delete,
        ub.delete_requested_at,
        CASE 
            WHEN ub.is_pending_delete = TRUE AND ub.delete_requested_at IS NOT NULL THEN
                GREATEST(0, 24 - EXTRACT(HOURS FROM (NOW() - ub.delete_requested_at))::INTEGER)
            ELSE NULL
        END as hours_remaining
    FROM user_books ub
    WHERE ub.id = p_book_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. 验证修改
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_books' 
AND column_name IN ('is_pending_delete', 'delete_requested_at')
ORDER BY column_name;
