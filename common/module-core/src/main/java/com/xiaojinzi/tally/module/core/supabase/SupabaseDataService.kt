package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.Order
import io.github.jan.supabase.postgrest.rpc
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonPrimitive

/**
 * Supabase 类别插入数据类（匹配 book_categories 表结构）
 */
@Serializable
data class SupabaseCategoryInsert(
    val id: String,
    val user_id: String,
    val book_id: String,
    val parent_id: String? = null,
    val name: String,
    val icon_name: String = "category1",
    val type: String = "spending", // "spending" 或 "income"
    val sort_order: Int = 0, // 注意：book_categories表中是INTEGER类型
    val is_deleted: Boolean = false
)

/**
 * Supabase 账本插入数据类
 */
@Serializable
data class SupabaseBookInsert(
    val id: String,
    val user_id: String,
    val name: String,
    val description: String? = null,
    val icon_name: String = "book1",
    val color: String = "#2196F3",
    val is_default: Boolean = false,
    val is_system: Boolean = false,
    val sort_order: Int = 0,
    val time_create: Long,
    val time_modify: Long? = null,
    val time_modify_format: String? = null,
    val is_deleted: Boolean = false,
    val is_sync: Boolean = true
)

/**
 * Supabase 用户插入数据类
 */
@Serializable
data class SupabaseUserInsert(
    val id: String,
    val name: String,
    val time_expire: Long,
    val time_create: Long,
    val time_modify: Long? = null,
    val time_modify_format: String? = null,
    val is_deleted: Boolean = false,
    val is_sync: Boolean = true
)

/**
 * Supabase 记账插入数据类
 */
@Serializable
data class SupabaseBillInsert(
    val id: String,                           // 标准UUID格式
    val user_id: String,                      // 用户ID
    val book_id: String,                      // 账本ID
    val type: String,                         // 记账类型：normal, transfer, refund
    val origin_bill_id: String?,              // 原始账单ID（用于退款）
    val time: Long,                           // 记账时间戳
    val category_id: String?,                 // 分类ID
    val account_id: String?,                  // 账户ID
    val transfer_target_account_id: String?,  // 转账目标账户ID
    val amount: Long,                         // 金额（单位：分）
    val note: String?,                        // 备注
    val is_not_calculate: Boolean,            // 是否不参与计算
    val time_create: Long,                    // 创建时间戳
    val time_modify: Long?,                   // 修改时间戳
    val time_modify_format: String?,          // 格式化修改时间
    val is_deleted: Boolean,                  // 是否已删除
    val is_sync: Boolean                      // 是否已同步
    // created_at 和 updated_at 由数据库自动管理，不需要在插入时提供
)

/**
 * Supabase 账单查询数据类
 */
@Serializable
data class SupabaseBillDto(
    val id: String,
    val user_id: String,
    val book_id: String,
    val type: String,
    val origin_bill_id: String? = null,
    val time: Long,
    val category_id: String? = null,
    val account_id: String? = null,
    val transfer_target_account_id: String? = null,
    val amount: Long,
    val note: String? = null,
    val is_not_calculate: Boolean = false,
    val time_create: Long,
    val time_modify: Long? = null,
    val is_deleted: Boolean = false,
    val is_sync: Boolean = true
)

/**
 * Supabase 数据服务
 * 
 * 提供账本、类别、账户等数据的 CRUD 操作
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
object SupabaseDataService {
    
    private const val TAG = "SupabaseDataService"
    private val supabaseClient get() = SupabaseConfig.client
    
    // ==================== 账本相关操作 ====================
    
    /**
     * 获取用户的所有账本
     */
    suspend fun getUserBooks(userId: String): Result<List<com.xiaojinzi.tally.lib.res.model.tally.TallyBookDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取用户账本: userId=$userId")
            
            val response = supabaseClient.from("user_books").select(
                columns = Columns.list(
                    "id", "user_id", "is_system", "name", "icon_name",
                    "created_at", "updated_at", "is_deleted"
                )
            ) {
                filter {
                    eq("user_id", userId)
                    eq("is_deleted", false)
                }
                order("created_at", Order.ASCENDING)
            }.decodeList<SupabaseTallyBookDto>()

            val standardBooks = response.map { it.toStandardBookDto() }

            Log.d(TAG, "✅ 获取用户账本成功: 共${standardBooks.size}个账本")
            Result.success(standardBooks)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取用户账本失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // TODO: 暂时注释掉，需要重构这些方法以使用正确的数据类型
    /*
    suspend fun createBook(book: TallyBookDto): Result<TallyBookDto> = withContext(Dispatchers.IO) {
        // 实现待完善
    }

    suspend fun updateBook(book: TallyBookDto): Result<TallyBookDto> = withContext(Dispatchers.IO) {
        // 实现待完善
    }
    */
    
    // ==================== 类别相关操作 ====================
    
    /**
     * 获取账本的所有类别
     */
    suspend fun getBookCategories(bookId: String): Result<List<com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取账本类别: bookId=$bookId")

            val response = supabaseClient.from("book_categories").select(
                columns = Columns.list(
                    "id", "user_id", "name", "icon_name", "type", "sort_order", "book_id", "parent_id",
                    "created_at", "updated_at", "is_deleted"
                )
            ) {
                filter {
                    eq("book_id", bookId)
                    eq("is_deleted", false)
                }
                order("sort_order", Order.ASCENDING)
            }.decodeList<SupabaseTallyCategoryDto>()

            val standardCategories = response.map { it.toStandardCategoryDto() }

            Log.d(TAG, "✅ 获取账本类别成功: 共${standardCategories.size}个类别")
            Result.success(standardCategories)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取账本类别失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 手动同步账本数据到本地
     */
    suspend fun syncBookDataToLocal(bookId: String): Result<SyncResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步账本数据到本地: bookId=$bookId")

            // 直接从 Supabase 获取原始数据
            val response = supabaseClient.from("book_categories").select(
                columns = Columns.list(
                    "id", "name", "icon_name", "type", "sort_order", "book_id", "parent_id", "user_id",
                    "created_at", "updated_at", "is_deleted"
                )
            ) {
                filter {
                    eq("book_id", bookId)
                    eq("is_deleted", false)
                }
                order("sort_order", Order.ASCENDING)
            }.decodeList<SupabaseTallyCategoryDto>()

            Log.d(TAG, "📊 从 Supabase 获取到 ${response.size} 个类别")

            // 转换为插入 DTO
            val insertDtoList = response.map { supabaseCategory ->
                com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryInsertDto(
                    id = supabaseCategory.id,
                    userId = supabaseCategory.user_id,
                    bookId = supabaseCategory.book_id,
                    parentId = supabaseCategory.parent_id,
                    type = supabaseCategory.type, // 直接使用字符串类型
                    iconName = supabaseCategory.icon_name ?: "category1",
                    name = supabaseCategory.name,
                    sort = System.currentTimeMillis() - supabaseCategory.sort_order.toLong(),
                    timeCreate = System.currentTimeMillis(),
                    timeModify = System.currentTimeMillis(),
                    isDeleted = supabaseCategory.is_deleted,
                    isSync = true
                )
            }

            // 使用应用的数据源服务插入数据
            val insertedIds = com.xiaojinzi.tally.module.base.support.AppServices.tallyDataSourceSpi
                .insertOrUpdateCategoryList(
                    targetList = insertDtoList,
                    isNeedSync = false // 避免循环同步
                )

            val syncResult = SyncResult(
                syncedCategories = insertedIds.size,
                totalCategories = response.size
            )

            Log.d(TAG, "✅ 同步完成: 同步了 ${syncResult.syncedCategories} 个类别")
            Result.success(syncResult)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步账本数据失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 批量创建类别
     */
    suspend fun createCategories(categories: List<TallyCategoryDto>): Result<List<TallyCategoryDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "批量创建类别: 共${categories.size}个")
            
            val response = supabaseClient.from("tally_categories").insert(categories) {
                select()
            }.decodeList<TallyCategoryDto>()
            
            Log.d(TAG, "✅ 批量创建类别成功")
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 批量创建类别失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 修复类别图标
     */
    suspend fun fixCategoryIcons(): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔧 开始执行 Supabase 图标修复...")

            var totalFixed = 0

            // 修复"其他支出"类别图标
            try {
                supabaseClient.from("book_categories").update({
                    set("icon_name", "more1")
                }) {
                    filter {
                        eq("name", "其他支出")
                        eq("type", "spending")
                        eq("icon_name", "other1")
                        eq("is_deleted", false)
                    }
                }
                Log.d(TAG, "✅ 修复'其他支出'图标: other1 → more1")
                totalFixed++
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 修复'其他支出'图标失败: ${e.message}")
            }

            // 修复"出行交通"类别图标
            try {
                supabaseClient.from("book_categories").update({
                    set("icon_name", "taxi1")
                }) {
                    filter {
                        eq("name", "出行交通")
                        eq("type", "spending")
                        eq("icon_name", "car1")
                        eq("is_deleted", false)
                    }
                }
                Log.d(TAG, "✅ 修复'出行交通'图标: car1 → taxi1")
                totalFixed++
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 修复'出行交通'图标失败: ${e.message}")
            }

            // 修复"休闲娱乐"类别图标
            try {
                supabaseClient.from("book_categories").update({
                    set("icon_name", "gamePad1")
                }) {
                    filter {
                        eq("name", "休闲娱乐")
                        eq("type", "spending")
                        eq("icon_name", "gamepad1")
                        eq("is_deleted", false)
                    }
                }
                Log.d(TAG, "✅ 修复'休闲娱乐'图标: gamepad1 → gamePad1")
                totalFixed++
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 修复'休闲娱乐'图标失败: ${e.message}")
            }

            // 修复"健康医疗"类别图标
            try {
                supabaseClient.from("book_categories").update({
                    set("icon_name", "firstAidKit1")
                }) {
                    filter {
                        eq("name", "健康医疗")
                        eq("type", "spending")
                        eq("icon_name", "medical1")
                        eq("is_deleted", false)
                    }
                }
                Log.d(TAG, "✅ 修复'健康医疗'图标: medical1 → firstAidKit1")
                totalFixed++
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 修复'健康医疗'图标失败: ${e.message}")
            }

            // 修复"投资收益"类别图标
            try {
                supabaseClient.from("book_categories").update({
                    set("icon_name", "invest1")
                }) {
                    filter {
                        eq("name", "投资收益")
                        eq("type", "income")
                        eq("icon_name", "investment1")
                        eq("is_deleted", false)
                    }
                }
                Log.d(TAG, "✅ 修复'投资收益'图标: investment1 → invest1")
                totalFixed++
            } catch (e: Exception) {
                Log.w(TAG, "⚠️ 修复'投资收益'图标失败: ${e.message}")
            }

            Log.d(TAG, "🎉 Supabase 图标修复完成，共尝试修复 $totalFixed 个类别")
            Result.success(totalFixed)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Supabase 图标修复失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 插入新类别到 Supabase
     */
    suspend fun insertCategoryInSupabase(category: com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "📤 插入 Supabase 类别: ${category.name} (${category.id})")

            // 注意：sort_order 在 Supabase 中是 integer 类型，需要转换
            val sortOrder = if (category.sort > Int.MAX_VALUE) {
                // 如果超出 integer 范围，使用当前时间的秒数
                (System.currentTimeMillis() / 1000).toInt()
            } else {
                category.sort.toInt()
            }

            // 构建要插入的数据
            val categoryData = SupabaseCategoryInsert(
                id = category.id,
                user_id = category.userId,
                book_id = category.bookId,
                parent_id = category.parentId,
                name = category.name ?: "",
                icon_name = category.iconName ?: "category1",
                type = category.type.dbStr,
                sort_order = sortOrder,
                is_deleted = category.isDeleted
            )

            supabaseClient.from("book_categories").insert(categoryData)

            Log.d(TAG, "✅ Supabase 类别插入成功: ${category.name}")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 插入 Supabase 类别失败: ${category.name} - ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 更新 Supabase 中的类别数据
     */
    suspend fun updateCategoryInSupabase(category: com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "📤 更新 Supabase 类别: ${category.name} (${category.id})")

            // 更新 Supabase 中的类别
            // 注意：sort_order 在 Supabase 中是 integer 类型，需要转换
            val sortOrder = if (category.sort > Int.MAX_VALUE) {
                // 如果超出 integer 范围，使用当前时间的秒数
                (System.currentTimeMillis() / 1000).toInt()
            } else {
                category.sort.toInt()
            }

            supabaseClient.from("book_categories").update({
                set("name", category.name)
                set("icon_name", category.iconName)
                set("type", category.type.dbStr)
                set("sort_order", sortOrder)
                set("is_deleted", category.isDeleted)
            }) {
                filter {
                    eq("id", category.id)
                }
            }

            Log.d(TAG, "✅ Supabase 类别更新成功: ${category.name}")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 更新 Supabase 类别失败: ${category.name} - ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 智能同步类别到 Supabase（自动判断插入或更新）
     */
    suspend fun upsertCategoryInSupabase(category: com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 智能同步 Supabase 类别: ${category.name} (${category.id})")

            // 先尝试检查记录是否存在
            val existsResult = supabaseClient.from("book_categories").select(
                columns = Columns.list("id")
            ) {
                filter {
                    eq("id", category.id)
                }
                limit(1)
            }.decodeList<Map<String, String>>()

            if (existsResult.isNotEmpty()) {
                // 记录存在，执行更新
                Log.d(TAG, "📝 记录已存在，执行更新: ${category.name}")
                return@withContext updateCategoryInSupabase(category)
            } else {
                // 记录不存在，执行插入
                Log.d(TAG, "➕ 记录不存在，执行插入: ${category.name}")
                return@withContext insertCategoryInSupabase(category)
            }

        } catch (e: Exception) {
            Log.e(TAG, "❌ 智能同步 Supabase 类别失败: ${category.name} - ${e.message}", e)
            Result.failure(e)
        }
    }

    // ==================== 账户相关操作 ====================
    
    /**
     * 获取账本的所有账户
     */
    suspend fun getBookAccounts(bookId: String): Result<List<TallyAccountDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取账本账户: bookId=$bookId")
            
            val response = supabaseClient.from("tally_accounts").select(
                columns = Columns.list(
                    "id", "name", "icon", "type", "balance", "sort", "book_id",
                    "created_time", "updated_time", "is_deleted"
                )
            ) {
                filter {
                    eq("book_id", bookId)
                    eq("is_deleted", false)
                }
                order("sort", Order.ASCENDING)
            }.decodeList<TallyAccountDto>()
            
            Log.d(TAG, "✅ 获取账本账户成功: 共${response.size}个账户")
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取账本账户失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 创建新账户
     */
    suspend fun createAccount(account: TallyAccountDto): Result<TallyAccountDto> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "创建账户: name=${account.name}")
            
            val response = supabaseClient.from("tally_accounts").insert(account) {
                select()
            }.decodeSingle<TallyAccountDto>()
            
            Log.d(TAG, "✅ 创建账户成功: id=${response.id}")
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 创建账户失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // ==================== 用户配置相关操作 ====================
    
    /**
     * 获取用户配置
     */
    suspend fun getUserConfig(userId: String): Result<UserConfigDto?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "获取用户配置: userId=$userId")
            
            val response = supabaseClient.from("user_configs").select(
                columns = Columns.list(
                    "id", "user_id", "current_book_id", "theme", "language",
                    "created_time", "updated_time"
                )
            ) {
                filter {
                    eq("user_id", userId)
                }
                single()
            }.decodeSingleOrNull<UserConfigDto>()
            
            if (response != null) {
                Log.d(TAG, "✅ 获取用户配置成功")
            } else {
                Log.d(TAG, "⚠️ 用户配置不存在")
            }
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取用户配置失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 创建或更新用户配置
     */
    suspend fun upsertUserConfig(config: UserConfigDto): Result<UserConfigDto> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "创建或更新用户配置: userId=${config.userId}")
            
            val response = supabaseClient.from("user_configs").upsert(config) {
                select()
            }.decodeSingle<UserConfigDto>()
            
            Log.d(TAG, "✅ 用户配置保存成功")
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 用户配置保存失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    // ==================== 记账相关操作 ====================

    /**
     * 确保账本在 public.books 表中存在
     */
    suspend fun ensureBookExistsInPublicBooks(bookId: String, userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 检查账本是否在 public.books 表中存在: $bookId")

            // 检查账本是否已存在
            val bookCount = supabaseClient.from("books").select(columns = Columns.list("id")) {
                filter {
                    eq("id", bookId)
                }
                limit(1)
            }.decodeList<Map<String, String>>().size

            if (bookCount > 0) {
                Log.d(TAG, "✅ 账本已存在于 public.books 表中")
                return@withContext Result.success(Unit)
            }

            Log.d(TAG, "🔄 账本不存在，从 user_books 获取数据并创建 public.books 记录...")

            // 从 user_books 表获取账本信息
            val userBook = supabaseClient.from("user_books").select {
                filter {
                    eq("id", bookId)
                    eq("user_id", userId)
                }
            }.decodeSingleOrNull<SupabaseTallyBookDto>()

            if (userBook == null) {
                val error = Exception("账本在 user_books 表中不存在: $bookId")
                Log.e(TAG, "❌ ${error.message}")
                return@withContext Result.failure(error)
            }

            // 创建 books 表记录
            val currentTime = System.currentTimeMillis()
            val bookData = SupabaseBookInsert(
                id = userBook.id,
                user_id = userBook.user_id,
                name = userBook.name ?: "我的账本",
                description = null, // SupabaseTallyBookDto 没有 description 字段
                icon_name = userBook.icon_name ?: "book1",
                color = "#2196F3", // SupabaseTallyBookDto 没有 color 字段，使用默认值
                is_default = false, // SupabaseTallyBookDto 没有 is_default 字段，使用默认值
                is_system = userBook.is_system,
                sort_order = 0, // SupabaseTallyBookDto 没有 sort_order 字段，使用默认值
                time_create = currentTime,
                time_modify = currentTime,
                is_deleted = false,
                is_sync = true
            )

            supabaseClient.from("books").insert(bookData)

            Log.d(TAG, "✅ 成功创建 public.books 记录: $bookId")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 确保账本存在失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 确保类别在 public.categories 表中存在
     */
    suspend fun ensureCategoryExistsInPublicCategories(categoryId: String, userId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 检查类别是否在 public.categories 表中存在: $categoryId")

            // 检查类别是否已存在
            val categoryCount = supabaseClient.from("categories").select(columns = Columns.list("id")) {
                filter {
                    eq("id", categoryId)
                }
                limit(1)
            }.decodeList<Map<String, String>>().size

            if (categoryCount > 0) {
                Log.d(TAG, "✅ 类别已存在于 public.categories 表中")
                return@withContext Result.success(Unit)
            }

            Log.d(TAG, "🔄 类别不存在，从 book_categories 获取数据并创建 public.categories 记录...")

            // 从 book_categories 表获取类别信息
            val bookCategory = supabaseClient.from("book_categories").select {
                filter {
                    eq("id", categoryId)
                    eq("user_id", userId)
                }
            }.decodeSingleOrNull<SupabaseTallyCategoryDto>()

            if (bookCategory == null) {
                val error = Exception("类别在 book_categories 表中不存在: $categoryId")
                Log.e(TAG, "❌ ${error.message}")
                return@withContext Result.failure(error)
            }

            // 创建 categories 表记录
            val categoryData = SupabaseCategoryInsert(
                id = bookCategory.id,
                user_id = bookCategory.user_id,
                book_id = bookCategory.book_id,
                parent_id = bookCategory.parent_id,
                name = bookCategory.name,
                icon_name = bookCategory.icon_name ?: "category1",
                type = bookCategory.type, // 直接使用字符串类型
                sort_order = bookCategory.sort_order,
                is_deleted = false
            )

            supabaseClient.from("categories").insert(categoryData)

            Log.d(TAG, "✅ 成功创建 public.categories 记录: $categoryId")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 确保类别存在失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 确保用户在 public.users 表中存在
     */
    suspend fun ensureUserExistsInPublicUsers(userId: String, userName: String = "用户"): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔍 检查用户是否在 public.users 表中存在: $userId")

            // 检查用户是否已存在 - 使用简单的计数查询避免序列化问题
            val userCount = supabaseClient.from("users").select(columns = Columns.list("id")) {
                filter {
                    eq("id", userId)
                }
                limit(1)
            }.decodeList<Map<String, String>>().size

            if (userCount > 0) {
                Log.d(TAG, "✅ 用户已存在于 public.users 表中")
                return@withContext Result.success(Unit)
            }

            Log.d(TAG, "🔄 用户不存在，开始创建 public.users 记录...")

            // 创建用户记录 - 使用专门的序列化数据类
            val currentTime = System.currentTimeMillis()
            val userData = SupabaseUserInsert(
                id = userId,
                name = userName,
                time_expire = currentTime + 365L * 24 * 60 * 60 * 1000, // 1年后过期
                time_create = currentTime,
                time_modify = currentTime,
                is_deleted = false,
                is_sync = true
            )

            supabaseClient.from("users").insert(userData)

            Log.d(TAG, "✅ 成功创建 public.users 记录: $userId")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 确保用户存在失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 从Supabase获取账本的账单数据
     */
    suspend fun getBillsFromSupabase(bookId: String, userId: String): Result<List<SupabaseBillDto>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 从Supabase获取账单数据: bookId=$bookId, userId=$userId")

            val response = supabaseClient.from("bills").select(
                columns = Columns.list(
                    "id", "user_id", "book_id", "type", "origin_bill_id", "time",
                    "category_id", "account_id", "transfer_target_account_id", "amount",
                    "note", "is_not_calculate", "time_create", "time_modify",
                    "is_deleted", "is_sync"
                )
            ) {
                filter {
                    eq("book_id", bookId)
                    eq("user_id", userId)
                    // 移除 is_deleted 过滤，获取所有账单（包括已删除的）
                }
                order("time", Order.DESCENDING)
                limit(1000) // 限制一次最多获取1000条
            }.decodeList<SupabaseBillDto>()

            Log.d(TAG, "✅ 从Supabase获取账单成功: 共${response.size}条账单")
            Result.success(response)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 从Supabase获取账单失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 从Supabase删除账单（硬删除）
     */
    suspend fun deleteBillFromSupabase(billId: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🗑️ 开始从Supabase删除账单: $billId")

            supabaseClient.from("bills").delete {
                filter {
                    eq("id", billId)
                }
            }

            Log.d(TAG, "✅ 成功从Supabase删除账单: $billId")
            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ 从Supabase删除账单失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 创建用户账本到Supabase
     */
    suspend fun createUserBook(
        userId: String,
        bookId: String,
        bookName: String?,
        iconName: String? = "book1",
        isSystem: Boolean = false
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "📚 开始创建用户账本到Supabase: bookName=$bookName, bookId=$bookId")

            val currentTime = System.currentTimeMillis()

            // 创建账本数据
            val bookData = SupabaseUserBookInsert(
                id = bookId,
                user_id = userId,
                name = bookName ?: "我的账本",
                icon_name = iconName ?: "book1",
                is_system = isSystem,
                is_deleted = false
            )

            // 插入到user_books表
            supabaseClient.from("user_books").insert(bookData)

            Log.d(TAG, "✅ 成功创建用户账本到Supabase: $bookName")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 创建用户账本到Supabase失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 为指定账本创建默认分类（包含完整的50个分类）
     */
    suspend fun createDefaultCategoriesForBook(
        userId: String,
        bookId: String
    ): Result<Int> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🏷️ 开始为账本创建默认分类: bookId=$bookId, userId=$userId")

            // 调用 Supabase RPC 函数
            val response = SupabaseConfig.database.rpc(
                function = "create_default_categories_for_book_enhanced",
                parameters = mapOf(
                    "p_user_id" to userId,
                    "p_book_id" to bookId
                )
            )

            // 解析响应获取创建的分类数量
            val categoryCount = Json.parseToJsonElement(response.data).jsonPrimitive.content.toInt()

            Log.d(TAG, "✅ 成功为账本创建默认分类: 共${categoryCount}个分类")
            Result.success(categoryCount)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 为账本创建默认分类失败: ${e.message}", e)
            Result.failure(e)
        }
    }

    /**
     * 智能同步记账到 Supabase（自动判断插入或更新）
     */
    suspend fun upsertBillInSupabase(bill: com.xiaojinzi.tally.lib.res.model.tally.TallyBillDto): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步账单到 Supabase")
            Log.d(TAG, "📋 账单信息: ID=${bill.id}, 备注=${bill.note ?: "无备注"}, 金额=${bill.amount.value}")
            Log.d(TAG, "📋 账单详情: 用户=${bill.userId}, 账本=${bill.bookId}, 类型=${bill.type.value}")
            Log.d(TAG, "🗑️ 删除状态: isDeleted=${bill.isDeleted}, isSync=${bill.isSync}")

            // 数据验证
            if (bill.id.isBlank()) {
                val error = IllegalArgumentException("账单ID不能为空")
                Log.e(TAG, "❌ 数据验证失败: 账单ID为空", error)
                return@withContext Result.failure(error)
            }

            if (bill.userId.isBlank()) {
                val error = IllegalArgumentException("用户ID不能为空")
                Log.e(TAG, "❌ 数据验证失败: 用户ID为空", error)
                return@withContext Result.failure(error)
            }

            // 确保用户在 public.users 表中存在（修复外键约束问题）
            Log.d(TAG, "🔧 确保用户在 public.users 表中存在...")
            ensureUserExistsInPublicUsers(bill.userId, "用户").getOrElse { error ->
                Log.e(TAG, "❌ 用户同步失败: ${error.message}", error)
                return@withContext Result.failure(Exception("用户同步失败，无法插入账单: ${error.message}", error))
            }

            // 确保账本在 public.books 表中存在（修复外键约束问题）
            Log.d(TAG, "🔧 确保账本在 public.books 表中存在...")
            ensureBookExistsInPublicBooks(bill.bookId, bill.userId).getOrElse { error ->
                Log.e(TAG, "❌ 账本同步失败: ${error.message}", error)
                return@withContext Result.failure(Exception("账本同步失败，无法插入账单: ${error.message}", error))
            }

            // 确保类别在 public.categories 表中存在（修复外键约束问题）
            bill.categoryId?.let { categoryId ->
                Log.d(TAG, "🔧 确保类别在 public.categories 表中存在...")
                ensureCategoryExistsInPublicCategories(categoryId, bill.userId).getOrElse { error ->
                    Log.e(TAG, "❌ 类别同步失败: ${error.message}", error)
                    return@withContext Result.failure(Exception("类别同步失败，无法插入账单: ${error.message}", error))
                }
            }

            Log.d(TAG, "✅ 数据验证通过，开始构建同步数据")

            // 构建要同步的数据 - 使用专门的序列化数据类，避免序列化错误
            val billData = SupabaseBillInsert(
                id = bill.id,
                user_id = bill.userId,
                book_id = bill.bookId,
                type = bill.type.value, // 使用 .value 获取字符串值
                origin_bill_id = bill.originBillId,
                time = bill.time,
                category_id = bill.categoryId,
                account_id = bill.accountId,
                transfer_target_account_id = bill.transferTargetAccountId,
                amount = bill.amount.value, // 使用 .value 获取 Long 值
                note = bill.note,
                is_not_calculate = bill.isNotCalculate,
                time_create = bill.timeCreate,
                time_modify = bill.timeModify,
                time_modify_format = null, // 由数据库触发器自动生成
                is_deleted = bill.isDeleted,
                is_sync = bill.isSync
            )

            Log.d(TAG, "📤 准备发送数据到 Supabase bills 表")
            Log.d(TAG, "🔗 使用客户端: ${supabaseClient.supabaseUrl}")

            // 使用 upsert 操作（如果存在则更新，不存在则插入）
            val startTime = System.currentTimeMillis()
            supabaseClient.from("bills").upsert(billData)
            val duration = System.currentTimeMillis() - startTime

            Log.d(TAG, "✅ Supabase 账单同步成功")
            Log.d(TAG, "📊 同步统计: 耗时=${duration}ms, 账单ID=${bill.id}")
            Log.d(TAG, "🎯 同步完成: ${bill.note ?: "无备注"}")

            Result.success(Unit)

        } catch (e: Exception) {
            Log.e(TAG, "❌ Supabase 账单同步失败", e)
            Log.e(TAG, "💥 错误详情: ${e.javaClass.simpleName} - ${e.message}")
            Log.e(TAG, "📋 失败账单: ID=${bill.id}, 备注=${bill.note ?: "无备注"}")

            // 记录详细的错误信息便于调试
            when (e) {
                is kotlinx.serialization.SerializationException -> {
                    Log.e(TAG, "🔧 序列化错误: 数据格式问题", e)
                }
                is io.ktor.client.network.sockets.ConnectTimeoutException -> {
                    Log.e(TAG, "🌐 网络超时: 连接 Supabase 超时", e)
                }
                is io.ktor.client.plugins.HttpRequestTimeoutException -> {
                    Log.e(TAG, "⏰ 请求超时: HTTP 请求超时", e)
                }
                else -> {
                    Log.e(TAG, "🚨 未知错误类型: ${e.javaClass.name}", e)
                }
            }

            Result.failure(Exception("账单同步到 Supabase 失败: ${e.message}", e))
        }
    }
}

// ==================== 数据传输对象 ====================

/**
 * 用户账本插入数据传输对象
 */
@Serializable
data class SupabaseUserBookInsert(
    val id: String,
    val user_id: String,
    val name: String,
    val icon_name: String,
    val is_system: Boolean = false,
    val is_deleted: Boolean = false
)

/**
 * 账本数据传输对象（Supabase 版本）
 */
@Serializable
data class SupabaseTallyBookDto(
    val id: String = "",
    val user_id: String,
    val is_system: Boolean = false,
    val name: String? = null,
    val icon_name: String? = null,
    val created_at: String? = null,
    val updated_at: String? = null,
    val is_deleted: Boolean = false
) {
    fun toStandardBookDto() = com.xiaojinzi.tally.lib.res.model.tally.TallyBookDto(
        id = id,
        userId = user_id,
        isSystem = is_system,
        type = "normal", // 默认类型
        name = name,
        iconName = icon_name,
        timeCreate = System.currentTimeMillis(), // 简化：使用当前时间
        timeModify = System.currentTimeMillis()
    )
}

/**
 * 同步结果数据类
 */
data class SyncResult(
    val syncedCategories: Int,
    val totalCategories: Int,
    val syncedAccounts: Int = 0,
    val totalAccounts: Int = 0
)

/**
 * Supabase 类别数据传输对象（匹配 book_categories 表结构）
 */
@Serializable
data class SupabaseTallyCategoryDto(
    val id: String = "",
    val user_id: String, // 恢复为必需字段，因为数据中确实有
    val book_id: String,
    val parent_id: String? = null,
    val name: String,
    val type: String, // "income" 或 "spending"
    val icon_name: String? = null,
    val color: String? = null,
    val sort_order: Int = 0,
    val is_system: Boolean = false,
    val is_deleted: Boolean = false,
    val created_at: String? = null,
    val updated_at: String? = null
) {
    fun toStandardCategoryDto() = com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto(
        id = id,
        userId = user_id,
        bookId = book_id,
        parentId = parent_id,
        type = when(type) {
            "spending" -> com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto.Companion.TallyCategoryType.SPENDING
            "income" -> com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto.Companion.TallyCategoryType.INCOME
            else -> com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryDto.Companion.TallyCategoryType.SPENDING
        },
        iconName = icon_name ?: "category1",
        name = name,
        sort = System.currentTimeMillis() - sort_order.toLong(), // 转换排序逻辑
        timeCreate = System.currentTimeMillis(),
        timeModify = System.currentTimeMillis(),
        isDeleted = is_deleted,
        isSync = true
    )
}

/**
 * 类别数据传输对象（保持兼容性）
 */
@Serializable
data class TallyCategoryDto(
    val id: String = "",
    val name: String,
    val icon: String,
    val type: String, // "SPENDING" 或 "INCOME"
    val sort: Int = 0,
    val bookId: String,
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis(),
    val isDeleted: Boolean = false
) {
    fun toCategoryDto() = TallyCategoryDto(
        id = id,
        name = name,
        icon = icon,
        type = type,
        sort = sort,
        bookId = bookId,
        createdTime = createdTime,
        updatedTime = updatedTime,
        isDeleted = isDeleted
    )
}

/**
 * 账户数据传输对象
 */
@Serializable
data class TallyAccountDto(
    val id: String = "",
    val name: String,
    val icon: String = "💰",
    val type: String = "CASH", // 账户类型
    val balance: Double = 0.0,
    val sort: Int = 0,
    val bookId: String,
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis(),
    val isDeleted: Boolean = false
)

/**
 * 用户配置数据传输对象
 */
@Serializable
data class UserConfigDto(
    val id: String = "",
    val userId: String,
    val currentBookId: String? = null,
    val theme: String = "AUTO",
    val language: String = "zh-CN",
    val createdTime: Long = System.currentTimeMillis(),
    val updatedTime: Long = System.currentTimeMillis()
) {
    fun toConfigDto() = UserConfigDto(
        id = id,
        userId = userId,
        currentBookId = currentBookId,
        theme = theme,
        language = language,
        createdTime = createdTime,
        updatedTime = updatedTime
    )

}