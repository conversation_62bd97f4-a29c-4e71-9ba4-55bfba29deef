package com.xiaojinzi.tally.module.core.module.book_crud.domain

import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.reactive.template.domain.BusinessUseCase
import com.xiaojinzi.reactive.template.domain.BusinessUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.reactive.template.domain.CommonUseCaseImpl
import com.xiaojinzi.reactive.template.domain.DialogUseCase
import com.xiaojinzi.support.annotation.StateHotObservable
import com.xiaojinzi.support.annotation.ViewModelLayer
import com.xiaojinzi.support.ktx.MutableSharedStateFlow
import com.xiaojinzi.support.ktx.orNull
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.tally.lib.res.model.tally.TallyRemoteBookTypeResDto
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.core.supabase.SupabaseDataService
import com.xiaojinzi.tally.lib.res.model.tally.TallyBookDto
import com.xiaojinzi.tally.lib.res.model.tally.TallyBookNecessaryInfoResDto
import android.util.Log
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.util.UUID

sealed class BookCrudIntent {

    data object Submit : BookCrudIntent()

    data class ItemSelect(
        val item: TallyRemoteBookTypeResDto,
    ) : BookCrudIntent()

}

@ViewModelLayer
interface BookCrudUseCase : BusinessUseCase {

    /**
     * 账本名字
     */
    @StateHotObservable
    val bookNameStateOb: MutableSharedStateFlow<String>

    /**
     * 账本列表
     */
    @StateHotObservable
    val bookTypeListStateOb: Flow<List<TallyRemoteBookTypeResDto>>

    /**
     * 选择的账本类型
     */
    @StateHotObservable
    val bookTypeSelectedStateOb: Flow<TallyRemoteBookTypeResDto?>

}

@ViewModelLayer
class BookCrudUseCaseImpl(
    private val commonUseCase: CommonUseCase = CommonUseCaseImpl(),
) : BusinessUseCaseImpl(
    commonUseCase = commonUseCase,
), BookCrudUseCase {

    override val bookNameStateOb = MutableSharedStateFlow(
        initValue = "",
    )

    override val bookTypeListStateOb = MutableSharedStateFlow<List<TallyRemoteBookTypeResDto>>(
        initValue = emptyList(),
    )

    override val bookTypeSelectedStateOb = MutableSharedStateFlow<TallyRemoteBookTypeResDto?>(
        initValue = null,
    )

    override suspend fun initData() {
        super.initData()
        
        // 检查是否为开源版本
        val isOpenSource = AppServices.appInfoSpi.forOpenSource
        
        if (isOpenSource) {
            // 开源版本：提供默认的账本类型
            val defaultBookType = TallyRemoteBookTypeResDto(
                type = TallyBookDto.TYPE_NORMAL,
                name = "普通账本",
                iconName = "book1",
                desc = "普通账本类型"
            )
            bookTypeListStateOb.emit(value = listOf(defaultBookType))
            bookTypeSelectedStateOb.emit(value = defaultBookType)
        } else {
            // 非开源版本：从网络获取账本类型列表
            try {
                bookTypeListStateOb.emit(
                    value = AppServices.appNetworkSpi.getBookTypeList(),
                )
            } catch (e: Exception) {
                Log.e("BookCrudUseCase", "获取账本类型列表失败: ${e.message}", e)
                // 提供默认类型作为备选
                val defaultBookType = TallyRemoteBookTypeResDto(
                    type = TallyBookDto.TYPE_NORMAL,
                    name = "普通账本",
                    iconName = "book1",
                    desc = "普通账本类型"
                )
                bookTypeListStateOb.emit(value = listOf(defaultBookType))
            }
        }
    }

    @IntentProcess
    private suspend fun itemSelect(intent: BookCrudIntent.ItemSelect) {
        bookTypeSelectedStateOb.emit(
            value = intent.item,
        )
    }

    @BusinessUseCase.AutoLoading
    @IntentProcess
    private suspend fun submit(intent: BookCrudIntent.Submit) {
        val name = bookNameStateOb.firstOrNull().orNull()
        val targetBookName = name.orNull()
        if (targetBookName.isNullOrBlank()) {
            tip(content = "请填写账本名称".toStringItemDto())
            return
        }

        try {
            // 检查是否为开源版本
            val isOpenSource = AppServices.appInfoSpi.forOpenSource
            
            val bookNecessaryInfo = if (isOpenSource) {
                // 开源版本：直接创建账本到本地和Supabase
                Log.d("BookCrudUseCase", "🏗️ 开源版本创建账本: $targetBookName")

                val userId = AppServices.userSpi.requiredLastUserId()
                val bookId = UUID.randomUUID().toString()
                val currentTime = System.currentTimeMillis()

                // 创建账本对象
                val newBook = TallyBookDto(
                    id = bookId,
                    userId = userId,
                    isSystem = false,
                    type = TallyBookDto.TYPE_NORMAL,
                    name = targetBookName,
                    iconName = "book1",
                    timeCreate = currentTime,
                    timeModify = currentTime
                )

                // 构造返回对象（先不创建分类，等账本插入数据库后再创建）
                TallyBookNecessaryInfoResDto(
                    book = newBook,
                    categoryList = emptyList() // 注意：这里仍然返回空列表，因为分类会在账本插入后创建
                )
            } else {
                // 非开源版本：使用原有逻辑
                val typeItem = bookTypeSelectedStateOb.firstOrNull()
                if (typeItem == null) {
                    tip(content = "请选择账本类型".toStringItemDto())
                    return
                }
                
                AppServices
                    .appNetworkSpi
                    .createBook(
                        type = typeItem.type,
                        name = targetBookName,
                    )
            }
            
            // 插入到数据库
            AppServices
                .tallyDataSourceSpi
                .insertBookNecessaryInfo(
                    bookNecessaryInfo = bookNecessaryInfo,
                )

            Log.d("BookCrudUseCase", "✅ 账本创建成功: ${bookNecessaryInfo.book.name}")

            // 如果是开源版本，在账本插入数据库后同步到Supabase并创建默认分类
            if (isOpenSource) {
                // 1. 先同步账本到Supabase
                Log.d("BookCrudUseCase", "📚 开始同步账本到Supabase...")
                try {
                    SupabaseDataService.createUserBook(
                        userId = bookNecessaryInfo.book.userId,
                        bookId = bookNecessaryInfo.book.id,
                        bookName = bookNecessaryInfo.book.name,
                        iconName = bookNecessaryInfo.book.iconName ?: "book1",
                        isSystem = bookNecessaryInfo.book.isSystem
                    ).getOrThrow()
                    Log.d("BookCrudUseCase", "✅ 账本同步到Supabase成功")
                } catch (e: Exception) {
                    Log.e("BookCrudUseCase", "❌ 账本同步到Supabase失败: ${e.message}")
                    throw e // 如果账本同步失败，不继续创建分类
                }

                // 2. 再创建默认分类
                Log.d("BookCrudUseCase", "🏷️ 开始为新账本创建默认分类...")
                try {
                    val categoryCount = SupabaseDataService.createDefaultCategoriesForBook(
                        userId = bookNecessaryInfo.book.userId,
                        bookId = bookNecessaryInfo.book.id
                    ).getOrThrow()
                    Log.d("BookCrudUseCase", "✅ 成功创建默认分类: 共${categoryCount}个")

                    // 3. 同步分类到本地数据库
                    Log.d("BookCrudUseCase", "🔄 开始同步分类到本地数据库...")
                    try {
                        val syncResult = SupabaseDataService.syncBookDataToLocal(bookNecessaryInfo.book.id)
                        if (syncResult.isSuccess) {
                            Log.d("BookCrudUseCase", "✅ 分类同步到本地成功")
                        } else {
                            Log.w("BookCrudUseCase", "⚠️ 分类同步到本地失败: ${syncResult.exceptionOrNull()?.message}")
                        }
                    } catch (e: Exception) {
                        Log.w("BookCrudUseCase", "⚠️ 分类同步到本地异常: ${e.message}")
                    }
                } catch (e: Exception) {
                    Log.w("BookCrudUseCase", "⚠️ 创建默认分类失败: ${e.message}")
                }
            }

            tip(content = "创建成功".toStringItemDto())
            
            // 询问是否切换到新账本
            when (
                confirmDialog(
                    content = "是否切换到新创建的账本?".toStringItemDto(),
                    positive = "切换".toStringItemDto(),
                    negative = "不切换".toStringItemDto(),
                )
            ) {
                DialogUseCase.ConfirmDialogResultType.CONFIRM -> {
                    AppServices
                        .tallyDataSourceSpi
                        .switchBook(
                            bookId = bookNecessaryInfo.book.id,
                            isTipAfterSwitch = true,
                        )
                }
                else -> {}
            }
            
            // 销毁界面
            postActivityFinishEvent()
            
        } catch (e: Exception) {
            Log.e("BookCrudUseCase", "❌ 创建账本失败: ${e.message}", e)
            tip(content = "创建失败: ${e.message}".toStringItemDto())
        }
    }

    override fun destroy() {
        super.destroy()
        commonUseCase.destroy()
    }

}