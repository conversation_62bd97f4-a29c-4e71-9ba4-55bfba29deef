package com.xiaojinzi.tally.module.core.module.first_sync.domain

import android.content.Context
import androidx.annotation.UiContext
import com.xiaojinzi.component.impl.routeApi
import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.reactive.template.domain.BusinessUseCase
import com.xiaojinzi.reactive.template.domain.BusinessUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.reactive.template.domain.CommonUseCaseImpl
import com.xiaojinzi.support.annotation.StateHotObservable
import com.xiaojinzi.support.annotation.ViewModelLayer
import com.xiaojinzi.support.ktx.MutableSharedStateFlow
import com.xiaojinzi.support.ktx.timeAtLeast
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.support.ktx.tryFinishActivity
import com.xiaojinzi.tally.lib.res.model.exception.NotLoggedInException
import com.xiaojinzi.tally.module.base.support.AppRouterMainApi
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.base.support.DevelopHelper
import com.xiaojinzi.tally.module.core.supabase.UserDataInitializer
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

sealed class FirstSyncIntent {

    data class StartInit(
        @UiContext val context: Context,
    ) : FirstSyncIntent()

}

@ViewModelLayer
interface FirstSyncUseCase : BusinessUseCase {

    enum class FirstSyncState {
        // 正在同步
        SYNCING,

        // 同步成功
        SYNC_SUCCESS,

        // 同步失败
        SYNC_FAIL,
    }

    @StateHotObservable
    val syncStateStateOb: Flow<FirstSyncState>

}

@ViewModelLayer
class FirstSyncUseCaseImpl(
    private val commonUseCase: CommonUseCase = CommonUseCaseImpl(),
) : BusinessUseCaseImpl(
    commonUseCase = commonUseCase,
), FirstSyncUseCase {

    override val syncStateStateOb = MutableSharedStateFlow(
        initValue = FirstSyncUseCase.FirstSyncState.SYNCING,
    )

    @IntentProcess
    private suspend fun startInit(intent: FirstSyncIntent.StartInit) {
        syncStateStateOb.emit(
            value = FirstSyncUseCase.FirstSyncState.SYNCING,
        )
        val result = runCatching {
            timeAtLeast(timeMillis = 3000) {
                val userInfo =
                    AppServices.userSpi.userInfoStateOb.firstOrNull() ?: throw NotLoggedInException()

                // 先进行 Supabase 数据同步
                try {
                    println("[FirstSync] 🔄 开始 Supabase 数据同步...")
                    val result = UserDataInitializer.ensureUserDataInitialized(userInfo.id, "用户")
                    if (result.isSuccess && result.getOrNull() == true) {
                        println("[FirstSync] ✅ Supabase 数据同步完成")
                    } else {
                        println("[FirstSync] ⚠️ Supabase 数据同步失败，继续本地同步")
                    }
                } catch (e: Exception) {
                    println("[FirstSync] ⚠️ Supabase 数据同步失败，继续本地同步: ${e.message}")
                }

                // 进行本地数据同步（兜底方案）
                AppServices
                    .tallyDataSourceSpi
                    .syncFirstData(
                        userId = userInfo.id,
                    )
            }
        }
        if (DevelopHelper.isDevelop) {
            result.exceptionOrNull()?.printStackTrace()
        }
        if (result.isSuccess) {
            tip(content = "同步成功".toStringItemDto())
            // 去主界面
            AppRouterMainApi::class
                .routeApi()
                .toMainView(
                    context = intent.context,
                ) {
                    intent.context.tryFinishActivity()
                }
        } else {
            syncStateStateOb.emit(
                value = FirstSyncUseCase.FirstSyncState.SYNC_FAIL,
            )
        }
    }

    override fun destroy() {
        super.destroy()
        commonUseCase.destroy()
    }

}