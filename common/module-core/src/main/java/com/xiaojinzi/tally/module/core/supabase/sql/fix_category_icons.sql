-- =====================================================
-- 修复分类图标脚本
-- 修复出行交通、休闲娱乐、健康医疗的图标问题
-- =====================================================

-- 1. 修复主类别图标
UPDATE book_categories 
SET 
    icon_name = 'taxi1',
    updated_at = NOW()
WHERE 
    name = '出行交通' 
    AND type = 'spending'
    AND parent_id IS NULL
    AND icon_name = 'car1'
    AND is_deleted = false;

UPDATE book_categories 
SET 
    icon_name = 'gamePad1',
    updated_at = NOW()
WHERE 
    name = '休闲娱乐' 
    AND type = 'spending'
    AND parent_id IS NULL
    AND icon_name = 'gamepad1'
    AND is_deleted = false;

UPDATE book_categories
SET
    icon_name = 'firstAidKit1',
    updated_at = NOW()
WHERE
    name = '健康医疗'
    AND type = 'spending'
    AND parent_id IS NULL
    AND icon_name = 'medical1'
    AND is_deleted = false;

UPDATE book_categories
SET
    icon_name = 'invest1',
    updated_at = NOW()
WHERE
    name = '投资收益'
    AND type = 'income'
    AND parent_id IS NULL
    AND icon_name = 'investment1'
    AND is_deleted = false;

-- 2. 修复子类别图标
UPDATE book_categories 
SET 
    icon_name = 'gamePad1',
    updated_at = NOW()
WHERE 
    name = '游戏娱乐' 
    AND type = 'spending'
    AND parent_id IS NOT NULL
    AND icon_name = 'gamepad1'
    AND is_deleted = false;

-- 3. 验证修复结果
SELECT
    name,
    type,
    icon_name,
    CASE WHEN parent_id IS NULL THEN '主类别' ELSE '子类别' END as category_level,
    updated_at
FROM book_categories
WHERE
    name IN ('出行交通', '休闲娱乐', '健康医疗', '游戏娱乐', '投资收益')
    AND is_deleted = false
ORDER BY name, parent_id NULLS FIRST;

-- 4. 检查修复数量
SELECT
    '修复完成' as status,
    COUNT(*) as total_fixed
FROM book_categories
WHERE
    (
        (name = '出行交通' AND icon_name = 'taxi1') OR
        (name = '休闲娱乐' AND icon_name = 'gamePad1') OR
        (name = '健康医疗' AND icon_name = 'firstAidKit1') OR
        (name = '游戏娱乐' AND icon_name = 'gamePad1') OR
        (name = '投资收益' AND icon_name = 'invest1')
    )
    AND is_deleted = false;
