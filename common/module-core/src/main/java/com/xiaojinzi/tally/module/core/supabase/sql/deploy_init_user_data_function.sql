-- =====================================================
-- 部署更新后的 initialize_user_data 函数
-- 包含修复后的图标
-- =====================================================

-- 删除旧函数（如果存在）
DROP FUNCTION IF EXISTS initialize_user_data(TEXT, TEXT);

-- 重新创建函数（从 init_user_data.sql 复制完整内容）
-- 注意：请将 init_user_data.sql 的完整内容复制到这里

-- 验证函数是否创建成功
SELECT 
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE proname = 'initialize_user_data';

-- 测试函数（可选 - 请替换为实际的用户ID和用户名）
-- SELECT initialize_user_data('test-user-id', 'Test User');
