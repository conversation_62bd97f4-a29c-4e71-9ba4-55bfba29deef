package com.xiaojinzi.tally.module.core.module.book_info.view

import androidx.annotation.DrawableRes
import androidx.annotation.Keep
import com.xiaojinzi.support.bean.StringItemDto
import com.xiaojinzi.tally.lib.res.model.support.LocalImageItemDto
import com.xiaojinzi.tally.lib.res.model.tally.MoneyYuan
import com.xiaojinzi.tally.lib.res.model.user.UserInfoCacheDto

@Keep
data class BookInfoItemVo(
    val bookId: String,
    val icon: LocalImageItemDto?,
    val bookName: StringItemDto?,
    val totalSpending: MoneyYuan,
    val totalIncome: MoneyYuan,
    val billCount: Long,
    val isSystem: Boolean,
    val userInfo: UserInfoCacheDto?,
    val timeCreate: Long,
    // 删除相关字段
    val isPendingDelete: Boolean = false,
    val deleteRequestedAt: Long? = null,
) {
    /**
     * 获取删除剩余小时数
     */
    fun getDeleteRemainingHours(): Int? {
        if (!isPendingDelete || deleteRequestedAt == null) return null

        val currentTime = System.currentTimeMillis()
        val elapsedHours = (currentTime - deleteRequestedAt) / (1000 * 60 * 60)
        val remainingHours = 24 - elapsedHours.toInt()

        return if (remainingHours > 0) remainingHours else 0
    }
}