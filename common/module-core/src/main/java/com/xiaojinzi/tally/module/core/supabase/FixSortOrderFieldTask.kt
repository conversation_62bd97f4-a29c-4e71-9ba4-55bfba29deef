package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

/**
 * 修复 categories 表 sort_order 字段缺失问题的任务执行器
 * 
 * 问题描述：应用日志显示 "Could not find the 'sort_order' column of 'categories' in the schema cache"
 * 解决方案：使用 MCP 服务为 categories 表添加 sort_order 字段并创建同步触发器
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-28
 */
class FixSortOrderFieldTask {
    
    companion object {
        private const val TAG = "FixSortOrderFieldTask"
        
        /**
         * 获取单例实例
         */
        fun getInstance(): FixSortOrderFieldTask {
            return FixSortOrderFieldTask()
        }
    }
    
    /**
     * 执行完整的修复流程
     */
    suspend fun executeFixProcess(): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始修复 categories 表 sort_order 字段问题 ==========")
            
            val report = StringBuilder()
            report.appendLine("修复 categories 表 sort_order 字段问题报告")
            report.appendLine("执行时间: ${System.currentTimeMillis()}")
            report.appendLine()
            
            // 步骤 1: 初始化 MCP 服务
            Log.d(TAG, "🚀 步骤 1: 初始化 SupabaseMcpService")
            val initResult = initializeMcpService()
            if (initResult.isFailure) {
                val error = "MCP 服务初始化失败: ${initResult.exceptionOrNull()?.message}"
                Log.e(TAG, "❌ $error")
                report.appendLine("❌ $error")
                return@withContext Result.failure(Exception(error))
            }
            
            Log.d(TAG, "✅ MCP 服务初始化成功")
            report.appendLine("✅ MCP 服务初始化成功")
            
            // 步骤 2: 检查当前表结构
            Log.d(TAG, "🔍 步骤 2: 检查 categories 表当前结构")
            val structureResult = checkTableStructure()
            structureResult.fold(
                onSuccess = { structure ->
                    Log.d(TAG, "✅ 表结构检查完成")
                    report.appendLine("✅ 表结构检查完成:")
                    report.appendLine(structure)
                },
                onFailure = { 
                    val error = "表结构检查失败: ${it.message}"
                    Log.e(TAG, "❌ $error")
                    report.appendLine("❌ $error")
                    return@withContext Result.failure(Exception(error))
                }
            )
            
            // 步骤 3: 执行修复脚本
            Log.d(TAG, "🔧 步骤 3: 执行 SQL 修复脚本")
            val fixResult = executeFixScript()
            fixResult.fold(
                onSuccess = { result ->
                    Log.d(TAG, "✅ 修复脚本执行成功")
                    report.appendLine("✅ 修复脚本执行成功:")
                    report.appendLine(result)
                },
                onFailure = { 
                    val error = "修复脚本执行失败: ${it.message}"
                    Log.e(TAG, "❌ $error")
                    report.appendLine("❌ $error")
                    return@withContext Result.failure(Exception(error))
                }
            )
            
            // 步骤 4: 验证修复结果
            Log.d(TAG, "✅ 步骤 4: 验证修复结果")
            val verifyResult = verifyFix()
            verifyResult.fold(
                onSuccess = { verification ->
                    Log.d(TAG, "✅ 修复验证成功")
                    report.appendLine("✅ 修复验证成功:")
                    report.appendLine(verification)
                },
                onFailure = { 
                    val error = "修复验证失败: ${it.message}"
                    Log.e(TAG, "❌ $error")
                    report.appendLine("❌ $error")
                    return@withContext Result.failure(Exception(error))
                }
            )
            
            Log.d(TAG, "========== categories 表 sort_order 字段修复完成 ==========")
            report.appendLine()
            report.appendLine("========== 修复完成 ==========")
            report.appendLine("下一步：重启应用并监控日志，确认错误已解决")
            
            Result.success(report.toString())
        } catch (e: Exception) {
            Log.e(TAG, "❌ 修复过程发生异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 步骤 1: 初始化 MCP 服务
     */
    private suspend fun initializeMcpService(): Result<Unit> {
        return try {
            Log.d(TAG, "获取 SupabaseMcpService 实例...")
            val mcpService = SupabaseMcpService.getInstance()
            
            Log.d(TAG, "调用 initialize() 方法...")
            val initResult = mcpService.initialize()
            
            initResult.fold(
                onSuccess = {
                    Log.d(TAG, "检查连接状态...")
                    val connectionState = mcpService.connectionState.value
                    Log.d(TAG, "当前连接状态: $connectionState")
                    
                    val currentProject = mcpService.currentProject
                    if (currentProject != null) {
                        Log.d(TAG, "当前项目信息:")
                        Log.d(TAG, "  - 项目名称: ${currentProject.name}")
                        Log.d(TAG, "  - 项目 URL: ${currentProject.url}")
                        Log.d(TAG, "  - 项目状态: ${currentProject.status}")
                    } else {
                        Log.w(TAG, "⚠️ 当前项目信息为空")
                    }
                    
                    Result.success(Unit)
                },
                onFailure = { exception ->
                    Log.e(TAG, "MCP 服务初始化失败: ${exception.message}", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "初始化过程发生异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 步骤 2: 检查表结构
     */
    private suspend fun checkTableStructure(): Result<String> {
        return try {
            val mcpService = SupabaseMcpService.getInstance()
            
            val sql = """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'categories'
                ORDER BY ordinal_position
            """.trimIndent()
            
            Log.d(TAG, "执行表结构查询...")
            mcpService.executeSql(sql).fold(
                onSuccess = { result ->
                    val report = StringBuilder()
                    report.appendLine("categories 表字段结构:")
                    
                    val hasSortOrder = result.rows?.any { row ->
                        row["column_name"] == "sort_order"
                    } ?: false
                    
                    result.rows?.forEach { row ->
                        val columnName = row["column_name"]
                        val dataType = row["data_type"]
                        val isNullable = row["is_nullable"]
                        val columnDefault = row["column_default"]
                        
                        report.appendLine("  - $columnName: $dataType (nullable: $isNullable, default: $columnDefault)")
                        
                        if (columnName == "sort") {
                            Log.d(TAG, "✅ 发现 sort 字段: $dataType")
                        }
                        if (columnName == "sort_order") {
                            Log.d(TAG, "✅ 发现 sort_order 字段: $dataType")
                        }
                    }
                    
                    report.appendLine()
                    if (hasSortOrder) {
                        report.appendLine("✅ sort_order 字段已存在")
                        Log.d(TAG, "✅ sort_order 字段已存在，无需修复")
                    } else {
                        report.appendLine("❌ sort_order 字段缺失，需要修复")
                        Log.d(TAG, "❌ 确认 sort_order 字段缺失")
                    }
                    
                    Result.success(report.toString())
                },
                onFailure = { exception ->
                    Log.e(TAG, "表结构查询失败: ${exception.message}", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "检查表结构时发生异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 步骤 3: 执行修复脚本
     */
    private suspend fun executeFixScript(): Result<String> {
        return try {
            val mcpService = SupabaseMcpService.getInstance()
            
            // 读取修复脚本内容
            val scriptPath = "fix_categories_sort_order.sql"
            val scriptContent = try {
                File(scriptPath).readText()
            } catch (e: Exception) {
                Log.e(TAG, "无法读取修复脚本文件: $scriptPath", e)
                return Result.failure(Exception("修复脚本文件不存在: $scriptPath"))
            }
            
            Log.d(TAG, "执行修复脚本，脚本长度: ${scriptContent.length} 字符")
            
            val startTime = System.currentTimeMillis()
            mcpService.executeSql(scriptContent).fold(
                onSuccess = { result ->
                    val executionTime = System.currentTimeMillis() - startTime
                    val report = StringBuilder()
                    report.appendLine("修复脚本执行结果:")
                    report.appendLine("  - 执行时间: ${executionTime}ms")
                    report.appendLine("  - 执行状态: ${if (result.success) "成功" else "失败"}")
                    
                    if (result.error != null) {
                        report.appendLine("  - 错误信息: ${result.error}")
                    }
                    
                    if (result.message != null) {
                        report.appendLine("  - 执行消息: ${result.message}")
                    }
                    
                    Log.d(TAG, "修复脚本执行完成，耗时: ${executionTime}ms")
                    Result.success(report.toString())
                },
                onFailure = { exception ->
                    Log.e(TAG, "修复脚本执行失败: ${exception.message}", exception)
                    Result.failure(exception)
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "执行修复脚本时发生异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 步骤 4: 验证修复结果
     */
    private suspend fun verifyFix(): Result<String> {
        return try {
            val mcpService = SupabaseMcpService.getInstance()
            val report = StringBuilder()
            
            // 验证 1: 检查 sort_order 字段是否存在
            Log.d(TAG, "验证 1: 检查 sort_order 字段...")
            val fieldCheckSql = """
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'categories' AND column_name = 'sort_order'
            """.trimIndent()
            
            mcpService.executeSql(fieldCheckSql).fold(
                onSuccess = { result ->
                    val fieldExists = result.rows?.isNotEmpty() ?: false
                    if (fieldExists) {
                        val dataType = result.rows?.firstOrNull()?.get("data_type")
                        report.appendLine("✅ sort_order 字段已存在，类型: $dataType")
                        Log.d(TAG, "✅ sort_order 字段验证通过")
                    } else {
                        report.appendLine("❌ sort_order 字段仍然缺失")
                        Log.e(TAG, "❌ sort_order 字段验证失败")
                        return Result.failure(Exception("sort_order 字段仍然缺失"))
                    }
                },
                onFailure = { 
                    Log.e(TAG, "字段验证查询失败: ${it.message}")
                    return Result.failure(it)
                }
            )
            
            // 验证 2: 检查数据同步情况
            Log.d(TAG, "验证 2: 检查数据同步...")
            val dataSyncSql = """
                SELECT id, sort, sort_order 
                FROM categories 
                WHERE sort_order IS NOT NULL
                LIMIT 5
            """.trimIndent()
            
            mcpService.executeSql(dataSyncSql).fold(
                onSuccess = { result ->
                    val recordCount = result.rows?.size ?: 0
                    report.appendLine("✅ 数据同步检查: $recordCount 条记录的 sort_order 字段已填充")
                    
                    result.rows?.forEach { row ->
                        val id = row["id"]
                        val sort = row["sort"]
                        val sortOrder = row["sort_order"]
                        Log.d(TAG, "记录 $id: sort=$sort, sort_order=$sortOrder")
                    }
                },
                onFailure = { 
                    Log.w(TAG, "数据同步检查失败: ${it.message}")
                    report.appendLine("⚠️ 数据同步检查失败: ${it.message}")
                }
            )
            
            // 验证 3: 检查索引是否创建
            Log.d(TAG, "验证 3: 检查索引...")
            val indexCheckSql = """
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'categories' AND indexname LIKE '%sort_order%'
            """.trimIndent()
            
            mcpService.executeSql(indexCheckSql).fold(
                onSuccess = { result ->
                    val indexExists = result.rows?.isNotEmpty() ?: false
                    if (indexExists) {
                        val indexName = result.rows?.firstOrNull()?.get("indexname")
                        report.appendLine("✅ 索引已创建: $indexName")
                        Log.d(TAG, "✅ 索引验证通过: $indexName")
                    } else {
                        report.appendLine("⚠️ 未找到 sort_order 相关索引")
                        Log.w(TAG, "⚠️ 未找到 sort_order 相关索引")
                    }
                },
                onFailure = { 
                    Log.w(TAG, "索引检查失败: ${it.message}")
                    report.appendLine("⚠️ 索引检查失败: ${it.message}")
                }
            )
            
            Result.success(report.toString())
        } catch (e: Exception) {
            Log.e(TAG, "验证修复结果时发生异常: ${e.message}", e)
            Result.failure(e)
        }
    }
}
