package com.xiaojinzi.tally.module.core.supabase

import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.auth.Auth
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.postgrest
import io.ktor.client.plugins.HttpTimeout

object SupabaseConfig {
    
    /**
     * Supabase 客户端实例
     * 使用懒加载模式，确保在需要时才初始化
     * 移除虚拟客户端机制，让错误更透明可诊断
     */
    val client: SupabaseClient by lazy {
        println("[SupabaseConfig] ========== 开始初始化 Supabase 客户端 ==========")
        
        try {
            // println("[SupabaseConfig] 🔍 步骤 1: 验证网络连接...")
            // checkNetworkConnectivity()
            // println("[SupabaseConfig] ✅ 网络连接检查通过")
            
            println("[SupabaseConfig] 🔍 步骤 2: 验证配置参数...")
            validateConfiguration()
            println("[SupabaseConfig] ✅ 配置参数验证通过")
            
            println("[SupabaseConfig] 🔍 步骤 3: 创建 Supabase 客户端...")
            println("[SupabaseConfig]    URL: ${SupabaseConstants.SUPABASE_URL}")
            println("[SupabaseConfig]    Key length: ${SupabaseConstants.SUPABASE_ANON_KEY.length}")
            
            val client = createSupabaseClient(
                supabaseUrl = SupabaseConstants.SUPABASE_URL,
                supabaseKey = SupabaseConstants.SUPABASE_ANON_KEY
            ) {
                println("[SupabaseConfig] 🔧 安装 Auth 模块...")
                install(Auth)
                
                println("[SupabaseConfig] 🔧 安装 Postgrest 模块...")
                install(Postgrest)
                
                println("[SupabaseConfig] 🔧 跳过 HttpTimeout 配置 (Supabase 3.1.0 兼容性)...")
                // 注意: 在 Supabase 3.1.0 中，httpConfig 可能是内部 API，暂时移除以避免编译错误
                // 如果需要配置超时，可以考虑其他方式或升级到更新版本
                println("[SupabaseConfig] ✅ 跳过 HttpTimeout 配置完成")
            }
            
            println("[SupabaseConfig] ✅ Supabase 客户端创建成功")
            
            // 安全地获取客户端信息
            try {
                val clientType = client.javaClass.simpleName
                println("[SupabaseConfig] 🔗 客户端类型: $clientType")
            } catch (e: Exception) {
                println("[SupabaseConfig] ⚠️  无法获取客户端类型: ${e.message}")
            }
            
            try {
                val clientUrl = client.supabaseUrl
                println("[SupabaseConfig] 🌐 客户端 URL: $clientUrl")
            } catch (e: Exception) {
                println("[SupabaseConfig] ⚠️  无法获取客户端 URL: ${e.message}")
            }
            
            println("[SupabaseConfig] ========== 客户端初始化完成 ==========")
            
            client
        } catch (e: Exception) {
            println("[SupabaseConfig] ❌ ========== 客户端初始化失败 ==========")
            println("[SupabaseConfig] 🔍 异常类型: ${e.javaClass.simpleName}")
            println("[SupabaseConfig] 💬 异常消息: ${e.message}")
            println("[SupabaseConfig] 🔗 异常原因: ${e.cause?.javaClass?.simpleName} - ${e.cause?.message}")
            println("[SupabaseConfig] 📚 完整异常堆栈:")
            e.printStackTrace()
            
            // 创建更具体的错误信息
            val detailedMessage = buildString {
                append("Supabase 客户端初始化失败: ")
                
                when {
                    e.message?.contains("network", ignoreCase = true) == true ||
                    e.javaClass.simpleName.contains("Connect") ||
                    e.javaClass.simpleName.contains("Network") -> {
                        append("网络连接问题")
                        append("\n• 请检查网络连接是否正常")
                        append("\n• 确认设备能访问外网")
                        append("\n• 如果在模拟器中，检查模拟器网络设置")
                    }
                    e.message?.contains("invalid", ignoreCase = true) == true ||
                    e.message?.contains("unauthorized", ignoreCase = true) == true -> {
                        append("配置参数错误")
                        append("\n• 请检查 SUPABASE_URL 是否正确")
                        append("\n• 请检查 SUPABASE_ANON_KEY 是否有效")
                        append("\n• 确认配置信息来自正确的 Supabase 项目")
                    }
                    e.javaClass.simpleName == "IllegalStateException" -> {
                        append("应用状态异常")
                        append("\n• ${e.message}")
                    }
                    else -> {
                        append("未知错误: ${e.message}")
                        append("\n• 原始异常: ${e.javaClass.simpleName}")
                        if (e.cause != null) {
                            append("\n• 根本原因: ${e.cause?.message}")
                        }
                    }
                }
                
                append("\n\n🔧 故障排除步骤:")
                append("\n1. 检查网络连接")
                append("\n2. 验证 Supabase 配置")
                append("\n3. 查看上方详细日志")
                append("\n4. 联系开发者获取支持")
            }
            
            println("[SupabaseConfig] 👤 用户友好错误信息:")
            println(detailedMessage)
            println("[SupabaseConfig] ================================================")
            
            // 直接重新抛出异常，不创建虚拟客户端
            throw RuntimeException(detailedMessage, e)
        }
    }
    
    /**
     * 检查网络连接
     * 在创建 Supabase 客户端前确认网络可达性
     */
    private fun checkNetworkConnectivity() {
        try {
            // 检查是否能解析 Supabase 域名
            val supabaseHost = SupabaseConstants.SUPABASE_URL
                .removePrefix("https://")
                .removePrefix("http://")
                .substringBefore("/")
            
            // println("[SupabaseConfig] 🌐 检查域名解析: $supabaseHost")
            
            // 尝试解析域名
            // val address = java.net.InetAddress.getByName(supabaseHost)
            // println("[SupabaseConfig] ✅ 域名解析成功: ${address.hostAddress}")
            
        } catch (e: Exception) {
            println("[SupabaseConfig] ❌ 网络连接检查失败: ${e.message}")
            throw RuntimeException(
                "网络连接不可用，无法连接到 Supabase 服务器\n" +
                "• 请检查网络连接\n" +
                "• 确认设备能访问外网\n" +
                "• 如果使用 VPN，请检查 VPN 设置\n" +
                "技术详情: ${e.message}", e
            )
        }
    }
    
    /**
     * 验证 Supabase 配置
     */
    private fun validateConfiguration() {
        println("[SupabaseConfig] 🔍 开始配置验证...")
        
        // 基础配置检查
        println("[SupabaseConfig] 📋 当前配置信息:")
        println("[SupabaseConfig]    URL: ${SupabaseConstants.SUPABASE_URL}")
        println("[SupabaseConfig]    Key length: ${SupabaseConstants.SUPABASE_ANON_KEY.length}")
        println("[SupabaseConfig]    Key preview: ${SupabaseConstants.SUPABASE_ANON_KEY.take(20)}...")
        
        // 检查是否为占位符值
        val urlContainsPlaceholder = SupabaseConstants.SUPABASE_URL.contains("your-project-ref")
        val keyContainsPlaceholder = SupabaseConstants.SUPABASE_ANON_KEY.contains("your-supabase-anon-key") ||
                SupabaseConstants.SUPABASE_ANON_KEY == "demo-key-replace-with-your-actual-key"
        
        println("[SupabaseConfig] 🔍 占位符检查:")
        println("[SupabaseConfig]    URL 包含占位符: $urlContainsPlaceholder")
        println("[SupabaseConfig]    Key 包含占位符: $keyContainsPlaceholder")
        
        if (urlContainsPlaceholder || keyContainsPlaceholder) {
            throw IllegalStateException(
                "检测到配置占位符值，请设置真实的 Supabase 配置\n\n" +
                "🔧 配置步骤:\n" +
                "1. 访问 Supabase Dashboard (https://supabase.com/dashboard)\n" +
                "2. 选择你的项目\n" +
                "3. 进入 Settings > API\n" +
                "4. 复制 Project URL 和 anon public key\n" +
                "5. 更新 SupabaseConstants.kt 中的配置\n\n" +
                "❌ 当前问题:\n" +
                "• URL 包含占位符: $urlContainsPlaceholder\n" +
                "• Key 包含占位符: $keyContainsPlaceholder"
            )
        }
        
        // 检查空值
        if (SupabaseConstants.SUPABASE_URL.isBlank() || SupabaseConstants.SUPABASE_ANON_KEY.isBlank()) {
            throw IllegalStateException(
                "Supabase 配置不能为空\n" +
                "• URL 为空: ${SupabaseConstants.SUPABASE_URL.isBlank()}\n" +
                "• Key 为空: ${SupabaseConstants.SUPABASE_ANON_KEY.isBlank()}"
            )
        }
        
        // 验证 URL 格式
        println("[SupabaseConfig] 🔍 URL 格式验证...")
        val urlStartsWithHttps = SupabaseConstants.SUPABASE_URL.startsWith("https://")
        val urlContainsSupabase = SupabaseConstants.SUPABASE_URL.contains(".supabase.co")
        
        println("[SupabaseConfig]    以 https:// 开头: $urlStartsWithHttps")
        println("[SupabaseConfig]    包含 .supabase.co: $urlContainsSupabase")
        
        if (!urlStartsWithHttps || !urlContainsSupabase) {
            throw IllegalStateException(
                "Supabase URL 格式不正确\n\n" +
                "✅ 正确格式: https://your-project-id.supabase.co\n" +
                "❌ 当前 URL: ${SupabaseConstants.SUPABASE_URL}\n\n" +
                "问题分析:\n" +
                "• 以 https:// 开头: $urlStartsWithHttps\n" +
                "• 包含 .supabase.co: $urlContainsSupabase\n\n" +
                "请检查 SupabaseConstants.kt 中的 SUPABASE_URL 配置"
            )
        }
        
        // 验证 Key 格式 (JWT token 基本格式检查)
        println("[SupabaseConfig] 🔍 Key 格式验证...")
        val keyParts = SupabaseConstants.SUPABASE_ANON_KEY.split(".")
        val isJWTFormat = keyParts.size == 3 && keyParts.all { it.isNotBlank() }
        
        println("[SupabaseConfig]    JWT 格式检查: $isJWTFormat (部分数: ${keyParts.size})")
        
        if (!isJWTFormat) {
            throw IllegalStateException(
                "Supabase API Key 格式不正确\n\n" +
                "✅ API Key 应该是 JWT 格式 (包含 3 个由点分隔的部分)\n" +
                "❌ 当前 Key 部分数: ${keyParts.size}\n\n" +
                "请确认从 Supabase Dashboard > Settings > API 复制了正确的 anon public key"
            )
        }
        
        println("[SupabaseConfig] ✅ 配置验证完成")
    }
    
    /**
     * 获取认证模块
     */
    val auth get() = client.auth
    
    /**
     * 获取数据库模块
     */
    val database get() = client.postgrest
    
    /**
     * 检查客户端是否已初始化且正常工作
     */
    fun isInitialized(): Boolean {
        return try {
            println("[SupabaseConfig] 🔍 检查客户端初始化状态...")
            val url = client.supabaseUrl
            val isInitialized = url.isNotEmpty() && !url.contains("dummy")
            println("[SupabaseConfig] 客户端初始化状态: $isInitialized")
            println("[SupabaseConfig] 客户端 URL: $url")
            isInitialized
        } catch (e: Exception) {
            println("[SupabaseConfig] 客户端初始化检查失败: ${e.message}")
            false
        }
    }
    
    /**
     * 获取客户端状态信息（用于调试）
     */
    fun getClientStatus(): String {
        return try {
            buildString {
                append("Supabase 客户端状态报告\n")
                append("==========================\n")
                append("初始化状态: ${isInitialized()}\n")
                append("客户端类型: ${client.javaClass.simpleName}\n")
                append("客户端 URL: ${client.supabaseUrl}\n")
                append("配置 URL: ${SupabaseConstants.SUPABASE_URL}\n")
                append("Key 长度: ${SupabaseConstants.SUPABASE_ANON_KEY.length}\n")
            }
        } catch (e: Exception) {
            "客户端状态获取失败: ${e.message}"
        }
    }
    
    /**
     * 预初始化 Supabase 客户端
     * 在应用启动时调用，避免首次使用时的延迟
     */
    suspend fun preInitialize() {
        try {
            println("[SupabaseConfig] 🔥 开始预初始化 Supabase 客户端...")
            // 触发 lazy 初始化
            val clientUrl = client.supabaseUrl
            println("[SupabaseConfig] 🎯 预初始化完成，客户端 URL: $clientUrl")
            
            // 验证客户端状态
            val authModule = client.auth
            println("[SupabaseConfig] ✅ Auth 模块已加载: ${authModule.javaClass.simpleName}")
            
            println("[SupabaseConfig] 🚀 Supabase 客户端预初始化成功！")
        } catch (e: Exception) {
            println("[SupabaseConfig] ❌ 预初始化失败: ${e.javaClass.simpleName} - ${e.message}")
            e.printStackTrace()
            throw e
        }
    }
}
