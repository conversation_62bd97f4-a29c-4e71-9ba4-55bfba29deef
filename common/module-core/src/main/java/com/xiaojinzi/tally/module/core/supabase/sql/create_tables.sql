-- =====================================================
-- Supabase 记账应用数据库表结构
-- 基于现有 SQLite 数据模型设计
-- Author: Claude 4.0 sonnet 🐾
-- Created: 2025-01-24
-- =====================================================

-- 启用 RLS (Row Level Security)
ALTER DATABASE postgres SET row_security = on;

-- =====================================================
-- 1. 用户信息缓存表
-- =====================================================
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    name TEXT,
    time_expire BIGINT NOT NULL,
    time_create BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_time_create ON users(time_create);
CREATE INDEX IF NOT EXISTS idx_users_is_deleted ON users(is_deleted);
CREATE INDEX IF NOT EXISTS idx_users_is_sync ON users(is_sync);

-- =====================================================
-- 2. 账本表
-- =====================================================
CREATE TABLE IF NOT EXISTS books (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_system BOOLEAN NOT NULL DEFAULT FALSE,
    type TEXT,
    name TEXT,
    icon_name TEXT,
    time_create BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 账本表索引
CREATE INDEX IF NOT EXISTS idx_books_user_id ON books(user_id);
CREATE INDEX IF NOT EXISTS idx_books_type ON books(type);
CREATE INDEX IF NOT EXISTS idx_books_time_create ON books(time_create);
CREATE INDEX IF NOT EXISTS idx_books_is_deleted ON books(is_deleted);
CREATE INDEX IF NOT EXISTS idx_books_is_sync ON books(is_sync);

-- =====================================================
-- 3. 分类表
-- =====================================================
CREATE TABLE IF NOT EXISTS categories (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id TEXT NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    parent_id TEXT REFERENCES categories(id) ON DELETE SET NULL,
    type TEXT, -- "spending" or "income"
    name TEXT,
    icon_name TEXT,
    sort BIGINT NOT NULL DEFAULT 0,
    time_create BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 分类表索引
CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_book_id ON categories(book_id);
CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_type ON categories(type);
CREATE INDEX IF NOT EXISTS idx_categories_sort ON categories(sort);
CREATE INDEX IF NOT EXISTS idx_categories_time_create ON categories(time_create);
CREATE INDEX IF NOT EXISTS idx_categories_is_deleted ON categories(is_deleted);
CREATE INDEX IF NOT EXISTS idx_categories_is_sync ON categories(is_sync);

-- =====================================================
-- 4. 账户表
-- =====================================================
CREATE TABLE IF NOT EXISTS accounts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id TEXT NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    icon_name TEXT,
    name TEXT,
    balance_init BIGINT NOT NULL DEFAULT 0, -- 初始余额，单位：分
    is_excluded BOOLEAN NOT NULL DEFAULT FALSE, -- 是否排除，不计入资产
    is_default BOOLEAN NOT NULL DEFAULT FALSE, -- 是否默认账户
    time_create BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 账户表索引
CREATE INDEX IF NOT EXISTS idx_accounts_user_id ON accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_accounts_book_id ON accounts(book_id);
CREATE INDEX IF NOT EXISTS idx_accounts_is_default ON accounts(is_default);
CREATE INDEX IF NOT EXISTS idx_accounts_is_excluded ON accounts(is_excluded);
CREATE INDEX IF NOT EXISTS idx_accounts_time_create ON accounts(time_create);
CREATE INDEX IF NOT EXISTS idx_accounts_is_deleted ON accounts(is_deleted);
CREATE INDEX IF NOT EXISTS idx_accounts_is_sync ON accounts(is_sync);

-- =====================================================
-- 5. 账单表 (核心表)
-- =====================================================
CREATE TABLE IF NOT EXISTS bills (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id TEXT NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    type TEXT NOT NULL, -- "normal", "transfer", "refund"
    origin_bill_id TEXT REFERENCES bills(id) ON DELETE SET NULL, -- 原账单ID（退款时使用）
    time BIGINT NOT NULL, -- 账单时间戳
    category_id TEXT REFERENCES categories(id) ON DELETE SET NULL,
    account_id TEXT REFERENCES accounts(id) ON DELETE SET NULL, -- 付款账户
    transfer_target_account_id TEXT REFERENCES accounts(id) ON DELETE SET NULL, -- 转账目标账户
    amount BIGINT NOT NULL, -- 金额，单位：分
    note TEXT, -- 备注
    is_not_calculate BOOLEAN NOT NULL DEFAULT FALSE, -- 是否不计入收支
    time_create BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000,
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 账单表索引（重要：账单表是查询最频繁的表）
CREATE INDEX IF NOT EXISTS idx_bills_user_id ON bills(user_id);
CREATE INDEX IF NOT EXISTS idx_bills_book_id ON bills(book_id);
CREATE INDEX IF NOT EXISTS idx_bills_type ON bills(type);
CREATE INDEX IF NOT EXISTS idx_bills_time ON bills(time); -- 按时间查询很常见
CREATE INDEX IF NOT EXISTS idx_bills_category_id ON bills(category_id);
CREATE INDEX IF NOT EXISTS idx_bills_account_id ON bills(account_id);
CREATE INDEX IF NOT EXISTS idx_bills_origin_bill_id ON bills(origin_bill_id);
CREATE INDEX IF NOT EXISTS idx_bills_time_create ON bills(time_create);
CREATE INDEX IF NOT EXISTS idx_bills_is_deleted ON bills(is_deleted);
CREATE INDEX IF NOT EXISTS idx_bills_is_sync ON bills(is_sync);
-- 复合索引：按用户和时间范围查询
CREATE INDEX IF NOT EXISTS idx_bills_user_time ON bills(user_id, time);
-- 复合索引：按账本和时间查询
CREATE INDEX IF NOT EXISTS idx_bills_book_time ON bills(book_id, time);

-- =====================================================
-- 6. 账单图片表
-- =====================================================
CREATE TABLE IF NOT EXISTS bill_images (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    book_id TEXT NOT NULL REFERENCES books(id) ON DELETE CASCADE,
    bill_id TEXT NOT NULL REFERENCES bills(id) ON DELETE CASCADE,
    url TEXT, -- 图片URL
    time_modify BIGINT,
    time_modify_format TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    is_sync BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 账单图片表索引
CREATE INDEX IF NOT EXISTS idx_bill_images_user_id ON bill_images(user_id);
CREATE INDEX IF NOT EXISTS idx_bill_images_book_id ON bill_images(book_id);
CREATE INDEX IF NOT EXISTS idx_bill_images_bill_id ON bill_images(bill_id);
CREATE INDEX IF NOT EXISTS idx_bill_images_is_deleted ON bill_images(is_deleted);
CREATE INDEX IF NOT EXISTS idx_bill_images_is_sync ON bill_images(is_sync);

-- =====================================================
-- 7. 更新时间触发器函数
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 更新时间格式化字段的触发器函数
CREATE OR REPLACE FUNCTION update_time_modify_format()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.time_modify IS NOT NULL THEN
        NEW.time_modify_format = TO_CHAR(TO_TIMESTAMP(NEW.time_modify / 1000), 'YYYY-MM-DD HH24:MI:SS');
    ELSE
        NEW.time_modify_format = NULL;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器（先删除再创建，避免重复）
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_books_updated_at ON books;
CREATE TRIGGER update_books_updated_at BEFORE UPDATE ON books
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;
CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_bills_updated_at ON bills;
CREATE TRIGGER update_bills_updated_at BEFORE UPDATE ON bills
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_bill_images_updated_at ON bill_images;
CREATE TRIGGER update_bill_images_updated_at BEFORE UPDATE ON bill_images
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为所有表添加时间格式化触发器（先删除再创建，避免重复）
DROP TRIGGER IF EXISTS update_users_time_format ON users;
CREATE TRIGGER update_users_time_format BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

DROP TRIGGER IF EXISTS update_books_time_format ON books;
CREATE TRIGGER update_books_time_format BEFORE INSERT OR UPDATE ON books
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

DROP TRIGGER IF EXISTS update_categories_time_format ON categories;
CREATE TRIGGER update_categories_time_format BEFORE INSERT OR UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

DROP TRIGGER IF EXISTS update_accounts_time_format ON accounts;
CREATE TRIGGER update_accounts_time_format BEFORE INSERT OR UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

DROP TRIGGER IF EXISTS update_bills_time_format ON bills;
CREATE TRIGGER update_bills_time_format BEFORE INSERT OR UPDATE ON bills
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

DROP TRIGGER IF EXISTS update_bill_images_time_format ON bill_images;
CREATE TRIGGER update_bill_images_time_format BEFORE INSERT OR UPDATE ON bill_images
    FOR EACH ROW EXECUTE FUNCTION update_time_modify_format();

-- =====================================================
-- 8. 行级安全策略 (RLS)
-- =====================================================

-- 启用所有表的 RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE books ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE bills ENABLE ROW LEVEL SECURITY;
ALTER TABLE bill_images ENABLE ROW LEVEL SECURITY;

-- 用户表 RLS 策略：用户只能访问自己的记录（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own data" ON users;
CREATE POLICY "Users can view own data" ON users
    FOR SELECT USING (auth.uid()::text = id);

DROP POLICY IF EXISTS "Users can insert own data" ON users;
CREATE POLICY "Users can insert own data" ON users
    FOR INSERT WITH CHECK (auth.uid()::text = id);

DROP POLICY IF EXISTS "Users can update own data" ON users;
CREATE POLICY "Users can update own data" ON users
    FOR UPDATE USING (auth.uid()::text = id);

DROP POLICY IF EXISTS "Users can delete own data" ON users;
CREATE POLICY "Users can delete own data" ON users
    FOR DELETE USING (auth.uid()::text = id);

-- 账本表 RLS 策略（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own books" ON books;
CREATE POLICY "Users can view own books" ON books
    FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert own books" ON books;
CREATE POLICY "Users can insert own books" ON books
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update own books" ON books;
CREATE POLICY "Users can update own books" ON books
    FOR UPDATE USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can delete own books" ON books;
CREATE POLICY "Users can delete own books" ON books
    FOR DELETE USING (auth.uid()::text = user_id);

-- 分类表 RLS 策略（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own categories" ON categories;
CREATE POLICY "Users can view own categories" ON categories
    FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert own categories" ON categories;
CREATE POLICY "Users can insert own categories" ON categories
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update own categories" ON categories;
CREATE POLICY "Users can update own categories" ON categories
    FOR UPDATE USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can delete own categories" ON categories;
CREATE POLICY "Users can delete own categories" ON categories
    FOR DELETE USING (auth.uid()::text = user_id);

-- 账户表 RLS 策略（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own accounts" ON accounts;
CREATE POLICY "Users can view own accounts" ON accounts
    FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert own accounts" ON accounts;
CREATE POLICY "Users can insert own accounts" ON accounts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update own accounts" ON accounts;
CREATE POLICY "Users can update own accounts" ON accounts
    FOR UPDATE USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can delete own accounts" ON accounts;
CREATE POLICY "Users can delete own accounts" ON accounts
    FOR DELETE USING (auth.uid()::text = user_id);

-- 账单表 RLS 策略（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own bills" ON bills;
CREATE POLICY "Users can view own bills" ON bills
    FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert own bills" ON bills;
CREATE POLICY "Users can insert own bills" ON bills
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update own bills" ON bills;
CREATE POLICY "Users can update own bills" ON bills
    FOR UPDATE USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can delete own bills" ON bills;
CREATE POLICY "Users can delete own bills" ON bills
    FOR DELETE USING (auth.uid()::text = user_id);

-- 账单图片表 RLS 策略（先删除再创建，避免重复）
DROP POLICY IF EXISTS "Users can view own bill images" ON bill_images;
CREATE POLICY "Users can view own bill images" ON bill_images
    FOR SELECT USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can insert own bill images" ON bill_images;
CREATE POLICY "Users can insert own bill images" ON bill_images
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can update own bill images" ON bill_images;
CREATE POLICY "Users can update own bill images" ON bill_images
    FOR UPDATE USING (auth.uid()::text = user_id);

DROP POLICY IF EXISTS "Users can delete own bill images" ON bill_images;
CREATE POLICY "Users can delete own bill images" ON bill_images
    FOR DELETE USING (auth.uid()::text = user_id);

-- =====================================================
-- 9. 创建视图和函数
-- =====================================================

-- 账单详情视图（类似于 BillDetailDo）
CREATE OR REPLACE VIEW bill_details AS
SELECT
    b.*,
    u.name as user_name,
    bk.name as book_name,
    bk.icon_name as book_icon_name,
    c.name as category_name,
    c.icon_name as category_icon_name,
    c.type as category_type,
    a.name as account_name,
    a.icon_name as account_icon_name,
    ta.name as transfer_target_account_name,
    ta.icon_name as transfer_target_account_icon_name
FROM bills b
LEFT JOIN users u ON b.user_id = u.id
LEFT JOIN books bk ON b.book_id = bk.id
LEFT JOIN categories c ON b.category_id = c.id
LEFT JOIN accounts a ON b.account_id = a.id
LEFT JOIN accounts ta ON b.transfer_target_account_id = ta.id;

-- 账户余额计算函数
CREATE OR REPLACE FUNCTION calculate_account_balance(account_id_param TEXT)
RETURNS BIGINT AS $$
DECLARE
    balance BIGINT;
    init_balance BIGINT;
BEGIN
    -- 获取初始余额
    SELECT balance_init INTO init_balance
    FROM accounts
    WHERE id = account_id_param;

    -- 计算账单影响的余额变化
    SELECT COALESCE(SUM(
        CASE
            WHEN account_id = account_id_param THEN -amount  -- 支出
            WHEN transfer_target_account_id = account_id_param THEN amount  -- 转入
            ELSE 0
        END
    ), 0) INTO balance
    FROM bills
    WHERE (account_id = account_id_param OR transfer_target_account_id = account_id_param)
    AND is_deleted = FALSE
    AND is_not_calculate = FALSE;

    RETURN COALESCE(init_balance, 0) + COALESCE(balance, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 完成表结构创建
-- =====================================================

-- 插入说明注释
COMMENT ON DATABASE postgres IS 'Yike记账应用数据库 - 基于Supabase PostgreSQL';
COMMENT ON TABLE users IS '用户信息缓存表';
COMMENT ON TABLE books IS '账本表';
COMMENT ON TABLE categories IS '分类表';
COMMENT ON TABLE accounts IS '账户表';
COMMENT ON TABLE bills IS '账单表（核心表）';
COMMENT ON TABLE bill_images IS '账单图片表';
