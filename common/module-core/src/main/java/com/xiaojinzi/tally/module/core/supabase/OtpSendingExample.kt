package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.launch
import kotlinx.coroutines.cancel

/**
 * OTP 发送使用示例
 * 
 * 演示如何使用方案一：无需等回调，直接提示发送成功并显示倒计时
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-21
 */
class OtpSendingExample {
    
    companion object {
        private const val TAG = "OtpSendingExample"
    }
    
    private val otpManager = OtpAuthManagerV2.getInstance()
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    /**
     * 初始化示例
     * 开始监听倒计时状态
     */
    fun initialize() {
        Log.d(TAG, "初始化 OTP 发送示例")
        
        // 监听倒计时状态
        scope.launch {
            otpManager.resendCountdown.collect { countdown ->
                handleCountdownUpdate(countdown)
            }
        }
    }
    
    /**
     * 发送验证码示例
     * 
     * @param email 邮箱地址
     */
    suspend fun sendOtpExample(email: String) {
        try {
            Log.d(TAG, "开始发送验证码到: $email")
            
            // 检查是否可以发送
            if (!otpManager.canResend) {
                Log.w(TAG, "当前不能发送，还需等待 ${otpManager.resendCountdown.value} 秒")
                showMessage("请等待 ${otpManager.resendCountdown.value} 秒后再重新发送")
                return
            }
            
            // 显示发送中状态
            showMessage("正在发送验证码...")
            showLoading(true)
            
            // 发送验证码
            val success = otpManager.sendEmailOtp(email)
            
            if (success) {
                // 立即显示发送成功提示
                Log.d(TAG, "✅ 验证码发送成功")
                showMessage("验证码已发送到您的邮箱，请查收")
                showSuccess("发送成功！请查看邮箱中的验证码")
                
                // 此时倒计时已经自动开始，UI会通过监听 resendCountdown 来更新按钮状态
            }
            
        } catch (e: AuthException) {
            Log.e(TAG, "发送验证码失败: ${e.message}")
            showError(e.message ?: "发送失败")
        } catch (e: Exception) {
            Log.e(TAG, "发送验证码时发生异常: ${e.message}", e)
            showError("发送失败，请稍后重试")
        } finally {
            showLoading(false)
        }
    }
    
    /**
     * 处理倒计时更新
     * 
     * @param countdown 当前倒计时秒数
     */
    private fun handleCountdownUpdate(countdown: Int) {
        when (countdown) {
            0 -> {
                // 倒计时结束，可以重新发送
                Log.d(TAG, "倒计时结束，可以重新发送")
                updateResendButton(enabled = true, text = "重新发送")
            }
            else -> {
                // 倒计时进行中
                Log.d(TAG, "倒计时: $countdown 秒")
                updateResendButton(enabled = false, text = "重新发送 (${countdown}s)")
            }
        }
    }
    
    /**
     * 取消倒计时示例
     * 在某些情况下可能需要手动取消倒计时
     */
    fun cancelCountdownExample() {
        Log.d(TAG, "取消倒计时")
        otpManager.cancelResendCountdown()
        showMessage("倒计时已取消")
    }
    
    /**
     * 销毁示例
     * 清理资源
     */
    fun destroy() {
        Log.d(TAG, "销毁 OTP 发送示例")
        scope.cancel()
    }
    
    // ========== UI 更新方法（需要根据实际UI框架实现） ==========
    
    /**
     * 显示消息提示
     */
    private fun showMessage(message: String) {
        Log.d(TAG, "显示消息: $message")
        // TODO: 实现实际的消息提示，例如 Toast 或 Snackbar
        // Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 显示成功提示
     */
    private fun showSuccess(message: String) {
        Log.d(TAG, "显示成功提示: $message")
        // TODO: 实现成功提示UI，例如绿色的提示框
        // 可以使用 Material Design 的 Snackbar 或自定义成功对话框
    }
    
    /**
     * 显示错误提示
     */
    private fun showError(message: String) {
        Log.e(TAG, "显示错误提示: $message")
        // TODO: 实现错误提示UI，例如红色的提示框
        // 可以使用 Material Design 的 Snackbar 或自定义错误对话框
    }
    
    /**
     * 显示/隐藏加载状态
     */
    private fun showLoading(show: Boolean) {
        Log.d(TAG, "显示加载状态: $show")
        // TODO: 实现加载状态UI，例如进度条或加载动画
        // progressBar.visibility = if (show) View.VISIBLE else View.GONE
    }
    
    /**
     * 更新重新发送按钮状态
     */
    private fun updateResendButton(enabled: Boolean, text: String) {
        Log.d(TAG, "更新重新发送按钮: enabled=$enabled, text=$text")
        // TODO: 实现按钮状态更新
        // resendButton.isEnabled = enabled
        // resendButton.text = text
    }
}

/**
 * Compose UI 使用示例
 * 
 * 展示如何在 Jetpack Compose 中使用 OTP 发送功能
 */
/*
@Composable
fun OtpSendingScreen(
    email: String,
    onEmailChange: (String) -> Unit
) {
    val otpManager = remember { OtpAuthManagerV2.getInstance() }
    val countdown by otpManager.resendCountdown.collectAsState()
    val canResend = countdown == 0
    
    var isLoading by remember { mutableStateOf(false) }
    var message by remember { mutableStateOf("") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        // 邮箱输入框
        OutlinedTextField(
            value = email,
            onValueChange = onEmailChange,
            label = { Text("邮箱地址") },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 发送按钮
        Button(
            onClick = {
                // 发送验证码
                // 在实际项目中，这里应该调用 ViewModel 或 Repository
            },
            enabled = canResend && !isLoading,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (isLoading) {
                CircularProgressIndicator(size = 16.dp)
            } else {
                Text(
                    text = if (canResend) "发送验证码" else "重新发送 (${countdown}s)"
                )
            }
        }
        
        // 消息提示
        if (message.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                color = MaterialTheme.colors.primary,
                style = MaterialTheme.typography.body2
            )
        }
    }
}
*/