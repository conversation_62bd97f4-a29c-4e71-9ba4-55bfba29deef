package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable

/**
 * Supabase 数据检查工具
 * 用于查询和验证用户数据初始化状态
 */
object SupabaseDataChecker {
    
    private const val TAG = "SupabaseDataChecker"
    
    /**
     * 用户数据统计结果
     */
    @Serializable
    data class UserDataStats(
        val userId: String,
        val isInitialized: Boolean,
        val books: List<BookInfo> = emptyList(),
        val totalCategories: Int = 0,
        val totalAccounts: Int = 0,
        val error: String? = null
    )
    
    /**
     * 账本信息
     */
    @Serializable
    data class BookInfo(
        val id: String,
        val name: String,
        val isSystem: Boolean,
        val categoryCount: Int,
        val accountCount: Int
    )
    
    /**
     * 检查用户数据初始化状态（详细版本）
     * 
     * @param userId 用户ID
     * @return 详细的数据统计结果
     */
    suspend fun checkUserDataStats(userId: String): Result<UserDataStats> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始检查用户数据状态: userId=$userId")
            
            // 1. 先使用现有的检查方法
            val basicCheck = UserDataInitializer.checkUserInitialized(userId).getOrThrow()
            
            if (!basicCheck.isInitialized) {
                return@withContext Result.success(
                    UserDataStats(
                        userId = userId,
                        isInitialized = false,
                        error = "用户数据未初始化"
                    )
                )
            }
            
            // 2. 获取详细的账本信息
            val booksResult = SupabaseDataService.getUserBooks(userId).getOrThrow()
            val bookInfos = mutableListOf<BookInfo>()
            var totalCategories = 0
            var totalAccounts = 0
            
            for (book in booksResult) {
                // 获取每个账本的类别和账户数量
                val categoriesResult = SupabaseDataService.getBookCategories(book.id)
                val accountsResult = SupabaseDataService.getBookAccounts(book.id)

                val categoryCount = categoriesResult.getOrNull()?.size ?: 0
                val accountCount = accountsResult.getOrNull()?.size ?: 0

                bookInfos.add(
                    BookInfo(
                        id = book.id,
                        name = book.name ?: "未命名账本",
                        isSystem = book.isSystem,
                        categoryCount = categoryCount,
                        accountCount = accountCount
                    )
                )
                
                totalCategories += categoryCount
                totalAccounts += accountCount
            }
            
            val stats = UserDataStats(
                userId = userId,
                isInitialized = true,
                books = bookInfos,
                totalCategories = totalCategories,
                totalAccounts = totalAccounts
            )
            
            Log.d(TAG, "✅ 用户数据检查完成")
            Log.d(TAG, "📊 统计结果:")
            Log.d(TAG, "  - 账本数量: ${bookInfos.size}")
            Log.d(TAG, "  - 总类别数: $totalCategories")
            Log.d(TAG, "  - 总账户数: $totalAccounts")
            
            Result.success(stats)
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查用户数据状态失败: ${e.message}", e)
            Result.success(
                UserDataStats(
                    userId = userId,
                    isInitialized = false,
                    error = e.message
                )
            )
        }
    }
    
    /**
     * 打印用户数据状态到日志（方便调试）
     * 
     * @param userId 用户ID
     */
    suspend fun printUserDataStats(userId: String) {
        try {
            val stats = checkUserDataStats(userId).getOrThrow()
            
            println("========== 用户数据状态报告 ==========")
            println("用户ID: ${stats.userId}")
            println("初始化状态: ${if (stats.isInitialized) "✅ 已初始化" else "❌ 未初始化"}")
            
            if (stats.error != null) {
                println("错误信息: ${stats.error}")
            }
            
            if (stats.isInitialized) {
                println("账本列表:")
                stats.books.forEachIndexed { index, book ->
                    println("  ${index + 1}. ${book.name} (${if (book.isSystem) "系统账本" else "用户账本"})")
                    println("     - ID: ${book.id}")
                    println("     - 类别数: ${book.categoryCount}")
                    println("     - 账户数: ${book.accountCount}")
                }
                println("总计:")
                println("  - 账本数量: ${stats.books.size}")
                println("  - 类别总数: ${stats.totalCategories}")
                println("  - 账户总数: ${stats.totalAccounts}")
            }
            println("=====================================")
            
        } catch (e: Exception) {
            println("========== 用户数据状态报告 ==========")
            println("用户ID: $userId")
            println("检查失败: ${e.message}")
            println("=====================================")
        }
    }
    
    /**
     * 快速检查用户是否已初始化
     * 
     * @param userId 用户ID
     * @return 是否已初始化
     */
    suspend fun isUserInitialized(userId: String): Boolean {
        return try {
            UserDataInitializer.checkUserInitialized(userId)
                .getOrNull()?.isInitialized ?: false
        } catch (e: Exception) {
            Log.w(TAG, "检查用户初始化状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 强制重新初始化用户数据
     * 
     * @param userId 用户ID
     * @param userName 用户名
     * @return 是否成功
     */
    suspend fun forceReinitializeUser(
        userId: String, 
        userName: String = "用户"
    ): Result<Boolean> {
        return try {
            Log.d(TAG, "开始强制重新初始化用户数据: userId=$userId")
            
            val result = UserDataInitializer.initializeUserData(userId, userName).getOrThrow()
            
            if (result.success) {
                Log.d(TAG, "✅ 强制重新初始化成功")
                // 打印初始化后的状态
                printUserDataStats(userId)
                Result.success(true)
            } else {
                Log.e(TAG, "❌ 强制重新初始化失败: ${result.message}")
                Result.success(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 强制重新初始化异常: ${e.message}", e)
            Result.failure(e)
        }
    }
}
