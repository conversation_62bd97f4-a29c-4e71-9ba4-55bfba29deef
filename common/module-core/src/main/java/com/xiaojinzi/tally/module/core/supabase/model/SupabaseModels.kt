package com.xiaojinzi.tally.module.core.supabase.model

import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Supabase 数据模型定义
 * 
 * 基于现有 SQLite 数据模型设计，适配 Supabase PostgreSQL
 * 使用 Kotlinx Serialization 进行序列化
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */

// =====================================================
// 基础接口
// =====================================================

/**
 * 同步表接口
 */
interface SupabaseSyncTable {
    val timeModify: Long?
    val isDeleted: Boolean
    val isSync: Boolean
}

/**
 * 时间创建字段接口
 */
interface SupabaseTimeCreateField {
    val timeCreate: Long
}

// =====================================================
// 用户模型
// =====================================================

@Keep
@Serializable
data class SupabaseUser(
    val id: String,
    val name: String? = null,
    @SerialName("time_expire")
    val timeExpire: Long,
    @SerialName("time_create")
    override val timeCreate: Long,
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable, SupabaseTimeCreateField

// =====================================================
// 账本模型
// =====================================================

@Keep
@Serializable
data class SupabaseBook(
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("is_system")
    val isSystem: Boolean = false,
    val type: String? = null,
    val name: String? = null,
    @SerialName("icon_name")
    val iconName: String? = null,
    @SerialName("time_create")
    override val timeCreate: Long,
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable, SupabaseTimeCreateField

// =====================================================
// 分类模型
// =====================================================

@Keep
@Serializable
data class SupabaseCategory(
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("book_id")
    val bookId: String,
    @SerialName("parent_id")
    val parentId: String? = null,
    val type: String? = null, // "spending" or "income"
    val name: String? = null,
    @SerialName("icon_name")
    val iconName: String? = null,
    val sort: Long = 0,
    @SerialName("time_create")
    override val timeCreate: Long,
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable, SupabaseTimeCreateField

// =====================================================
// 账户模型
// =====================================================

@Keep
@Serializable
data class SupabaseAccount(
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("book_id")
    val bookId: String,
    @SerialName("icon_name")
    val iconName: String? = null,
    val name: String? = null,
    @SerialName("balance_init")
    val balanceInit: Long = 0, // 初始余额，单位：分
    @SerialName("is_excluded")
    val isExcluded: Boolean = false, // 是否排除，不计入资产
    @SerialName("is_default")
    val isDefault: Boolean = false, // 是否默认账户
    @SerialName("time_create")
    override val timeCreate: Long,
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable, SupabaseTimeCreateField

// =====================================================
// 账单模型
// =====================================================

@Keep
@Serializable
data class SupabaseBill(
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("book_id")
    val bookId: String,
    val type: String, // "normal", "transfer", "refund"
    @SerialName("origin_bill_id")
    val originBillId: String? = null, // 原账单ID（退款时使用）
    val time: Long, // 账单时间戳
    @SerialName("category_id")
    val categoryId: String? = null,
    @SerialName("account_id")
    val accountId: String? = null, // 付款账户
    @SerialName("transfer_target_account_id")
    val transferTargetAccountId: String? = null, // 转账目标账户
    val amount: Long, // 金额，单位：分
    val note: String? = null, // 备注
    @SerialName("is_not_calculate")
    val isNotCalculate: Boolean = false, // 是否不计入收支
    @SerialName("time_create")
    override val timeCreate: Long,
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable, SupabaseTimeCreateField

// =====================================================
// 账单图片模型
// =====================================================

@Keep
@Serializable
data class SupabaseBillImage(
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("book_id")
    val bookId: String,
    @SerialName("bill_id")
    val billId: String,
    val url: String? = null, // 图片URL
    @SerialName("time_modify")
    override val timeModify: Long? = null,
    @SerialName("time_modify_format")
    val timeModifyFormat: String? = null,
    @SerialName("is_deleted")
    override val isDeleted: Boolean = false,
    @SerialName("is_sync")
    override val isSync: Boolean = false,
    @SerialName("created_at")
    val createdAt: String? = null,
    @SerialName("updated_at")
    val updatedAt: String? = null
) : SupabaseSyncTable

// =====================================================
// 账单详情模型（对应视图）
// =====================================================

@Keep
@Serializable
data class SupabaseBillDetail(
    // 账单基础信息
    val id: String,
    @SerialName("user_id")
    val userId: String,
    @SerialName("book_id")
    val bookId: String,
    val type: String,
    @SerialName("origin_bill_id")
    val originBillId: String? = null,
    val time: Long,
    @SerialName("category_id")
    val categoryId: String? = null,
    @SerialName("account_id")
    val accountId: String? = null,
    @SerialName("transfer_target_account_id")
    val transferTargetAccountId: String? = null,
    val amount: Long,
    val note: String? = null,
    @SerialName("is_not_calculate")
    val isNotCalculate: Boolean = false,
    @SerialName("time_create")
    val timeCreate: Long,
    @SerialName("time_modify")
    val timeModify: Long? = null,
    @SerialName("is_deleted")
    val isDeleted: Boolean = false,
    @SerialName("is_sync")
    val isSync: Boolean = false,
    
    // 关联信息
    @SerialName("user_name")
    val userName: String? = null,
    @SerialName("book_name")
    val bookName: String? = null,
    @SerialName("book_icon_name")
    val bookIconName: String? = null,
    @SerialName("category_name")
    val categoryName: String? = null,
    @SerialName("category_icon_name")
    val categoryIconName: String? = null,
    @SerialName("category_type")
    val categoryType: String? = null,
    @SerialName("account_name")
    val accountName: String? = null,
    @SerialName("account_icon_name")
    val accountIconName: String? = null,
    @SerialName("transfer_target_account_name")
    val transferTargetAccountName: String? = null,
    @SerialName("transfer_target_account_icon_name")
    val transferTargetAccountIconName: String? = null
)
