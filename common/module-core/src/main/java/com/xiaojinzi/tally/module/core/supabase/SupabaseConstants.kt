package com.xiaojinzi.tally.module.core.supabase

/**
 * Supabase 配置常量
 * 
 * 使用说明：
 * 1. 请将 SUPABASE_URL 替换为你的 Supabase 项目 URL
 * 2. 请将 SUPABASE_ANON_KEY 替换为你的 Supabase anon 密钥
 * 3. 这些信息可以在 Supabase Dashboard > Settings > API 中找到
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-21
 */
object SupabaseConstants {
    
    /**
     * Supabase 项目 URL
     * 格式: https://your-project-ref.supabase.co
     * 注意：请替换为你的实际 Supabase 项目 URL
     */
    const val SUPABASE_URL = "https://poiaidnbnqnldymniaqs.supabase.co"
    
    /**
     * Supabase anon 密钥
     * 这个密钥可以安全地暴露在客户端
     * 注意：请替换为你的实际 Supabase anon 密钥
     */
    const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBvaWFpZG5ibnFubGR5bW5pYXFzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI4NDUwODIsImV4cCI6MjA2ODQyMTA4Mn0.c3UYrqboAMBWvAjx16UMZ8OwcF4N5uDw40O1PmvH1qo"
    
    /**
     * 邮箱 OTP 过期时间（秒）
     * 默认 60 秒防重复发送
     */
    const val OTP_RESEND_INTERVAL_SECONDS = 60L
    
    /**
     * OTP 验证码长度
     */
    const val OTP_CODE_LENGTH = 6
    
    /**
     * 邮箱验证正则表达式
     */
    const val EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
    
}