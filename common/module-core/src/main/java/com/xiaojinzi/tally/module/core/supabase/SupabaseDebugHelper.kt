package com.xiaojinzi.tally.module.core.supabase

import android.content.Context
import android.util.Log
import android.widget.Toast
import com.xiaojinzi.tally.module.base.support.AppServices
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.firstOrNull

/**
 * Supabase 调试助手
 * 提供便捷的调试和查询功能
 */
object SupabaseDebugHelper {
    
    private const val TAG = "SupabaseDebugHelper"
    
    /**
     * 显示当前用户的数据状态（带 Toast 提示）
     * 
     * @param context 上下文
     */
    fun showCurrentUserDataStatus(context: Context) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val userInfo = AppServices.userSpi.userInfoStateOb.firstOrNull()
                if (userInfo == null) {
                    Toast.makeText(context, "用户未登录", Toast.LENGTH_SHORT).show()
                    return@launch
                }
                
                val stats = withContext(Dispatchers.IO) {
                    SupabaseDataChecker.checkUserDataStats(userInfo.id).getOrThrow()
                }
                
                val message = if (stats.isInitialized) {
                    "✅ 数据已初始化\n账本: ${stats.books.size}个\n类别: ${stats.totalCategories}个\n账户: ${stats.totalAccounts}个"
                } else {
                    "❌ 数据未初始化\n${stats.error ?: "未知错误"}"
                }
                
                Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                
                // 同时打印到日志
                SupabaseDataChecker.printUserDataStats(userInfo.id)
                
            } catch (e: Exception) {
                Toast.makeText(context, "查询失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "查询用户数据状态失败", e)
            }
        }
    }
    
    /**
     * 强制重新初始化当前用户数据（带 Toast 提示）
     * 
     * @param context 上下文
     */
    fun forceReinitializeCurrentUser(context: Context) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val userInfo = AppServices.userSpi.userInfoStateOb.firstOrNull()
                if (userInfo == null) {
                    Toast.makeText(context, "用户未登录", Toast.LENGTH_SHORT).show()
                    return@launch
                }
                
                Toast.makeText(context, "开始重新初始化...", Toast.LENGTH_SHORT).show()
                
                val success = withContext(Dispatchers.IO) {
                    SupabaseDataChecker.forceReinitializeUser(
                        userId = userInfo.id,
                        userName = userInfo.name ?: "用户"
                    ).getOrThrow()
                }
                
                val message = if (success) {
                    "✅ 重新初始化成功"
                } else {
                    "❌ 重新初始化失败"
                }
                
                Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                
            } catch (e: Exception) {
                Toast.makeText(context, "重新初始化失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "重新初始化失败", e)
            }
        }
    }
    
    /**
     * 检查 Supabase 连接状态
     * 
     * @param context 上下文
     */
    fun checkSupabaseConnection(context: Context) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                val connector = SupabaseConnector.getInstance()
                val isConnected = connector.isConnected
                val isInitialized = connector.isInitialized
                
                val message = """
                    Supabase 连接状态:
                    初始化: ${if (isInitialized) "✅" else "❌"}
                    连接: ${if (isConnected) "✅" else "❌"}
                """.trimIndent()
                
                Toast.makeText(context, message, Toast.LENGTH_LONG).show()
                Log.d(TAG, message)
                
            } catch (e: Exception) {
                Toast.makeText(context, "检查连接失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "检查连接失败", e)
            }
        }
    }
    
    /**
     * 获取当前用户的账本列表（仅日志输出）
     */
    fun logCurrentUserBooks() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val userInfo = AppServices.userSpi.userInfoStateOb.firstOrNull()
                if (userInfo == null) {
                    Log.w(TAG, "用户未登录，无法获取账本列表")
                    return@launch
                }
                
                val booksResult = SupabaseDataService.getUserBooks(userInfo.id)
                
                if (booksResult.isSuccess) {
                    val books = booksResult.getOrNull() ?: emptyList()
                    Log.d(TAG, "========== 用户账本列表 ==========")
                    Log.d(TAG, "用户ID: ${userInfo.id}")
                    Log.d(TAG, "账本数量: ${books.size}")
                    
                    books.forEachIndexed { index, book ->
                        Log.d(TAG, "${index + 1}. ${book.name} (${if (book.isSystem) "系统" else "用户"})")
                        Log.d(TAG, "   ID: ${book.id}")
                        Log.d(TAG, "   创建时间: ${book.timeCreate}")
                    }
                    Log.d(TAG, "================================")
                } else {
                    Log.e(TAG, "获取账本列表失败: ${booksResult.exceptionOrNull()?.message}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "获取账本列表异常", e)
            }
        }
    }
    
    /**
     * 打印完整的调试信息
     */
    fun printFullDebugInfo() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "========== Supabase 完整调试信息 ==========")

                // 1. 连接状态
                val connector = SupabaseConnector.getInstance()
                Log.d(TAG, "连接器初始化: ${connector.isInitialized}")
                Log.d(TAG, "连接器连接: ${connector.isConnected}")

                // 2. 用户信息
                val userInfo = AppServices.userSpi.userInfoStateOb.firstOrNull()
                if (userInfo != null) {
                    Log.d(TAG, "当前用户: ${userInfo.name} (${userInfo.id})")

                    // 3. 用户数据状态
                    SupabaseDataChecker.printUserDataStats(userInfo.id)

                    // 4. 账本详情
                    logCurrentUserBooks()

                    // 5. 数据库表检查
                    val tableReport = SupabaseTableChecker.checkAllTables(userInfo.id).getOrNull()
                    if (tableReport != null) {
                        Log.d(TAG, "数据库表检查结果:")
                        Log.d(TAG, tableReport)
                    }
                } else {
                    Log.d(TAG, "当前用户: 未登录")

                    // 即使未登录也检查表结构
                    val tableReport = SupabaseTableChecker.checkAllTables().getOrNull()
                    if (tableReport != null) {
                        Log.d(TAG, "数据库表检查结果:")
                        Log.d(TAG, tableReport)
                    }
                }

                Log.d(TAG, "==========================================")

            } catch (e: Exception) {
                Log.e(TAG, "打印调试信息失败", e)
            }
        }
    }

    /**
     * 检查数据库表状态（带 Toast 提示）
     */
    fun checkDatabaseTables(context: Context) {
        CoroutineScope(Dispatchers.Main).launch {
            try {
                Toast.makeText(context, "开始检查数据库表...", Toast.LENGTH_SHORT).show()

                val userInfo = AppServices.userSpi.userInfoStateOb.firstOrNull()
                val userId = userInfo?.id

                val report = withContext(Dispatchers.IO) {
                    SupabaseTableChecker.checkAllTables(userId).getOrThrow()
                }

                // 简化的 Toast 消息
                val lines = report.lines()
                val bookCount = lines.find { it.contains("books") && it.contains("记录数量") }?.substringAfter("记录数量: ")?.trim() ?: "0"
                val categoryCount = lines.find { it.contains("categories") && it.contains("记录数量") }?.substringAfter("记录数量: ")?.trim() ?: "0"
                val accountCount = lines.find { it.contains("accounts") && it.contains("记录数量") }?.substringAfter("记录数量: ")?.trim() ?: "0"

                val message = "数据库检查完成\n账本: ${bookCount}个\n类别: ${categoryCount}个\n账户: ${accountCount}个"
                Toast.makeText(context, message, Toast.LENGTH_LONG).show()

                // 详细信息输出到日志
                Log.d(TAG, report)

            } catch (e: Exception) {
                Toast.makeText(context, "检查数据库失败: ${e.message}", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "检查数据库失败", e)
            }
        }
    }
}
