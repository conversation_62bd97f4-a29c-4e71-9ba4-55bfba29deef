# 部署增强版默认分类函数

## 概述

为了解决新建账本时不会创建默认分类的问题，我们创建了一个增强版的SQL函数 `create_default_categories_for_book_enhanced`，它包含完整的50个默认分类。

## 部署步骤

### 1. 在Supabase控制台执行SQL

登录到你的Supabase项目控制台，进入SQL编辑器，执行以下文件中的SQL代码：

```
common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/create_default_categories_enhanced.sql
```

### 2. 验证函数是否创建成功

执行以下SQL来验证函数是否正确创建：

```sql
-- 检查函数是否存在
SELECT proname, prosrc 
FROM pg_proc 
WHERE proname = 'create_default_categories_for_book_enhanced';
```

### 3. 测试函数功能

可以使用以下SQL测试函数（请替换实际的用户ID和账本ID）：

```sql
-- 测试创建默认分类
SELECT create_default_categories_for_book_enhanced(
    'your-user-id'::uuid, 
    'your-book-id'::uuid
);

-- 检查创建的分类
SELECT name, type, parent_id, sort_order 
FROM book_categories 
WHERE book_id = 'your-book-id'::uuid 
ORDER BY sort_order;
```

## 函数说明

### 函数签名
```sql
create_default_categories_for_book_enhanced(p_user_id UUID, p_book_id UUID) RETURNS INTEGER
```

### 参数
- `p_user_id`: 用户ID
- `p_book_id`: 账本ID

### 返回值
- 返回创建的分类总数（应该是50个）

### 创建的分类结构

#### 主要支出类别（8个）
1. 购物消费 (shopping1)
2. 食品餐饮 (chopsticksFork1)
3. 出行交通 (car1)
4. 休闲娱乐 (gamepad1)
5. 人情世故 (gift1)
6. 健康医疗 (medical1)
7. 居家生活 (house1)
8. 其他支出 (expenses1)

#### 子类别（36个）
- 购物消费子类别：10个
- 食品餐饮子类别：7个
- 出行交通子类别：5个
- 休闲娱乐子类别：5个
- 健康医疗子类别：3个
- 居家生活子类别：4个
- 人情世故子类别：2个

#### 收入类别（6个）
1. 工资收入 (wage1)
2. 奖金补贴 (bonus1)
3. 兼职外快 (partTimeJob1)
4. 投资收益 (investment1)
5. 借入资金 (income1)
6. 其他收入 (money1)

## 代码集成

函数部署后，Android应用中的新建账本功能会自动调用此函数来创建默认分类。相关代码修改：

1. `SupabaseDataService.kt` - 添加了 `createDefaultCategoriesForBook` 方法
2. `BookCrudUseCase.kt` - 在开源版本创建账本时调用该方法

## 注意事项

1. 确保Supabase项目中已经有正确的表结构（book_categories表）
2. 函数使用 `SECURITY DEFINER` 权限，确保有足够的权限执行
3. 如果函数执行失败，新建账本功能仍然可以正常工作，只是不会有默认分类
