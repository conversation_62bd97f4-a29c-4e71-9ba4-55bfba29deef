package com.xiaojinzi.tally.module.core

import android.app.Application
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.component.anno.ModuleAppAnno
import com.xiaojinzi.component.application.IApplicationLifecycle
import com.xiaojinzi.component.application.IModuleNotifyChanged
import com.xiaojinzi.support.ktx.executeTaskInCoroutinesIgnoreError

@ModuleAppAnno
class CoreModuleApplication : IApplicationLifecycle, IModuleNotifyChanged {

    override fun onCreate(app: Application) {
        // 预初始化 Supabase 客户端
        // 在应用启动时就初始化，避免首次使用时的延迟
        executeTaskInCoroutinesIgnoreError {
            try {
                println("[CoreModuleApplication] 开始预初始化 Supabase 客户端...")
                
                // 首先运行兼容性测试
                println("[CoreModuleApplication] 🔍 运行 Ktor 兼容性测试...")
                val testResult = com.xiaojinzi.tally.module.core.test.SupabaseConnectionTest.runAllTests()
                if (testResult) {
                    println("[CoreModuleApplication] ✅ Ktor 兼容性测试通过")
                } else {
                    println("[CoreModuleApplication] ❌ Ktor 兼容性测试失败")
                }
                
                com.xiaojinzi.tally.module.core.supabase.SupabaseConfig.preInitialize()
                println("[CoreModuleApplication] Supabase 客户端预初始化完成")
                
                // 初始化 SupabaseConnector (新增)
                println("[CoreModuleApplication] 开始初始化 SupabaseConnector V2...")
                val connector = com.xiaojinzi.tally.module.core.supabase.SupabaseConnector.getInstance()
                val result = connector.initialize()
                if (result.isSuccess) {
                    println("[CoreModuleApplication] SupabaseConnector V2 初始化完成")
                } else {
                    println("[CoreModuleApplication] SupabaseConnector V2 初始化失败: ${result.exceptionOrNull()?.message}")
                }

                // 初始化 Supabase MCP 服务 (新增)
                println("[CoreModuleApplication] 开始初始化 Supabase MCP 服务...")
                val mcpService = com.xiaojinzi.tally.module.core.supabase.SupabaseMcpService.getInstance()
                val mcpResult = mcpService.initialize()
                if (mcpResult.isSuccess) {
                    println("[CoreModuleApplication] Supabase MCP 服务初始化完成")

                    // 运行 MCP 使用示例（可选，仅用于演示）
                    // com.xiaojinzi.tally.module.core.supabase.SupabaseMcpUsageExample.runExampleInCoroutine()
                } else {
                    println("[CoreModuleApplication] Supabase MCP 服务初始化失败: ${mcpResult.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                println("[CoreModuleApplication] Supabase 初始化失败: ${e.message}")
                e.printStackTrace()
            }
        }
    }

    override fun onDestroy() {
    }

    override fun onModuleChanged(app: Application) {
        /*executeTaskInCoroutinesIgnoreError {
            AppServices
                .tallyDataSourceSpi
                ?.insertTestDataOnce()
        }*/
    }

}
