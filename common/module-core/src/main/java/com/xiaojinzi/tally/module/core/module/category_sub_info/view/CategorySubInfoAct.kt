package com.xiaojinzi.tally.module.core.module.category_sub_info.view

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.core.view.WindowCompat
import com.xiaojinzi.component.anno.AttrValueAutowiredAnno
import com.xiaojinzi.component.anno.RouterAnno
import com.xiaojinzi.module.common.base.interceptor.AlphaInAnimInterceptor
import com.xiaojinzi.support.activity_stack.ActivityFlag
import com.xiaojinzi.support.annotation.ViewLayer
import com.xiaojinzi.support.compose.StateBar
import com.xiaojinzi.support.ktx.initOnceUseViewModel
import com.xiaojinzi.tally.lib.res.ui.APP_ACTIVITY_FLAG_SYNC_SKIP
import com.xiaojinzi.tally.module.base.support.AppRouterConfig
import com.xiaojinzi.tally.module.base.support.alphaOutWhenFinish
import com.xiaojinzi.tally.module.base.theme.AppTheme
import com.xiaojinzi.tally.module.base.view.BaseBusinessAct
import com.xiaojinzi.tally.module.core.module.category_sub_info.domain.CategorySubInfoIntent
import kotlinx.coroutines.InternalCoroutinesApi

@ActivityFlag(
    value = [
        APP_ACTIVITY_FLAG_SYNC_SKIP,
    ],
)
@RouterAnno(
    hostAndPath = AppRouterConfig.CORE_CATEGORY_SUB_INFO,
    interceptors = [
        AlphaInAnimInterceptor::class,
    ],
)
@ViewLayer
class CategorySubInfoAct : BaseBusinessAct<CategorySubInfoViewModel>() {

    @AttrValueAutowiredAnno("parentId")
    lateinit var parentId: String

    override fun getViewModelClass(): Class<CategorySubInfoViewModel> {
        return CategorySubInfoViewModel::class.java
    }

    @OptIn(
        InternalCoroutinesApi::class,
        ExperimentalMaterial3Api::class,
        ExperimentalAnimationApi::class,
        ExperimentalFoundationApi::class,
    )
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        enableEdgeToEdge()
        WindowCompat.setDecorFitsSystemWindows(window, false)

        initOnceUseViewModel {
            requiredViewModel().addIntent(
                intent = CategorySubInfoIntent.ParameterInit(
                    parentId = parentId,
                )
            )
        }

        setContent {
            AppTheme {
                StateBar {
                    CategorySubInfoViewWrap()
                }
            }
        }

    }

    override fun finish() {
        super.finish()
        this.alphaOutWhenFinish()
    }

}