package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.auth.user.UserInfo

/**
 * Supabase 表检查工具
 * 用于检查数据库表结构和数据
 */
object SupabaseTableChecker {
    
    private const val TAG = "SupabaseTableChecker"
    
    /**
     * 检查所有表是否存在以及数据情况
     */
    suspend fun checkAllTables(userId: String? = null): Result<String> = withContext(Dispatchers.IO) {
        try {
            val client = SupabaseConfig.client
            val report = StringBuilder()
            
            report.appendLine("========== Supabase 数据库检查报告 ==========")
            report.appendLine("检查时间: ${System.currentTimeMillis()}")
            if (userId != null) {
                report.appendLine("用户ID: $userId")
            }
            report.appendLine()
            
            // 检查各个表
            val tables = listOf("books", "categories", "accounts", "user_configs")
            
            for (tableName in tables) {
                try {
                    report.appendLine("--- 检查表: $tableName ---")
                    
                    // 尝试查询表结构和数据
                    val query = if (userId != null) {
                        client.from(tableName).select(Columns.ALL) {
                            filter {
                                eq("user_id", userId)
                            }
                            limit(5) // 只取前5条记录
                        }
                    } else {
                        client.from(tableName).select(Columns.ALL) {
                            limit(5) // 只取前5条记录
                        }
                    }
                    
                    val response = query.decodeList<JsonObject>()
                    
                    report.appendLine("✅ 表 $tableName 存在")
                    report.appendLine("记录数量: ${response.size}")
                    
                    if (response.isNotEmpty()) {
                        report.appendLine("示例记录:")
                        response.take(2).forEachIndexed { index, record ->
                            report.appendLine("  记录 ${index + 1}:")
                            record.entries.take(5).forEach { (key, value) ->
                                val displayValue = when (value) {
                                    is JsonPrimitive -> value.content.take(50)
                                    else -> value.toString().take(50)
                                }
                                report.appendLine("    $key: $displayValue")
                            }
                        }
                    } else {
                        report.appendLine("⚠️ 表为空")
                    }
                    
                } catch (e: Exception) {
                    report.appendLine("❌ 表 $tableName 检查失败: ${e.message}")
                    Log.e(TAG, "检查表 $tableName 失败", e)
                }
                
                report.appendLine()
            }
            
            // 检查用户认证状态
            try {
                val auth = SupabaseConfig.auth
                val currentUser = auth.currentUserOrNull()

                report.appendLine("--- 用户认证状态 ---")
                if (currentUser != null) {
                    report.appendLine("✅ 用户已认证")
                    report.appendLine("用户ID: ${currentUser.id}")
                    report.appendLine("邮箱: ${currentUser.email}")
                } else {
                    report.appendLine("❌ 用户未认证")
                }

            } catch (e: Exception) {
                report.appendLine("❌ 检查认证状态失败: ${e.message}")
            }
            
            report.appendLine("==========================================")
            
            val reportString = report.toString()
            Log.d(TAG, reportString)
            
            Result.success(reportString)
            
        } catch (e: Exception) {
            val errorMsg = "数据库检查失败: ${e.message}"
            Log.e(TAG, errorMsg, e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查特定表的详细信息
     */
    suspend fun checkTable(tableName: String, userId: String? = null): Result<String> = withContext(Dispatchers.IO) {
        try {
            val client = SupabaseConfig.client
            val report = StringBuilder()
            
            report.appendLine("========== 表 $tableName 详细检查 ==========")
            
            val query = if (userId != null) {
                client.from(tableName).select(Columns.ALL) {
                    filter {
                        eq("user_id", userId)
                    }
                }
            } else {
                client.from(tableName).select(Columns.ALL)
            }
            
            val response = query.decodeList<JsonObject>()
            
            report.appendLine("总记录数: ${response.size}")
            report.appendLine()
            
            if (response.isNotEmpty()) {
                // 显示字段结构
                val firstRecord = response.first()
                report.appendLine("字段结构:")
                firstRecord.keys.forEach { key ->
                    report.appendLine("  - $key")
                }
                report.appendLine()
                
                // 显示所有记录
                report.appendLine("所有记录:")
                response.forEachIndexed { index, record ->
                    report.appendLine("记录 ${index + 1}:")
                    record.entries.forEach { (key, value) ->
                        val displayValue = when (value) {
                            is JsonPrimitive -> value.content
                            else -> value.toString()
                        }
                        report.appendLine("  $key: $displayValue")
                    }
                    report.appendLine()
                }
            } else {
                report.appendLine("⚠️ 表中没有数据")
            }
            
            report.appendLine("==========================================")
            
            val reportString = report.toString()
            Log.d(TAG, reportString)
            
            Result.success(reportString)
            
        } catch (e: Exception) {
            val errorMsg = "检查表 $tableName 失败: ${e.message}"
            Log.e(TAG, errorMsg, e)
            Result.failure(e)
        }
    }
    
    /**
     * 打印当前用户的所有数据
     */
    suspend fun printCurrentUserData() {
        try {
            val auth = SupabaseConfig.auth
            val currentUser = auth.currentUserOrNull()

            if (currentUser == null) {
                Log.w(TAG, "用户未登录，无法查询数据")
                return
            }

            Log.d(TAG, "========== 当前用户数据报告 ==========")
            Log.d(TAG, "用户ID: ${currentUser.id}")
            Log.d(TAG, "邮箱: ${currentUser.email}")

            val report = checkAllTables(currentUser.id).getOrNull()
            if (report != null) {
                println(report)
            }

        } catch (e: Exception) {
            Log.e(TAG, "打印用户数据失败", e)
        }
    }
}
