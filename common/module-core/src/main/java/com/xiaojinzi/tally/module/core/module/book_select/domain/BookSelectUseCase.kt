package com.xiaojinzi.tally.module.core.module.book_select.domain

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.annotation.UiContext
import com.xiaojinzi.reactive.anno.IntentProcess
import com.xiaojinzi.reactive.template.domain.BusinessUseCase
import com.xiaojinzi.reactive.template.domain.BusinessUseCaseImpl
import com.xiaojinzi.reactive.template.domain.CommonUseCase
import com.xiaojinzi.reactive.template.domain.CommonUseCaseImpl
import com.xiaojinzi.reactive.template.domain.DialogUseCase
import com.xiaojinzi.support.annotation.StateHotObservable
import com.xiaojinzi.support.annotation.ViewModelLayer
import com.xiaojinzi.support.ktx.MutableSharedStateFlow
import com.xiaojinzi.support.ktx.getActivity
import com.xiaojinzi.support.ktx.toStringItemDto
import com.xiaojinzi.tally.lib.res.model.tally.TallyBookDto
import com.xiaojinzi.tally.module.base.support.AppServices
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull

sealed class BookSelectIntent {

    data object Submit : BookSelectIntent()

    data class ParameterInit(
        val maxCount: Int? = null,
        val bookIdSelectList: Set<String>,
    )

    data class SwitchBook(
        val bookId: String,
    ) : BookSelectIntent()

    data class SelectBook(
        val bookId: String,
    ) : BookSelectIntent()

    data class ReturnSelectList(
        @UiContext val context: Context,
    ) : BookSelectIntent()

    /**
     * 请求删除账本（24小时确认期）
     */
    data class RequestDeleteBook(
        val bookId: String,
    ) : BookSelectIntent()

    /**
     * 取消删除账本
     */
    data class CancelDeleteBook(
        val bookId: String,
    ) : BookSelectIntent()

    /**
     * 确认删除账本（立即删除，用于定时任务或手动确认）
     */
    data class ConfirmDeleteBook(
        val bookId: String,
    ) : BookSelectIntent()

}

@ViewModelLayer
interface BookSelectUseCase : BusinessUseCase {

    /**
     * 最大选择数量
     */
    @StateHotObservable
    val maxSelectCountStateOb: Flow<Int?>

    /**
     * 所有的账本
     */
    @StateHotObservable
    val allBookStateOb: Flow<List<TallyBookDto>>

    /**
     * 选择的账本的 id 列表
     */
    @StateHotObservable
    val bookIdSelectListStateOb: Flow<Set<String>>

}

@ViewModelLayer
class BookSelectUseCaseImpl(
    private val commonUseCase: CommonUseCase = CommonUseCaseImpl(),
) : BusinessUseCaseImpl(
    commonUseCase = commonUseCase,
), BookSelectUseCase {

    override val maxSelectCountStateOb = MutableSharedStateFlow<Int?>(
        initValue = null,
    )

    override val allBookStateOb = combine(
        AppServices.userSpi.latestUserIdStateOb,
        AppServices
            .tallyDataSourceSpi
            .allBookStateOb,
    ) { userId, bookList ->
        val (list1, list2) = bookList.partition {
            it.userId == userId
        }
        list1.sortedBy { it.timeCreate } + list2.sortedBy { it.timeCreate }
    }

    override val bookIdSelectListStateOb = MutableSharedStateFlow<Set<String>>(
        initValue = emptySet(),
    )

    @IntentProcess
    private suspend fun parameterInit(intent: BookSelectIntent.ParameterInit) {
        maxSelectCountStateOb.emit(
            value = intent.maxCount,
        )
        bookIdSelectListStateOb.emit(
            value = intent.bookIdSelectList,
        )
    }

    @IntentProcess
    private suspend fun selectBook(intent: BookSelectIntent.SelectBook) {
        val maxCount = maxSelectCountStateOb.firstOrNull()
        bookIdSelectListStateOb.emit(
            value = bookIdSelectListStateOb.first().toMutableSet().apply {
                if (contains(element = intent.bookId)) {
                    this.remove(element = intent.bookId)
                } else {
                    if (maxCount != null && maxCount <= this.size) {
                        if (maxCount == 1) {
                            this.clear()
                            this.add(element = intent.bookId)
                        } else {
                            tip(
                                content = "最多只能选择 $maxCount 个账本".toStringItemDto(),
                            )
                        }
                    } else {
                        this.add(element = intent.bookId)
                    }
                }
            },
        )
    }

    @IntentProcess
    private suspend fun switchBook(intent: BookSelectIntent.SwitchBook) {
        AppServices
            .tallyDataSourceSpi
            .switchBook(
                bookId = intent.bookId,
                isTipAfterSwitch = true,
            )
        postActivityFinishEvent()
    }

    @IntentProcess
    private suspend fun requestDeleteBook(intent: BookSelectIntent.RequestDeleteBook) {
        try {
            // 检查是否是最后一个账本
            val allBooks = AppServices.tallyDataSourceSpi.getAllBookList()
            val activeBooks = allBooks.filter { !it.isPendingDelete }

            if (activeBooks.size <= 1) {
                tip(content = "不能删除最后一个账本".toStringItemDto())
                return
            }

            // 检查是否是当前使用的账本
            val currentBook = AppServices.tallyDataSourceSpi.requiredSelectedBookInfo()
            if (currentBook.id == intent.bookId) {
                // 需要先切换到其他账本
                val otherBook = activeBooks.firstOrNull { it.id != intent.bookId }
                if (otherBook != null) {
                    when (
                        confirmDialog(
                            content = "删除当前账本需要先切换到其他账本，是否继续？".toStringItemDto(),
                            positive = "继续".toStringItemDto(),
                            negative = "取消".toStringItemDto(),
                        )
                    ) {
                        DialogUseCase.ConfirmDialogResultType.CONFIRM -> {
                            AppServices.tallyDataSourceSpi.switchBook(
                                bookId = otherBook.id,
                                isTipAfterSwitch = false,
                            )
                        }
                        else -> return
                    }
                } else {
                    tip(content = "无法找到其他可用账本".toStringItemDto())
                    return
                }
            }

            // 确认删除对话框
            when (
                confirmDialog(
                    content = "账本删除后有24小时的确认期，期间可以取消删除。确定要删除这个账本吗？".toStringItemDto(),
                    positive = "删除".toStringItemDto(),
                    negative = "取消".toStringItemDto(),
                )
            ) {
                DialogUseCase.ConfirmDialogResultType.CONFIRM -> {
                    // 执行删除请求
                    AppServices.tallyDataSourceSpi.requestDeleteBook(intent.bookId)
                    tip(content = "账本已标记为删除，24小时后将永久删除".toStringItemDto())
                }
                else -> {}
            }

        } catch (e: Exception) {
            tip(content = "删除失败: ${e.message}".toStringItemDto())
        }
    }

    @IntentProcess
    private suspend fun cancelDeleteBook(intent: BookSelectIntent.CancelDeleteBook) {
        try {
            AppServices.tallyDataSourceSpi.cancelDeleteBook(intent.bookId)
            tip(content = "已取消删除".toStringItemDto())
        } catch (e: Exception) {
            tip(content = "取消删除失败: ${e.message}".toStringItemDto())
        }
    }

    @IntentProcess
    private suspend fun confirmDeleteBook(intent: BookSelectIntent.ConfirmDeleteBook) {
        try {
            AppServices.tallyDataSourceSpi.confirmDeleteBook(intent.bookId)
            tip(content = "账本已永久删除".toStringItemDto())
        } catch (e: Exception) {
            tip(content = "删除失败: ${e.message}".toStringItemDto())
        }
    }

    @IntentProcess
    private suspend fun returnSelectList(intent: BookSelectIntent.ReturnSelectList) {
        intent
            .context
            .getActivity()?.run {
                this.setResult(
                    Activity.RESULT_OK,
                    Intent().apply {
                        this.putStringArrayListExtra(
                            "data", ArrayList(
                                bookIdSelectListStateOb.first(),
                            )
                        )
                    }
                )
                this.finish()
            }
    }

    override fun destroy() {
        super.destroy()
        commonUseCase.destroy()
    }

}