-- =====================================================
-- 更新用户数据初始化函数（包含丰富的类别数据）
-- =====================================================

CREATE OR REPLACE FUNCTION initialize_user_data(p_user_id TEXT, p_user_name TEXT DEFAULT '用户')
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_book_id UUID;
    v_account_id UUID;
    v_current_time BIGINT;
    v_result JSON;
    v_category_count INTEGER := 0;

    -- 主类别ID变量
    v_shopping_id UUID;
    v_food_id UUID;
    v_transport_id UUID;
    v_entertainment_id UUID;
    v_social_id UUID;
    v_health_id UUID;
    v_home_id UUID;
    v_other_spending_id UUID;
BEGIN
    -- 获取当前时间戳（毫秒）
    v_current_time := EXTRACT(EPOCH FROM NOW()) * 1000;

    -- 检查用户是否已经初始化
    IF EXISTS (SELECT 1 FROM public.user_books WHERE user_id = p_user_id::uuid AND is_deleted = false) THEN
        RETURN json_build_object(
            'success', true,
            'message', '用户数据已存在',
            'already_initialized', true,
            'data', json_build_object(
                'user_id', p_user_id,
                'user_name', p_user_name
            )
        );
    END IF;

    -- 生成唯一ID
    v_book_id := gen_random_uuid();
    v_account_id := gen_random_uuid();
    v_shopping_id := gen_random_uuid();
    v_food_id := gen_random_uuid();
    v_transport_id := gen_random_uuid();
    v_entertainment_id := gen_random_uuid();
    v_social_id := gen_random_uuid();
    v_health_id := gen_random_uuid();
    v_home_id := gen_random_uuid();
    v_other_spending_id := gen_random_uuid();

    RAISE NOTICE '🚀 开始为用户 % 初始化数据', p_user_id;

    -- 1. 创建默认账本
    INSERT INTO public.user_books (
        id, user_id, is_system, name, icon_name,
        created_at, updated_at, is_deleted
    ) VALUES (
        v_book_id, p_user_id::uuid, true, '我的账本', 'book1',
        NOW(), NOW(), false
    );

    RAISE NOTICE '📖 创建账本成功: %', v_book_id;

    -- 2. 创建主要支出类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (v_shopping_id, p_user_id::uuid, v_book_id, NULL, 'spending', '购物消费', 'shopping1', 1, NOW(), NOW(), false),
    (v_food_id, p_user_id::uuid, v_book_id, NULL, 'spending', '食品餐饮', 'chopsticksFork1', 2, NOW(), NOW(), false),
    (v_transport_id, p_user_id::uuid, v_book_id, NULL, 'spending', '出行交通', 'taxi1', 3, NOW(), NOW(), false),
    (v_entertainment_id, p_user_id::uuid, v_book_id, NULL, 'spending', '休闲娱乐', 'gamePad1', 4, NOW(), NOW(), false),
    (v_social_id, p_user_id::uuid, v_book_id, NULL, 'spending', '人情世故', 'gift1', 5, NOW(), NOW(), false),
    (v_health_id, p_user_id::uuid, v_book_id, NULL, 'spending', '健康医疗', 'firstAidKit1', 6, NOW(), NOW(), false),
    (v_home_id, p_user_id::uuid, v_book_id, NULL, 'spending', '居家生活', 'house1', 7, NOW(), NOW(), false),
    (v_other_spending_id, p_user_id::uuid, v_book_id, NULL, 'spending', '其他支出', 'expenses1', 8, NOW(), NOW(), false);

    -- 3. 创建购物消费子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '日常家居', 'nightstand1', 11, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '个护美妆', 'lipstick1', 12, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '手机数码', 'airpods1', 13, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '虚拟充值', 'money1', 14, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '生活电器', 'washingMachine1', 15, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '配饰腕表', 'watch1', 16, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '母婴玩具', 'babyBottle1', 17, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '服饰运动', 'cardigan1', 18, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '宠物用品', 'dog1', 19, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_shopping_id, 'spending', '办公用品', 'printer1', 20, NOW(), NOW(), false);

    -- 4. 创建食品餐饮子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '早餐', 'bread1', 21, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '午餐', 'rice1', 22, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '晚餐', 'noodles1', 23, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '饮料酒水', 'cocktail1', 24, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '休闲零食', 'candy1', 25, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '生鲜食品', 'croissant1', 26, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_food_id, 'spending', '请客吃饭', 'cookingPot1', 27, NOW(), NOW(), false);

    -- 5. 创建出行交通子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_transport_id, 'spending', '公共交通', 'bus1', 31, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_transport_id, 'spending', '打车出行', 'taxi1', 32, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_transport_id, 'spending', '私家车费', 'car1', 33, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_transport_id, 'spending', '火车高铁', 'train1', 34, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_transport_id, 'spending', '飞机出行', 'airplane1', 35, NOW(), NOW(), false);

    -- 6. 创建休闲娱乐子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_entertainment_id, 'spending', '电影演出', 'movie1', 41, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_entertainment_id, 'spending', '游戏娱乐', 'gamePad1', 42, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_entertainment_id, 'spending', '运动健身', 'dumbbell1', 43, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_entertainment_id, 'spending', '旅游度假', 'suitcase1', 44, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_entertainment_id, 'spending', '学习培训', 'book1', 45, NOW(), NOW(), false);

    -- 7. 创建健康医疗子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_health_id, 'spending', '医院就诊', 'hospital1', 51, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_health_id, 'spending', '买药保健', 'pill1', 52, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_health_id, 'spending', '体检疫苗', 'stethoscope1', 53, NOW(), NOW(), false);

    -- 8. 创建居家生活子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_home_id, 'spending', '房租房贷', 'house1', 61, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_home_id, 'spending', '水电燃气', 'lightbulb1', 62, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_home_id, 'spending', '通讯费用', 'phone1', 63, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_home_id, 'spending', '物业管理', 'building1', 64, NOW(), NOW(), false);

    -- 9. 创建人情世故子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_social_id, 'spending', '红包礼金', 'gift1', 71, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, v_social_id, 'spending', '慈善捐助', 'heart1', 72, NOW(), NOW(), false);

    -- 10. 创建收入类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '工资收入', 'wage1', 101, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '奖金补贴', 'bonus1', 102, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '兼职外快', 'partTimeJob1', 103, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '投资收益', 'invest1', 104, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '借入资金', 'income1', 105, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id::uuid, v_book_id, NULL, 'income', '其他收入', 'money1', 106, NOW(), NOW(), false);

    -- 统计创建的类别数量
    SELECT COUNT(*) INTO v_category_count
    FROM public.book_categories
    WHERE user_id = p_user_id::uuid AND book_id = v_book_id;

    RAISE NOTICE '📊 创建类别成功: %个', v_category_count;

    -- 11. 创建默认账户
    INSERT INTO public.book_accounts (
        id, user_id, book_id, name, icon_name,
        balance_init, balance_current, is_excluded, is_default,
        created_at, updated_at, is_deleted
    ) VALUES (
        v_account_id, p_user_id::uuid, v_book_id, '现金', 'cash1',
        0, 0, false, true,
        NOW(), NOW(), false
    );

    RAISE NOTICE '💰 创建账户成功: %', v_account_id;

    -- 12. 创建用户配置（如果表存在）
    BEGIN
        INSERT INTO public.user_configs (user_id, config_key, config_value, config_type, description) VALUES
        (p_user_id::uuid, 'default_book_id', v_book_id::TEXT, 'string', '默认选中的账本ID'),
        (p_user_id::uuid, 'currency', 'CNY', 'string', '默认货币单位'),
        (p_user_id::uuid, 'theme', 'system', 'string', '主题设置'),
        (p_user_id::uuid, 'show_assets_tab', 'true', 'boolean', '是否显示资产标签页');
        RAISE NOTICE '⚙️ 创建用户配置成功';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '⚠️ 用户配置创建失败: %', SQLERRM;
    END;

    -- 返回成功结果
    v_result := json_build_object(
        'success', true,
        'message', '用户数据初始化成功',
        'data', json_build_object(
            'user_id', p_user_id,
            'book_id', v_book_id::TEXT,
            'account_id', v_account_id::TEXT,
            'category_count', v_category_count,
            'user_name', p_user_name,
            'created_at', v_current_time
        )
    );

    RAISE NOTICE '✅ 用户 % 数据初始化完成！共创建%个类别', p_user_id, v_category_count;
    RETURN v_result;

EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE '❌ 初始化失败: %', SQLERRM;
    -- 返回错误信息
    RETURN json_build_object(
        'success', false,
        'message', '初始化失败: ' || SQLERRM,
        'error_code', SQLSTATE
    );
END;
$$;

-- 清除现有数据并重新初始化
DELETE FROM public.book_categories WHERE user_id = '9eeeb0ef-e537-463f-b981-dfbed880b87a'::uuid;
DELETE FROM public.book_accounts WHERE user_id = '9eeeb0ef-e537-463f-b981-dfbed880b87a'::uuid;
DELETE FROM public.user_books WHERE user_id = '9eeeb0ef-e537-463f-b981-dfbed880b87a'::uuid;
DELETE FROM public.user_configs WHERE user_id = '9eeeb0ef-e537-463f-b981-dfbed880b87a'::uuid;

-- 重新初始化用户数据
SELECT initialize_user_data('9eeeb0ef-e537-463f-b981-dfbed880b87a', '测试用户');