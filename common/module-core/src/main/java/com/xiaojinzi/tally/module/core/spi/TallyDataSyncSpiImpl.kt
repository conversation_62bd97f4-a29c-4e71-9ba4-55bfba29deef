package com.xiaojinzi.tally.module.core.spi

import android.util.Log
import com.xiaojinzi.component.anno.ServiceAnno
import com.xiaojinzi.support.ktx.AppScope
import com.xiaojinzi.tally.module.base.spi.TallyDataSyncSpi
import com.xiaojinzi.tally.module.base.support.AppServices
import com.xiaojinzi.tally.module.core.supabase.SupabaseDataService
import com.xiaojinzi.tally.module.base.spi.TallyDataSourceSpi
import com.xiaojinzi.tally.lib.res.model.tally.MoneyFen
import com.xiaojinzi.tally.lib.res.model.tally.TallyBillInsertDto
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Supabase 数据同步实现
 * 
 * 负责将本地未同步的数据推送到 Supabase
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-25
 */
@ServiceAnno(TallyDataSyncSpi::class)
class TallyDataSyncSpiImpl : TallyDataSyncSpi {
    
    companion object {
        private const val TAG = "TallyDataSyncSpiImpl"
    }
    
    private val _isSyncingStateFlow = MutableStateFlow(false)
    override val isSyncingStateOb: Flow<Boolean> = _isSyncingStateFlow.asStateFlow()
    
    private var syncEnabled = true
    
    override fun setSyncSwitch(enable: Boolean) {
        syncEnabled = enable
        Log.d(TAG, "同步开关设置为: $enable")
    }
    
    override fun trySync(bookIdList: List<String>?) {
        if (!syncEnabled) {
            Log.d(TAG, "同步已禁用，跳过同步")
            return
        }
        
        Log.d(TAG, "🔄 开始尝试同步数据: bookIdList=$bookIdList")
        
        // 在协程中执行同步
        CoroutineScope(Dispatchers.IO).launch {
            try {
                _isSyncingStateFlow.value = true
                
                if (bookIdList.isNullOrEmpty()) {
                    // 同步所有账本
                    syncAllBooks()
                } else {
                    // 同步指定账本
                    bookIdList.forEach { bookId ->
                        syncSingleBook(bookId)
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 数据同步失败: ${e.message}", e)
            } finally {
                _isSyncingStateFlow.value = false
            }
        }
    }
    
    /**
     * 同步所有账本
     */
    private suspend fun syncAllBooks() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步所有账本...")
            
            val allBooks = AppServices.tallyDataSourceSpi.getAllBookList()
            Log.d(TAG, "📋 找到 ${allBooks.size} 个账本需要同步")
            
            allBooks.forEach { book ->
                syncSingleBook(book.id)
            }
            
            Log.d(TAG, "✅ 所有账本同步完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步所有账本失败: ${e.message}", e)
        }
    }
    
    /**
     * 同步单个账本
     */
    private suspend fun syncSingleBook(bookId: String) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步账本: $bookId")
            
            // 同步类别数据
            syncCategoriesForBook(bookId)
            
            // 同步账户数据
            syncAccountsForBook(bookId)

            // 先将本地账单同步到Supabase（上行同步）
            syncBillsForBook(bookId)

            // 再从Supabase同步账单到本地（下行同步）
            syncBillsFromSupabase(bookId)
            
            Log.d(TAG, "✅ 账本同步完成: $bookId")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步账本失败: $bookId - ${e.message}", e)
        }
    }
    
    /**
     * 同步账本的类别数据
     */
    private suspend fun syncCategoriesForBook(bookId: String) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步类别数据: bookId=$bookId")
            
            // 获取未同步的类别
            val unSyncCategories = AppServices.tallyDataSourceSpi.getUnSyncCategoryList(
                bookId = bookId,
                pageSize = 100 // 一次最多同步100个
            )
            
            if (unSyncCategories.isEmpty()) {
                Log.d(TAG, "✅ 没有需要同步的类别数据")
                return@withContext
            }
            
            Log.d(TAG, "📤 准备同步 ${unSyncCategories.size} 个类别到 Supabase...")
            
            // 将类别数据推送到 Supabase
            unSyncCategories.forEach { category ->
                try {
                    // 调用 SupabaseDataService 智能同步类别（自动判断插入或更新）
                    val result = SupabaseDataService.upsertCategoryInSupabase(category)
                    if (result.isSuccess) {
                        // 标记为已同步
                        AppServices.tallyDataSourceSpi.updateCategory(
                            target = category.copy(isSync = true),
                            isNeedSync = false // 避免循环同步
                        )
                        Log.d(TAG, "✅ 类别同步成功: ${category.name}")
                    } else {
                        Log.w(TAG, "⚠️ 类别同步失败: ${category.name} - ${result.exceptionOrNull()?.message}")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "❌ 类别同步异常: ${category.name} - ${e.message}")
                }
            }
            
            Log.d(TAG, "✅ 类别数据同步完成")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步类别数据失败: ${e.message}", e)
        }
    }
    
    /**
     * 同步账本的账户数据
     */
    private suspend fun syncAccountsForBook(bookId: String) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始同步账户数据: bookId=$bookId")
            
            // 获取未同步的账户
            val unSyncAccounts = AppServices.tallyDataSourceSpi.getUnSyncAccountList(
                bookId = bookId,
                pageSize = 100
            )
            
            if (unSyncAccounts.isEmpty()) {
                Log.d(TAG, "✅ 没有需要同步的账户数据")
                return@withContext
            }
            
            Log.d(TAG, "📤 准备同步 ${unSyncAccounts.size} 个账户到 Supabase...")
            
            // TODO: 实现账户同步逻辑
            Log.d(TAG, "⚠️ 账户同步功能待实现")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 同步账户数据失败: ${e.message}", e)
        }
    }
    
    /**
     * 从Supabase同步账单到本地
     */
    private suspend fun syncBillsFromSupabase(bookId: String) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🔄 开始从Supabase同步账单到本地: bookId=$bookId")

            // 获取当前用户ID
            val userId = AppServices.userSpi.requiredLastUserId()

            // 从Supabase获取账单数据
            val billsResult = SupabaseDataService.getBillsFromSupabase(bookId, userId)

            if (billsResult.isFailure) {
                Log.e(TAG, "❌ 从Supabase获取账单失败: ${billsResult.exceptionOrNull()?.message}")
                return@withContext
            }

            val supabaseBills = billsResult.getOrNull() ?: emptyList()
            Log.d(TAG, "📥 从Supabase获取到 ${supabaseBills.size} 条账单")

            if (supabaseBills.isEmpty()) {
                Log.d(TAG, "✅ Supabase中没有账单数据")
                return@withContext
            }

            // 获取本地已存在的账单列表
            val localBills = AppServices.tallyDataSourceSpi.getBillDetailListByCondition(
                queryCondition = TallyDataSourceSpi.Companion.BillQueryConditionDto(
                    bookIdList = listOf(bookId)
                )
            )
            val localBillMap = localBills.associate { it.core.id to it.core }

            // 找出需要同步的账单（新账单 + 状态变化的账单）
            val billsToSync = supabaseBills.filter { supabaseBill ->
                val localBill = localBillMap[supabaseBill.id]
                if (localBill == null) {
                    // 本地不存在，是新账单
                    true
                } else {
                    // 本地存在，检查是否有状态变化（比较修改时间或删除状态）
                    supabaseBill.time_modify != localBill.timeModify ||
                    supabaseBill.is_deleted != localBill.isDeleted
                }
            }

            Log.d(TAG, "📊 需要同步到本地的账单: ${billsToSync.size} 条（新增+更新）")

            if (billsToSync.isEmpty()) {
                Log.d(TAG, "✅ 所有账单状态已同步")
                return@withContext
            }

            // 将Supabase账单转换为本地格式并插入
            val insertDtos = billsToSync.map { supabaseBill ->
                TallyBillInsertDto(
                    id = supabaseBill.id,
                    userId = supabaseBill.user_id,
                    bookId = supabaseBill.book_id,
                    type = supabaseBill.type, // 直接使用字符串类型
                    originBillId = supabaseBill.origin_bill_id,
                    time = supabaseBill.time,
                    categoryId = supabaseBill.category_id,
                    accountId = supabaseBill.account_id,
                    transferTargetAccountId = supabaseBill.transfer_target_account_id,
                    amount = MoneyFen(value = supabaseBill.amount),
                    note = supabaseBill.note,
                    isNotCalculate = supabaseBill.is_not_calculate,
                    timeCreate = supabaseBill.time_create,
                    timeModify = supabaseBill.time_modify,
                    isDeleted = supabaseBill.is_deleted,
                    isSync = true // 从云端同步的数据标记为已同步
                )
            }

            // 批量插入到本地数据库
            AppServices.tallyDataSourceSpi.insertOrUpdateBillList(
                targetList = insertDtos,
                isNeedSync = false // 避免循环同步
            )

            Log.d(TAG, "✅ 成功同步 ${billsToSync.size} 条账单到本地")

        } catch (e: Exception) {
            Log.e(TAG, "❌ 从Supabase同步账单失败: ${e.message}", e)
        }
    }

    /**
     * 同步账本的账单数据（上行同步：本地→Supabase）
     */
    private suspend fun syncBillsForBook(bookId: String) = withContext(Dispatchers.IO) {
        val startTime = System.currentTimeMillis()
        var successCount = 0
        var failureCount = 0

        try {
            Log.d(TAG, "========== 开始账单同步流程 ==========")
            Log.d(TAG, "🔄 同步目标: bookId=$bookId")
            Log.d(TAG, "⏰ 同步开始时间: ${java.text.SimpleDateFormat("HH:mm:ss.SSS", java.util.Locale.getDefault()).format(java.util.Date())}")

            // 获取未同步的账单
            Log.d(TAG, "📋 正在查询未同步的账单...")
            val unSyncBills = AppServices.tallyDataSourceSpi.getUnSyncBillList(
                bookId = bookId,
                pageSize = 50 // 账单数据较大，一次同步50个
            )

            // 特殊测试：直接查询特定的已删除账单
            val testBillId = "be6a16cf-4d86-4193-ac1e-70f4ef0220bf"
            val testBill = AppServices.tallyDataSourceSpi.getBillDetailById(id = testBillId)
            Log.d(TAG, "🧪 测试查询特定账单: ID=$testBillId")
            Log.d(TAG, "🧪 测试结果: isDeleted=${testBill?.core?.isDeleted}, isSync=${testBill?.core?.isSync}")
            Log.d(TAG, "🔍 查询详情: 查询到 ${unSyncBills.size} 个未同步账单")
            unSyncBills.forEach { bill ->
                Log.d(TAG, "📄 未同步账单: ID=${bill.id}, isDeleted=${bill.isDeleted}, isSync=${bill.isSync}")
            }

            Log.d(TAG, "📊 查询结果: 发现 ${unSyncBills.size} 个未同步账单")

            if (unSyncBills.isEmpty()) {
                Log.d(TAG, "✅ 没有需要同步的账单数据，同步流程结束")
                Log.d(TAG, "========== 账单同步流程完成 ==========")
                return@withContext
            }

            Log.d(TAG, "📤 开始批量同步 ${unSyncBills.size} 个账单到 Supabase...")
            Log.d(TAG, "🎯 同步策略: 逐个处理，失败不中断")

            // 将账单数据推送到 Supabase
            unSyncBills.forEachIndexed { index, bill ->
                val billProgress = "${index + 1}/${unSyncBills.size}"
                Log.d(TAG, "🔄 处理账单 [$billProgress]: ID=${bill.id}")
                Log.d(TAG, "📋 账单详情: 备注=${bill.note ?: "无备注"}, 金额=${bill.amount.value}")

                try {
                    // 调用 SupabaseDataService 智能同步账单（自动判断插入或更新）
                    val syncStartTime = System.currentTimeMillis()
                    val result = SupabaseDataService.upsertBillInSupabase(bill)
                    val syncDuration = System.currentTimeMillis() - syncStartTime

                    if (result.isSuccess) {
                        // 标记为已同步
                        AppServices.tallyDataSourceSpi.updateBill(
                            target = bill.copy(isSync = true),
                            isNeedSync = false // 避免循环同步
                        )
                        successCount++
                        Log.d(TAG, "✅ 账单同步成功 [$billProgress]: ${bill.note ?: "无备注"} (耗时: ${syncDuration}ms)")
                    } else {
                        failureCount++
                        val errorMsg = result.exceptionOrNull()?.message ?: "未知错误"
                        Log.w(TAG, "⚠️ 账单同步失败 [$billProgress]: ${bill.note ?: "无备注"} - $errorMsg")
                        Log.w(TAG, "🔍 失败详情: ID=${bill.id}, 用户=${bill.userId}")
                    }
                } catch (e: Exception) {
                    failureCount++
                    Log.e(TAG, "❌ 账单同步异常 [$billProgress]: ${bill.note ?: "无备注"}", e)
                    Log.e(TAG, "💥 异常详情: ${e.javaClass.simpleName} - ${e.message}")
                }

                // 每10个账单输出一次进度统计
                if ((index + 1) % 10 == 0 || index == unSyncBills.size - 1) {
                    val currentProgress = ((index + 1).toFloat() / unSyncBills.size * 100).toInt()
                    Log.d(TAG, "📈 同步进度: $currentProgress% (${index + 1}/${unSyncBills.size}), 成功: $successCount, 失败: $failureCount")
                }
            }

            val totalDuration = System.currentTimeMillis() - startTime
            Log.d(TAG, "========== 账单同步统计报告 ==========")
            Log.d(TAG, "📊 总计处理: ${unSyncBills.size} 个账单")
            Log.d(TAG, "✅ 同步成功: $successCount 个")
            Log.d(TAG, "❌ 同步失败: $failureCount 个")
            Log.d(TAG, "📈 成功率: ${if (unSyncBills.isNotEmpty()) (successCount * 100 / unSyncBills.size) else 0}%")
            Log.d(TAG, "⏱️ 总耗时: ${totalDuration}ms")
            Log.d(TAG, "⚡ 平均耗时: ${if (unSyncBills.isNotEmpty()) totalDuration / unSyncBills.size else 0}ms/个")
            Log.d(TAG, "========== 账单同步流程完成 ==========")

        } catch (e: Exception) {
            val totalDuration = System.currentTimeMillis() - startTime
            Log.e(TAG, "❌ 账单同步流程异常终止", e)
            Log.e(TAG, "💥 异常详情: ${e.javaClass.simpleName} - ${e.message}")
            Log.e(TAG, "📊 异常前统计: 成功 $successCount 个, 失败 $failureCount 个, 耗时 ${totalDuration}ms")
            Log.e(TAG, "========== 账单同步流程异常结束 ==========")
        }
    }
}
