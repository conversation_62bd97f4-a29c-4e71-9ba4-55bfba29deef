-- =====================================================
-- 增强版默认分类创建函数（包含完整的50个分类）
-- =====================================================

CREATE OR REPLACE FUNCTION create_default_categories_for_book_enhanced(p_user_id UUID, p_book_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER := 0;
    v_current_time BIGINT := EXTRACT(EPOCH FROM NOW()) * 1000;
    
    -- 主类别ID变量
    v_shopping_id UUID;
    v_food_id UUID;
    v_transport_id UUID;
    v_entertainment_id UUID;
    v_social_id UUID;
    v_health_id UUID;
    v_home_id UUID;
    v_other_spending_id UUID;
BEGIN
    -- 生成主类别ID
    v_shopping_id := gen_random_uuid();
    v_food_id := gen_random_uuid();
    v_transport_id := gen_random_uuid();
    v_entertainment_id := gen_random_uuid();
    v_social_id := gen_random_uuid();
    v_health_id := gen_random_uuid();
    v_home_id := gen_random_uuid();
    v_other_spending_id := gen_random_uuid();

    RAISE NOTICE '🏷️ 开始为账本 % 创建默认分类', p_book_id;

    -- 1. 创建主要支出类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (v_shopping_id, p_user_id, p_book_id, NULL, 'spending', '购物消费', 'shopping1', 1, NOW(), NOW(), false),
    (v_food_id, p_user_id, p_book_id, NULL, 'spending', '食品餐饮', 'chopsticksFork1', 2, NOW(), NOW(), false),
    (v_transport_id, p_user_id, p_book_id, NULL, 'spending', '出行交通', 'taxi1', 3, NOW(), NOW(), false),
    (v_entertainment_id, p_user_id, p_book_id, NULL, 'spending', '休闲娱乐', 'gamePad1', 4, NOW(), NOW(), false),
    (v_social_id, p_user_id, p_book_id, NULL, 'spending', '人情世故', 'gift1', 5, NOW(), NOW(), false),
    (v_health_id, p_user_id, p_book_id, NULL, 'spending', '健康医疗', 'firstAidKit1', 6, NOW(), NOW(), false),
    (v_home_id, p_user_id, p_book_id, NULL, 'spending', '居家生活', 'house1', 7, NOW(), NOW(), false),
    (v_other_spending_id, p_user_id, p_book_id, NULL, 'spending', '其他支出', 'expenses1', 8, NOW(), NOW(), false);

    v_count := v_count + 8;

    -- 2. 创建购物消费子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '日常家居', 'nightstand1', 11, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '个护美妆', 'lipstick1', 12, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '手机数码', 'airpods1', 13, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '虚拟充值', 'money1', 14, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '生活电器', 'washingMachine1', 15, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '配饰腕表', 'watch1', 16, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '母婴玩具', 'babyBottle1', 17, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '服饰运动', 'cardigan1', 18, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '宠物用品', 'dog1', 19, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_shopping_id, 'spending', '办公用品', 'printer1', 20, NOW(), NOW(), false);

    v_count := v_count + 10;

    -- 3. 创建食品餐饮子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '早餐', 'bread1', 21, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '午餐', 'rice1', 22, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '晚餐', 'noodles1', 23, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '饮料酒水', 'cocktail1', 24, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '休闲零食', 'candy1', 25, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '生鲜食品', 'croissant1', 26, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_food_id, 'spending', '请客吃饭', 'cookingPot1', 27, NOW(), NOW(), false);

    v_count := v_count + 7;

    -- 4. 创建出行交通子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_transport_id, 'spending', '公共交通', 'bus1', 31, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_transport_id, 'spending', '打车出行', 'taxi1', 32, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_transport_id, 'spending', '私家车费', 'car1', 33, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_transport_id, 'spending', '火车高铁', 'train1', 34, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_transport_id, 'spending', '飞机出行', 'airplane1', 35, NOW(), NOW(), false);

    v_count := v_count + 5;

    -- 5. 创建休闲娱乐子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_entertainment_id, 'spending', '电影演出', 'movie1', 41, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_entertainment_id, 'spending', '游戏娱乐', 'gamePad1', 42, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_entertainment_id, 'spending', '运动健身', 'dumbbell1', 43, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_entertainment_id, 'spending', '旅游度假', 'suitcase1', 44, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_entertainment_id, 'spending', '学习培训', 'book1', 45, NOW(), NOW(), false);

    v_count := v_count + 5;

    -- 6. 创建健康医疗子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_health_id, 'spending', '医院就诊', 'hospital1', 51, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_health_id, 'spending', '买药保健', 'pill1', 52, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_health_id, 'spending', '体检疫苗', 'stethoscope1', 53, NOW(), NOW(), false);

    v_count := v_count + 3;

    -- 7. 创建居家生活子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_home_id, 'spending', '房租房贷', 'house1', 61, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_home_id, 'spending', '水电燃气', 'lightbulb1', 62, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_home_id, 'spending', '通讯费用', 'phone1', 63, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_home_id, 'spending', '物业管理', 'building1', 64, NOW(), NOW(), false);

    v_count := v_count + 4;

    -- 8. 创建人情世故子类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, v_social_id, 'spending', '红包礼金', 'gift1', 71, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, v_social_id, 'spending', '慈善捐助', 'heart1', 72, NOW(), NOW(), false);

    v_count := v_count + 2;

    -- 9. 创建收入类别
    INSERT INTO public.book_categories (
        id, user_id, book_id, parent_id, type, name, icon_name,
        sort_order, created_at, updated_at, is_deleted
    ) VALUES
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '工资收入', 'wage1', 101, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '奖金补贴', 'bonus1', 102, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '兼职外快', 'partTimeJob1', 103, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '投资收益', 'invest1', 104, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '借入资金', 'income1', 105, NOW(), NOW(), false),
    (gen_random_uuid(), p_user_id, p_book_id, NULL, 'income', '其他收入', 'money1', 106, NOW(), NOW(), false);

    v_count := v_count + 6;

    RAISE NOTICE '✅ 成功创建 % 个默认分类', v_count;
    RETURN v_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
