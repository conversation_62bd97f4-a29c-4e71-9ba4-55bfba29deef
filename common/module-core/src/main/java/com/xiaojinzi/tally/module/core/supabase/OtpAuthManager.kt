package com.xiaojinzi.tally.module.core.supabase

import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.auth.providers.builtin.OTP
import io.github.jan.supabase.auth.user.UserSession
import io.github.jan.supabase.auth.OtpType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * OTP 认证管理器
 * 
 * 负责处理邮箱验证码的发送和验证逻辑
 * 基于 Supabase Auth 3.x API 实现
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-21
 */
class OtpAuthManager {
    
    /**
     * 获取 Supabase 客户端
     * 使用主线程初始化避免协程上下文问题
     */
    private suspend fun getSupabaseClient(): io.github.jan.supabase.SupabaseClient {
        return try {
            println("[OtpAuthManager] 🔄 正在获取 Supabase 客户端...")
            println("[OtpAuthManager] 📍 当前线程: ${Thread.currentThread().name}")
            
            val client = SupabaseConfig.client
            println("[OtpAuthManager] ✅ Supabase 客户端获取成功")
            
            try {
                println("[OtpAuthManager] 🔗 客户端类型: ${client.javaClass.simpleName}")
            } catch (e: Exception) {
                println("[OtpAuthManager] ⚠️  无法获取客户端类型: ${e.message}")
            }
            
            println("[OtpAuthManager] 🎯 客户端获取流程完成")
            client
        } catch (e: Exception) {
            println("[OtpAuthManager] ❌ Supabase 客户端获取失败: ${e.javaClass.simpleName}")
            println("[OtpAuthManager] 💬 初始化异常消息: ${e.message}")
            println("[OtpAuthManager] 📍 失败线程: ${Thread.currentThread().name}")
            e.printStackTrace()
            throw AuthException("Supabase 客户端初始化失败: ${e.message}", e)
        }
    }
    
    /**
         * 发送邮箱验证码
         * 
         * @param email 邮箱地址
         * @param shouldCreateUser 如果用户不存在是否自动创建，默认为 true
         * @return 发送结果，成功返回 true
         * @throws AuthException 认证异常
         */
        suspend fun sendEmailOtp(
            email: String,
            shouldCreateUser: Boolean = true
        ): Boolean = withContext(Dispatchers.IO) {
            try {
                println("[OtpAuthManager] ========== 开始发送邮箱验证码 ==========")
                println("[OtpAuthManager] 邮箱: $email, 创建用户: $shouldCreateUser")
                
                // 获取并验证 Supabase 客户端
                println("[OtpAuthManager] 🔍 开始获取和验证 Supabase 客户端...")
                val client = getSupabaseClient()
                
                // 验证客户端URL
                val clientUrl = try {
                    val url = client.supabaseUrl
                    println("[OtpAuthManager] 🌐 客户端 URL: $url")
                    url
                } catch (e: Exception) {
                    println("[OtpAuthManager] ❌ 获取客户端URL时发生异常: ${e.javaClass.simpleName}")
                    println("[OtpAuthManager] 💬 异常消息: ${e.message}")
                    e.printStackTrace()
                    throw AuthException("无法获取 Supabase 客户端URL: ${e.message}", e)
                }
                
                // 客户端 URL 已验证，继续执行
                
                // 检查认证模块
                println("[OtpAuthManager] 🔐 检查认证模块...")
                val authModule = client.auth
                println("[OtpAuthManager] ✅ 认证模块状态: ${authModule.javaClass.simpleName}")
                
                // 使用 Supabase 3.x API 发送邮箱 OTP
                println("[OtpAuthManager] ✅ 客户端验证通过，准备发送 OTP...")
                
                // Android 模拟器环境检测和诊断
                println("[OtpAuthManager] 🔍 环境诊断...")
                try {
                    val isEmulator = android.os.Build.FINGERPRINT.startsWith("generic") ||
                            android.os.Build.FINGERPRINT.startsWith("unknown") ||
                            android.os.Build.MODEL.contains("google_sdk") ||
                            android.os.Build.MODEL.contains("Emulator") ||
                            android.os.Build.MODEL.contains("Android SDK")
                    
                    if (isEmulator) {
                        println("[OtpAuthManager] ⚠️  检测到 Android 模拟器环境")
                        println("[OtpAuthManager] ⚠️  模拟器可能存在网络限制，这可能影响 OTP 发送")
                        println("[OtpAuthManager] 💡 建议在真实设备上测试")
                    } else {
                        println("[OtpAuthManager] ✅ 检测到真实设备环境")
                    }
                    
                    println("[OtpAuthManager] 📱 设备信息:")
                    println("[OtpAuthManager]    品牌: ${android.os.Build.BRAND}")
                    println("[OtpAuthManager]    型号: ${android.os.Build.MODEL}")
                    println("[OtpAuthManager]    指纹: ${android.os.Build.FINGERPRINT.take(50)}...")
                    
                } catch (e: Exception) {
                    println("[OtpAuthManager] ⚠️  环境检测失败: ${e.message}")
                }
                
                println("[OtpAuthManager] 🚀 开始调用 signInWith(OTP)...")
                
                try {
                    println("[OtpAuthManager] 📧 配置 OTP 参数...")
                    val result = client.auth.signInWith(OTP) {
                        println("[OtpAuthManager]    📍 邮箱地址: $email")
                        this.email = email
                        println("[OtpAuthManager]    📍 创建用户: $shouldCreateUser")
                        this.createUser = shouldCreateUser
                        println("[OtpAuthManager] ✅ OTP 参数配置完成")
                    }
                    
                    println("[OtpAuthManager] 🎉 signInWith(OTP) 执行成功!")
                    println("[OtpAuthManager] 📊 返回结果类型: ${result?.javaClass?.simpleName ?: "null"}")
                    if (result != null) {
                        println("[OtpAuthManager] 📊 返回结果详情: $result")
                    } else {
                        println("[OtpAuthManager] 📊 返回结果: null (这通常是正常的)")
                    }
                    
                    // 判断是否成功 - 基于 Supabase 3.x 的行为
                    // 如果没有异常抛出，通常表示 OTP 发送成功
                    println("[OtpAuthManager] ✅ OTP 发送成功！验证码应该已发送到用户邮箱")
                    println("[OtpAuthManager] ========== 邮箱验证码发送完成 ==========")
                    
                    return@withContext true
                    
                } catch (otpException: Exception) {
                    println("[OtpAuthManager] ❌ signInWith(OTP) 调用失败!")
                    println("[OtpAuthManager] 🔍 异常类型: ${otpException.javaClass.simpleName}")
                    println("[OtpAuthManager] 💬 异常消息: ${otpException.message}")
                    println("[OtpAuthManager] 🌍 本地化消息: ${otpException.localizedMessage}")
                    println("[OtpAuthManager] 📚 异常堆栈跟踪:")
                    otpException.printStackTrace()
                    
                    // 根据异常类型提供更具体的错误信息
                    val errorMessage = when {
                        otpException.message?.contains("rate limit", ignoreCase = true) == true -> 
                            "发送太频繁，请稍后再试"
                        otpException.message?.contains("invalid", ignoreCase = true) == true -> 
                            "邮箱地址格式不正确"
                        otpException.message?.contains("network", ignoreCase = true) == true -> 
                            "网络连接失败，请检查网络"
                        else -> "OTP 发送失败: ${otpException.message}"
                    }
                    
                    throw AuthException(errorMessage, otpException)
                }
                
            } catch (e: AuthException) {
                // AuthException 直接重新抛出，但先记录日志
                println("[OtpAuthManager] ⚠️  收到 AuthException，准备重新抛出")
                println("[OtpAuthManager] ⚠️  AuthException 消息: ${e.message}")
                println("[OtpAuthManager] ⚠️  原始异常: ${e.cause?.javaClass?.simpleName}")
                println("[OtpAuthManager] ========== AuthException 重新抛出 ==========")
                throw e
            } catch (e: Exception) {
                println("[OtpAuthManager] ❌ 发送验证码时发生未预期的异常")
                println("[OtpAuthManager] 🔍 异常类型: ${e.javaClass.simpleName}")
                println("[OtpAuthManager] 💬 异常消息: ${e.message}")
                println("[OtpAuthManager] 🌍 本地化消息: ${e.localizedMessage}")
                println("[OtpAuthManager] 📚 异常堆栈跟踪:")
                e.printStackTrace()
                println("[OtpAuthManager] ========================================")
                
                // 根据异常类型给出更好的错误提示
                val userFriendlyMessage = when (e.javaClass.simpleName) {
                    "UnknownHostException", "ConnectException" -> "网络连接失败，请检查网络设置"
                    "SocketTimeoutException" -> "网络请求超时，请稍后重试"
                    "SSLException" -> "安全连接失败，请检查网络环境"
                    "IllegalStateException" -> "应用状态异常，请重启应用后重试"
                    "IllegalArgumentException" -> "参数错误，请检查邮箱格式"
                    else -> "发送验证码失败: ${e.message ?: "未知错误"}"
                }
                
                throw AuthException(userFriendlyMessage, e)
            }
        }

    
    /**
     * 验证邮箱验证码并登录
     * 
     * @param email 邮箱地址
     * @param token 6位验证码
     * @return 用户会话信息
     * @throws AuthException 认证异常
     */
    suspend fun verifyEmailOtp(
        email: String,
        token: String
    ): UserSession = withContext(Dispatchers.IO) {
        try {
            val client = getSupabaseClient()
            
            // 使用 Supabase 3.x API 验证邮箱 OTP
            client.auth.verifyEmailOtp(
                type = OtpType.Email.EMAIL,
                email = email,
                token = token
            )
            
            // 获取当前会话
            val session = client.auth.currentSessionOrNull()
            session ?: throw AuthException("验证失败，未获取到有效会话")
        } catch (e: Exception) {
            throw AuthException("验证码验证失败: ${e.message}", e)
        }
    }
    
    /**
     * 获取当前用户会话
     * 
     * @return 当前用户会话，如果未登录返回 null
     */
    suspend fun getCurrentSession(): UserSession? = withContext(Dispatchers.IO) {
        try {
            val client = getSupabaseClient()
            client.auth.currentSessionOrNull()
        } catch (e: Exception) {
            null
        }
    }
    
    /**
     * 检查用户是否已登录
     * 
     * @return 是否已登录
     */
    suspend fun isLoggedIn(): Boolean = withContext(Dispatchers.IO) {
        getCurrentSession() != null
    }
    
    /**
     * 登出当前用户
     */
    suspend fun signOut() = withContext(Dispatchers.IO) {
        try {
            val client = getSupabaseClient()
            client.auth.signOut()
        } catch (e: Exception) {
            throw AuthException("登出失败: ${e.message}", e)
        }
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 是否为有效邮箱格式
     */
    fun isValidEmail(email: String): Boolean {
        return email.matches(SupabaseConstants.EMAIL_REGEX.toRegex())
    }
    
}

/**
 * 认证异常类
 * 
 * @param message 错误消息
 * @param cause 原始异常
 */
class AuthException(
    message: String,
    cause: Throwable? = null
) : Exception(message, cause)