package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import com.xiaojinzi.tally.module.core.supabase.SupabaseConfig
import io.github.jan.supabase.postgrest.rpc
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

/**
 * Supabase 用户数据初始化器
 * 
 * 负责在用户注册时自动创建默认的账本、类别和账户
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
object UserDataInitializer {
    
    private const val TAG = "UserDataInitializer"
    private val database = SupabaseConfig.database
    
    /**
     * 初始化结果数据类
     */
    @Serializable
    data class InitializationResult(
        val success: Boolean,
        val message: String,
        val data: InitializationData? = null,
        val errorCode: String? = null
    )
    
    @Serializable
    data class InitializationData(
        val userId: String,
        val bookId: String,
        val accountId: String,
        val categoryCount: Int,
        val createdAt: Long
    )
    
    /**
     * 检查结果数据类
     */
    @Serializable
    data class CheckResult(
        val userId: String,
        val isInitialized: Boolean,
        val bookCount: Int,
        val categoryCount: Int,
        val accountCount: Int,
        val error: Boolean = false,
        val message: String? = null
    )
    
    /**
     * 初始化用户数据
     * 
     * @param userId 用户ID
     * @param userName 用户名称（可选）
     * @return 初始化结果
     */
    suspend fun initializeUserData(
        userId: String,
        userName: String = "默认用户"
    ): Result<InitializationResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始初始化用户数据: userId=$userId, userName=$userName")
            
            // 调用 Supabase RPC 函数
            Log.d(TAG, "🔧 调用 Supabase RPC 函数: initialize_user_data")
            Log.d(TAG, "   参数: userId=$userId, userName=$userName")

            val response = database.rpc(
                function = "initialize_user_data",
                parameters = mapOf(
                    "p_user_id" to userId,
                    "p_user_name" to userName
                )
            )

            Log.d(TAG, "📥 RPC 响应: ${response.data}")

            // 解析响应
            val jsonElement = Json.parseToJsonElement(response.data)
            val result = parseInitializationResponse(jsonElement)

            Log.d(TAG, "📊 解析结果: success=${result.success}, message=${result.message}")
            
            if (result.success) {
                Log.d(TAG, "✅ 用户数据初始化成功: ${result.message}")
                Log.d(TAG, "   - 账本ID: ${result.data?.bookId}")
                Log.d(TAG, "   - 账户ID: ${result.data?.accountId}")
                Log.d(TAG, "   - 类别数量: ${result.data?.categoryCount}")
            } else {
                Log.e(TAG, "❌ 用户数据初始化失败: ${result.message}")
            }
            
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 初始化用户数据异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 检查用户是否已初始化
     * 
     * @param userId 用户ID
     * @return 检查结果
     */
    suspend fun checkUserInitialized(userId: String): Result<CheckResult> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "检查用户初始化状态: userId=$userId")
            
            // 调用 Supabase RPC 函数
            val response = database.rpc(
                function = "check_user_initialized",
                parameters = mapOf("p_user_id" to userId)
            )
            
            // 解析响应
            val jsonElement = Json.parseToJsonElement(response.data)
            val result = parseCheckResponse(jsonElement)
            
            Log.d(TAG, "✅ 用户初始化检查完成:")
            Log.d(TAG, "   - 是否已初始化: ${result.isInitialized}")
            Log.d(TAG, "   - 账本数量: ${result.bookCount}")
            Log.d(TAG, "   - 类别数量: ${result.categoryCount}")
            Log.d(TAG, "   - 账户数量: ${result.accountCount}")
            
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 检查用户初始化状态异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 确保用户数据已初始化
     * 如果未初始化则自动初始化
     * 
     * @param userId 用户ID
     * @param userName 用户名称
     * @return 是否成功确保初始化
     */
    suspend fun ensureUserDataInitialized(
        userId: String,
        userName: String = "默认用户"
    ): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "确保用户数据已初始化: userId=$userId")
            
            // 1. 检查是否已初始化
            val checkResult = checkUserInitialized(userId).getOrThrow()
            
            if (checkResult.isInitialized) {
                Log.d(TAG, "✅ 用户数据已存在，无需初始化")
                return@withContext Result.success(true)
            }
            
            // 2. 如果未初始化，则进行初始化
            Log.d(TAG, "🔄 用户数据未初始化，开始自动初始化...")
            val initResult = initializeUserData(userId, userName).getOrThrow()
            
            if (initResult.success) {
                Log.d(TAG, "✅ 用户数据自动初始化成功")
                Result.success(true)
            } else {
                Log.e(TAG, "❌ 用户数据自动初始化失败: ${initResult.message}")
                Result.success(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ 确保用户数据初始化异常: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 解析初始化响应
     */
    private fun parseInitializationResponse(response: JsonElement): InitializationResult {
        return try {
            val json = response.jsonObject
            val success = json["success"]?.jsonPrimitive?.content?.toBoolean() ?: false
            val message = json["message"]?.jsonPrimitive?.content ?: "未知结果"
            
            if (success) {
                val dataJson = json["data"]?.jsonObject
                val data = dataJson?.let {
                    InitializationData(
                        userId = it["user_id"]?.jsonPrimitive?.content ?: "",
                        bookId = it["book_id"]?.jsonPrimitive?.content ?: "",
                        accountId = it["account_id"]?.jsonPrimitive?.content ?: "",
                        categoryCount = it["category_count"]?.jsonPrimitive?.content?.toIntOrNull() ?: 0,
                        createdAt = it["created_at"]?.jsonPrimitive?.content?.toLongOrNull() ?: 0L
                    )
                }
                InitializationResult(success = true, message = message, data = data)
            } else {
                val errorCode = json["error_code"]?.jsonPrimitive?.content
                InitializationResult(success = false, message = message, errorCode = errorCode)
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析初始化响应失败: ${e.message}", e)
            InitializationResult(success = false, message = "响应解析失败: ${e.message}")
        }
    }
    
    /**
     * 解析检查响应
     */
    private fun parseCheckResponse(response: JsonElement): CheckResult {
        return try {
            val json = response.jsonObject
            CheckResult(
                userId = json["user_id"]?.jsonPrimitive?.content ?: "",
                isInitialized = json["is_initialized"]?.jsonPrimitive?.content?.toBoolean() ?: false,
                bookCount = json["book_count"]?.jsonPrimitive?.content?.toIntOrNull() ?: 0,
                categoryCount = json["category_count"]?.jsonPrimitive?.content?.toIntOrNull() ?: 0,
                accountCount = json["account_count"]?.jsonPrimitive?.content?.toIntOrNull() ?: 0
            )
        } catch (e: Exception) {
            Log.e(TAG, "解析检查响应失败: ${e.message}", e)
            CheckResult(
                userId = "",
                isInitialized = false,
                bookCount = 0,
                categoryCount = 0,
                accountCount = 0,
                error = true,
                message = "响应解析失败: ${e.message}"
            )
        }
    }
}
