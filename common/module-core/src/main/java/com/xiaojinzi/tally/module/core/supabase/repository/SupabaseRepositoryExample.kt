package com.xiaojinzi.tally.module.core.supabase.repository

import android.util.Log
import com.xiaojinzi.tally.module.core.supabase.SupabaseConfig
import com.xiaojinzi.tally.module.core.supabase.model.*
import io.github.jan.supabase.postgrest.from
import io.github.jan.supabase.postgrest.query.Order
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Supabase Repository 示例
 * 
 * 演示如何使用 Supabase 进行基础的 CRUD 操作
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-24
 */
object SupabaseRepositoryExample {
    
    private const val TAG = "SupabaseRepositoryExample"
    private val database = SupabaseConfig.database
    
    // =====================================================
    // 用户操作示例
    // =====================================================
    
    /**
     * 插入用户
     */
    suspend fun insertUser(user: SupabaseUser): Result<SupabaseUser> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "插入用户: ${user.name}")
            
            val result = database
                .from("users")
                .insert(user) {
                    select()
                }
                .decodeSingle<SupabaseUser>()
            
            Log.d(TAG, "✅ 用户插入成功")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 用户插入失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 根据 ID 查询用户
     */
    suspend fun getUserById(userId: String): Result<SupabaseUser?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "查询用户: $userId")
            
            val result = database
                .from("users")
                .select {
                    filter {
                        eq("id", userId)
                        eq("is_deleted", false)
                    }
                }
                .decodeSingleOrNull<SupabaseUser>()
            
            Log.d(TAG, "✅ 用户查询完成")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 用户查询失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // =====================================================
    // 账单操作示例
    // =====================================================
    
    /**
     * 插入账单
     */
    suspend fun insertBill(bill: SupabaseBill): Result<SupabaseBill> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "插入账单: ${bill.note}")
            
            val result = database
                .from("bills")
                .insert(bill) {
                    select()
                }
                .decodeSingle<SupabaseBill>()
            
            Log.d(TAG, "✅ 账单插入成功")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 账单插入失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 查询用户的账单列表
     */
    suspend fun getBillsByUserId(
        userId: String,
        limit: Int = 50
    ): Result<List<SupabaseBill>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "查询用户账单: $userId")
            
            val result = database
                .from("bills")
                .select {
                    filter {
                        eq("user_id", userId)
                        eq("is_deleted", false)
                    }
                    order("time", Order.DESCENDING)
                    limit(limit.toLong())
                }
                .decodeList<SupabaseBill>()
            
            Log.d(TAG, "✅ 账单查询完成，返回 ${result.size} 条")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 账单查询失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 更新账单
     */
    suspend fun updateBill(billId: String, updates: Map<String, Any>): Result<SupabaseBill?> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "更新账单: $billId")
            
            val updatesWithTime = updates.toMutableMap().apply {
                put("time_modify", System.currentTimeMillis())
            }
            
            val result = database
                .from("bills")
                .update(updatesWithTime) {
                    filter {
                        eq("id", billId)
                    }
                    select()
                }
                .decodeSingleOrNull<SupabaseBill>()
            
            Log.d(TAG, "✅ 账单更新完成")
            Result.success(result)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 账单更新失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * 软删除账单
     */
    suspend fun deleteBill(billId: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "删除账单: $billId")
            
            val updates = mapOf(
                "is_deleted" to true,
                "time_modify" to System.currentTimeMillis()
            )
            
            database
                .from("bills")
                .update(updates) {
                    filter {
                        eq("id", billId)
                    }
                }
            
            Log.d(TAG, "✅ 账单删除完成")
            Result.success(true)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 账单删除失败: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    // =====================================================
    // 使用示例
    // =====================================================
    
    /**
     * 完整的使用示例
     */
    suspend fun runExample() {
        try {
            Log.d(TAG, "========== Supabase Repository 示例开始 ==========")
            
            // 1. 创建用户
            val user = SupabaseUser(
                id = "example-user-123",
                name = "测试用户",
                timeExpire = System.currentTimeMillis() + 86400000,
                timeCreate = System.currentTimeMillis()
            )
            
            insertUser(user).fold(
                onSuccess = { Log.d(TAG, "用户创建成功: ${it.name}") },
                onFailure = { Log.e(TAG, "用户创建失败: ${it.message}") }
            )
            
            // 2. 创建账单
            val bill = SupabaseBill(
                id = "example-bill-456",
                userId = "example-user-123",
                bookId = "example-book-789",
                type = "normal",
                time = System.currentTimeMillis(),
                amount = 5000, // 50.00元
                note = "测试账单",
                timeCreate = System.currentTimeMillis()
            )
            
            insertBill(bill).fold(
                onSuccess = { Log.d(TAG, "账单创建成功: ${it.note}") },
                onFailure = { Log.e(TAG, "账单创建失败: ${it.message}") }
            )
            
            // 3. 查询账单
            getBillsByUserId("example-user-123").fold(
                onSuccess = { bills ->
                    Log.d(TAG, "查询到 ${bills.size} 条账单")
                    bills.forEach { Log.d(TAG, "  - ${it.note}: ${it.amount / 100.0}元") }
                },
                onFailure = { Log.e(TAG, "账单查询失败: ${it.message}") }
            )
            
            Log.d(TAG, "========== Supabase Repository 示例完成 ==========")
        } catch (e: Exception) {
            Log.e(TAG, "❌ 示例执行失败: ${e.message}", e)
        }
    }
}
