package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import kotlinx.coroutines.*

/**
 * Supabase MCP 使用示例
 * 
 * 展示如何在 Android 应用中使用 Supabase MCP 服务进行各种数据库操作
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-25
 */
object SupabaseMcpUsageExample {
    
    private const val TAG = "SupabaseMcpExample"
    
    /**
     * 完整的 MCP 使用示例
     */
    suspend fun runCompleteExample() {
        try {
            Log.d(TAG, "========== Supabase MCP 使用示例开始 ==========")
            
            val mcpService = SupabaseMcpService.getInstance()
            
            // 1. 初始化 MCP 服务
            Log.d(TAG, "🚀 步骤 1: 初始化 MCP 服务")
            mcpService.initialize().fold(
                onSuccess = { Log.d(TAG, "✅ MCP 服务初始化成功") },
                onFailure = { 
                    Log.e(TAG, "❌ MCP 服务初始化失败: ${it.message}")
                    return
                }
            )
            
            // 2. 查看当前项目信息
            Log.d(TAG, "🏗️ 步骤 2: 查看当前项目信息")
            val project = mcpService.currentProject
            if (project != null) {
                Log.d(TAG, "📋 项目名称: ${project.name}")
                Log.d(TAG, "🔗 项目 URL: ${project.url}")
                Log.d(TAG, "📊 项目状态: ${project.status}")
            }
            
            // 3. 列出数据库表
            Log.d(TAG, "📊 步骤 3: 列出数据库表")
            mcpService.listTables("public").fold(
                onSuccess = { tables ->
                    Log.d(TAG, "✅ 成功获取 ${tables.size} 个表:")
                    tables.forEach { table ->
                        Log.d(TAG, "  📋 表名: ${table.name} (${table.columns.size} 列, ${table.rowCount} 行)")
                        table.columns.take(3).forEach { column ->
                            Log.d(TAG, "    📝 列: ${column.name} (${column.type})")
                        }
                    }
                },
                onFailure = { Log.e(TAG, "❌ 列出数据库表失败: ${it.message}") }
            )
            
            // 4. 执行查询 SQL
            Log.d(TAG, "🔍 步骤 4: 执行查询 SQL")
            val queryResult = mcpService.executeSql("SELECT COUNT(*) as total_books FROM user_books")
            queryResult.fold(
                onSuccess = { result ->
                    if (result.success && result.rows != null) {
                        Log.d(TAG, "✅ 查询成功，耗时: ${result.executionTime}ms")
                        result.rows.forEach { row ->
                            Log.d(TAG, "📊 查询结果: $row")
                        }
                    } else {
                        Log.e(TAG, "❌ 查询失败: ${result.error}")
                    }
                },
                onFailure = { Log.e(TAG, "❌ SQL 执行失败: ${it.message}") }
            )
            
            // 5. 获取项目日志
            Log.d(TAG, "📜 步骤 5: 获取项目日志")
            mcpService.getProjectLogs("api", 5).fold(
                onSuccess = { logs ->
                    Log.d(TAG, "✅ 成功获取 ${logs.size} 条日志:")
                    logs.forEach { log ->
                        Log.d(TAG, "  📝 $log")
                    }
                },
                onFailure = { Log.e(TAG, "❌ 获取日志失败: ${it.message}") }
            )
            
            // 6. 监听连接状态
            Log.d(TAG, "📡 步骤 6: 监听连接状态")
            mcpService.connectionState.collect { state ->
                when (state) {
                    is SupabaseMcpService.McpConnectionState.Connected -> {
                        Log.d(TAG, "🟢 MCP 连接状态: 已连接")
                    }
                    is SupabaseMcpService.McpConnectionState.Connecting -> {
                        Log.d(TAG, "🟡 MCP 连接状态: 连接中...")
                    }
                    is SupabaseMcpService.McpConnectionState.Disconnected -> {
                        Log.d(TAG, "🔴 MCP 连接状态: 已断开")
                    }
                    is SupabaseMcpService.McpConnectionState.Error -> {
                        Log.e(TAG, "❌ MCP 连接错误: ${state.exception.message}")
                    }
                }
                // 只收集一次状态就退出
                return@collect
            }
            
            Log.d(TAG, "========== Supabase MCP 使用示例完成 ==========")
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ 示例执行失败: ${e.message}", e)
        }
    }
    
    /**
     * 数据库管理示例
     */
    suspend fun databaseManagementExample() {
        Log.d(TAG, "========== 数据库管理示例 ==========")
        
        val mcpService = SupabaseMcpService.getInstance()
        
        // 确保服务已初始化
        mcpService.initialize()
        
        // 1. 查看表结构
        Log.d(TAG, "🔍 查看 book_categories 表结构")
        mcpService.executeSql("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'book_categories'
            ORDER BY ordinal_position
        """.trimIndent()).fold(
            onSuccess = { result ->
                Log.d(TAG, "✅ 表结构查询成功:")
                result.rows?.forEach { row ->
                    Log.d(TAG, "  📝 ${row["column_name"]}: ${row["data_type"]} (nullable: ${row["is_nullable"]})")
                }
            },
            onFailure = { Log.e(TAG, "❌ 表结构查询失败: ${it.message}") }
        )
        
        // 2. 统计数据
        Log.d(TAG, "📊 统计各类型类别数量")
        mcpService.executeSql("""
            SELECT type, COUNT(*) as count
            FROM book_categories 
            WHERE is_deleted = false
            GROUP BY type
        """.trimIndent()).fold(
            onSuccess = { result ->
                Log.d(TAG, "✅ 统计查询成功:")
                result.rows?.forEach { row ->
                    Log.d(TAG, "  📊 ${row["type"]}: ${row["count"]} 个类别")
                }
            },
            onFailure = { Log.e(TAG, "❌ 统计查询失败: ${it.message}") }
        )
        
        // 3. 查看最近创建的类别
        Log.d(TAG, "🕒 查看最近创建的类别")
        mcpService.executeSql("""
            SELECT name, type, icon_name, created_at
            FROM book_categories 
            WHERE is_deleted = false
            ORDER BY created_at DESC
            LIMIT 5
        """.trimIndent()).fold(
            onSuccess = { result ->
                Log.d(TAG, "✅ 最近类别查询成功:")
                result.rows?.forEach { row ->
                    Log.d(TAG, "  📝 ${row["name"]} (${row["type"]}) - ${row["icon_name"]}")
                }
            },
            onFailure = { Log.e(TAG, "❌ 最近类别查询失败: ${it.message}") }
        )
    }
    
    /**
     * 项目监控示例
     */
    suspend fun projectMonitoringExample() {
        Log.d(TAG, "========== 项目监控示例 ==========")
        
        val mcpService = SupabaseMcpService.getInstance()
        
        // 确保服务已初始化
        mcpService.initialize()
        
        // 1. 获取 API 日志
        Log.d(TAG, "📡 获取 API 服务日志")
        mcpService.getProjectLogs("api", 10).fold(
            onSuccess = { logs ->
                Log.d(TAG, "✅ API 日志获取成功:")
                logs.forEach { log ->
                    Log.d(TAG, "  📝 $log")
                }
            },
            onFailure = { Log.e(TAG, "❌ API 日志获取失败: ${it.message}") }
        )
        
        // 2. 获取数据库日志
        Log.d(TAG, "🗄️ 获取数据库服务日志")
        mcpService.getProjectLogs("postgres", 5).fold(
            onSuccess = { logs ->
                Log.d(TAG, "✅ 数据库日志获取成功:")
                logs.forEach { log ->
                    Log.d(TAG, "  📝 $log")
                }
            },
            onFailure = { Log.e(TAG, "❌ 数据库日志获取失败: ${it.message}") }
        )
        
        // 3. 获取认证日志
        Log.d(TAG, "🔐 获取认证服务日志")
        mcpService.getProjectLogs("auth", 5).fold(
            onSuccess = { logs ->
                Log.d(TAG, "✅ 认证日志获取成功:")
                logs.forEach { log ->
                    Log.d(TAG, "  📝 $log")
                }
            },
            onFailure = { Log.e(TAG, "❌ 认证日志获取失败: ${it.message}") }
        )
    }
    
    /**
     * 在协程中运行示例
     */
    fun runExampleInCoroutine() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 运行完整示例
                runCompleteExample()
                
                delay(1000)
                
                // 运行数据库管理示例
                databaseManagementExample()
                
                delay(1000)
                
                // 运行项目监控示例
                projectMonitoringExample()
                
            } catch (e: Exception) {
                Log.e(TAG, "❌ 协程示例执行失败: ${e.message}", e)
            }
        }
    }
}
