package com.xiaojinzi.tally.module.core.supabase

import android.os.Bundle
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Supabase 调试页面
 * 用于测试和调试 Supabase 功能
 */
class SupabaseDebugActivity : AppCompatActivity() {
    
    private lateinit var logTextView: TextView
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 创建布局
        val scrollView = ScrollView(this)
        val linearLayout = LinearLayout(this).apply {
            orientation = LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }
        
        // 标题
        val titleText = TextView(this).apply {
            text = "Supabase 调试工具"
            textSize = 20f
            setPadding(0, 0, 0, 32)
        }
        linearLayout.addView(titleText)
        
        // 按钮1：检查数据库表
        val checkTablesButton = Button(this).apply {
            text = "检查数据库表"
            setOnClickListener {
                SupabaseDebugHelper.checkDatabaseTables(this@SupabaseDebugActivity)
            }
        }
        linearLayout.addView(checkTablesButton)
        
        // 按钮2：显示用户数据状态
        val showUserDataButton = Button(this).apply {
            text = "显示用户数据状态"
            setOnClickListener {
                SupabaseDebugHelper.showCurrentUserDataStatus(this@SupabaseDebugActivity)
            }
        }
        linearLayout.addView(showUserDataButton)
        
        // 按钮3：检查连接状态
        val checkConnectionButton = Button(this).apply {
            text = "检查连接状态"
            setOnClickListener {
                SupabaseDebugHelper.checkSupabaseConnection(this@SupabaseDebugActivity)
            }
        }
        linearLayout.addView(checkConnectionButton)
        
        // 按钮4：强制重新初始化
        val reinitializeButton = Button(this).apply {
            text = "强制重新初始化"
            setOnClickListener {
                SupabaseDebugHelper.forceReinitializeCurrentUser(this@SupabaseDebugActivity)
            }
        }
        linearLayout.addView(reinitializeButton)
        
        // 按钮5：打印完整调试信息
        val printDebugButton = Button(this).apply {
            text = "打印完整调试信息"
            setOnClickListener {
                SupabaseDebugHelper.printFullDebugInfo()
                appendLog("已打印完整调试信息到日志，请查看 Logcat")
            }
        }
        linearLayout.addView(printDebugButton)
        
        // 按钮6：检查当前用户数据
        val checkCurrentUserButton = Button(this).apply {
            text = "检查当前用户数据"
            setOnClickListener {
                CoroutineScope(Dispatchers.Main).launch {
                    try {
                        SupabaseTableChecker.printCurrentUserData()
                        appendLog("已打印当前用户数据到日志，请查看 Logcat")
                    } catch (e: Exception) {
                        appendLog("检查用户数据失败: ${e.message}")
                    }
                }
            }
        }
        linearLayout.addView(checkCurrentUserButton)
        
        // 日志显示区域
        val logTitle = TextView(this).apply {
            text = "日志输出："
            textSize = 16f
            setPadding(0, 32, 0, 16)
        }
        linearLayout.addView(logTitle)
        
        logTextView = TextView(this).apply {
            text = "点击按钮开始调试...\n"
            textSize = 12f
            setBackgroundColor(0xFFF5F5F5.toInt())
            setPadding(16, 16, 16, 16)
        }
        linearLayout.addView(logTextView)
        
        scrollView.addView(linearLayout)
        setContentView(scrollView)
        
        // 初始化时显示一些基本信息
        appendLog("Supabase 调试工具已启动")
        appendLog("URL: ${SupabaseConstants.SUPABASE_URL}")
        appendLog("Key length: ${SupabaseConstants.SUPABASE_ANON_KEY.length}")
    }
    
    private fun appendLog(message: String) {
        runOnUiThread {
            val currentText = logTextView.text.toString()
            val timestamp = System.currentTimeMillis()
            val newText = "$currentText\n[$timestamp] $message"
            logTextView.text = newText
        }
    }
}
