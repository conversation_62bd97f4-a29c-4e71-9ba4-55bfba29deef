package com.xiaojinzi.tally.module.core.supabase

import android.util.Log
import io.github.jan.supabase.SupabaseClient
import io.github.jan.supabase.auth.Auth
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.auth.providers.builtin.OTP
import io.github.jan.supabase.auth.providers.builtin.Email
import io.github.jan.supabase.auth.user.UserSession
import io.github.jan.supabase.auth.OtpType
import io.github.jan.supabase.auth.status.SessionStatus
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * Supabase 连接器
 * 
 * 提供统一的认证管理、会话持久化、连接监听和错误恢复功能
 * 
 * 主要特性：
 * - 统一的认证接口
 * - 自动会话管理和持久化
 * - 实时连接状态监听
 * - 智能错误处理和重试机制
 * - 配置验证和诊断
 * - 线程安全的操作
 * 
 * <AUTHOR> 4.0 sonnet 🐾
 * @since 2025-01-21
 */
class SupabaseConnector {
    
    companion object {
        private const val TAG = "SupabaseConnector"
        private const val SESSION_REFRESH_INTERVAL_MS = 30_000L // 30秒
        
        // 单例实例
        @Volatile
        private var INSTANCE: SupabaseConnector? = null
        
        /**
         * 获取单例实例
         */
        fun getInstance(): SupabaseConnector {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SupabaseConnector().also { INSTANCE = it }
            }
        }
    }
    
    // 客户端状态
    private val _client = AtomicReference<SupabaseClient?>(null)
    private val _isInitialized = AtomicBoolean(false)
    private val _isConnected = AtomicBoolean(false)
    
    // 会话状态
    private val _currentSession = AtomicReference<UserSession?>(null)
    private val _sessionStateFlow = MutableStateFlow<SessionState>(SessionState.NotAuthenticated)
    
    // 连接状态
    private val _connectionStateFlow = MutableStateFlow<ConnectionState>(ConnectionState.Disconnected)
    
    // 协程作用域
    private val connectorScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // 会话监听任务
    private var sessionMonitorJob: Job? = null
    
    /**
     * 会话状态
     */
    sealed class SessionState {
        object NotAuthenticated : SessionState()
        object Authenticating : SessionState()
        data class Authenticated(val session: UserSession) : SessionState()
        data class AuthenticationFailed(val error: Throwable) : SessionState()
    }
    
    /**
     * 连接状态
     */
    sealed class ConnectionState {
        object Disconnected : ConnectionState()
        object Connecting : ConnectionState()
        object Connected : ConnectionState()
        data class Error(val error: Throwable) : ConnectionState()
    }
    
    /**
     * 会话状态流
     */
    val sessionState: StateFlow<SessionState> = _sessionStateFlow.asStateFlow()
    
    /**
     * 连接状态流
     */
    val connectionState: StateFlow<ConnectionState> = _connectionStateFlow.asStateFlow()
    
    /**
     * 当前是否已认证
     */
    val isAuthenticated: Boolean
        get() = _sessionStateFlow.value is SessionState.Authenticated
    
    /**
     * 当前是否已初始化
     */
    val isInitialized: Boolean
        get() = _isInitialized.get()

    /**
     * 当前是否已连接
     */
    val isConnected: Boolean
        get() = _isConnected.get()
    
    /**
     * 当前用户会话
     */
    val currentSession: UserSession?
        get() = _currentSession.get()
    
    /**
     * Supabase客户端
     */
    val client: SupabaseClient
        get() = _client.get() ?: throw SupabaseConnectorException("Supabase 客户端未初始化")
    
    /**
     * 数据服务实例
     */
    val dataService: SupabaseDataService
        get() = SupabaseDataService
    
    /**
     * 初始化连接器
     */
    suspend fun initialize(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            if (_isInitialized.get()) {
                Log.d(TAG, "连接器已初始化，跳过重复初始化")
                return@withContext Result.success(Unit)
            }
            
            Log.d(TAG, "========== 开始初始化 Supabase 连接器 ==========")
            _connectionStateFlow.value = ConnectionState.Connecting
            
            // 验证配置
            validateConfiguration()
            
            // 获取客户端
            val client = getOrCreateClient()
            _client.set(client)
            
            // 启动会话监听
            startSessionMonitoring()
            
            // 尝试恢复现有会话
            restoreExistingSession()
            
            _isInitialized.set(true)
            _isConnected.set(true)
            _connectionStateFlow.value = ConnectionState.Connected
            
            Log.d(TAG, "✅ Supabase 连接器初始化成功")
            Log.d(TAG, "========== 连接器初始化完成 ==========")
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 连接器初始化失败: ${e.message}", e)
            _connectionStateFlow.value = ConnectionState.Error(e)
            _isInitialized.set(false)
            _isConnected.set(false)
            Result.failure(SupabaseConnectorException("连接器初始化失败", e))
        }
    }
    
    /**
     * 发送邮箱验证码
     */
    suspend fun sendEmailOtp(
        email: String,
        shouldCreateUser: Boolean = true
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始发送邮箱验证码 ===========")
            Log.d(TAG, "邮箱: $email, 创建用户: $shouldCreateUser")
            
            // 确保连接器已初始化
            ensureInitialized()
            
            val client = requireClient()
            
            // 更新认证状态
            _sessionStateFlow.value = SessionState.Authenticating
            
            // 发送邮箱验证码
            client.auth.signInWith(OTP) {
                this.email = email
                this.createUser = shouldCreateUser
            }
            
            Log.d(TAG, "✅ 邮箱验证码发送成功")
            Log.d(TAG, "========== 验证码发送完成 ===========")
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 发送邮箱验证码失败: ${e.message}", e)
            _sessionStateFlow.value = SessionState.AuthenticationFailed(e)
            Result.failure(SupabaseConnectorException("验证码发送失败", e))
        }
    }
    
    /**
     * 验证邮箱验证码并登录
     */
    suspend fun verifyEmailOtp(
        email: String,
        token: String
    ): Result<UserSession> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始验证邮箱验证码 ===========")
            Log.d(TAG, "邮箱: $email")
            
            // 确保连接器已初始化
            ensureInitialized()
            
            val client = requireClient()
            
            // 更新认证状态
            _sessionStateFlow.value = SessionState.Authenticating
            
            // 执行验证
            client.auth.verifyEmailOtp(
                type = OtpType.Email.EMAIL,
                email = email,
                token = token
            )
            
            // 获取会话
            val session = client.auth.currentSessionOrNull()
                ?: throw SupabaseConnectorException("验证成功但未获取到会话")
            
            // 更新会话状态
            updateSession(session)
            
            Log.d(TAG, "✅ 邮箱验证码验证成功")
            Log.d(TAG, "用户ID: ${session.user?.id}")
            Log.d(TAG, "用户邮箱: ${session.user?.email}")
            Log.d(TAG, "========== 验证码验证完成 ===========")
            
            Result.success(session)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 验证邮箱验证码失败: ${e.message}", e)
            _sessionStateFlow.value = SessionState.AuthenticationFailed(e)
            Result.failure(SupabaseConnectorException("验证码验证失败", e))
        }
    }
    
    /**
     * 邮箱密码注册
     */
    suspend fun signUpWithEmail(
        email: String,
        password: String
    ): Result<UserSession> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始邮箱密码注册 ===========")
            Log.d(TAG, "邮箱: $email")
            
            // 确保连接器已初始化
            ensureInitialized()
            
            val client = requireClient()
            
            // 更新认证状态
            _sessionStateFlow.value = SessionState.Authenticating
            
            // 执行注册
            client.auth.signUpWith(Email) {
                this.email = email
                this.password = password
            }
            
            // 获取会话
            val session = client.auth.currentSessionOrNull()
                ?: throw SupabaseConnectorException("注册成功但未获取到会话")
            
            // 更新会话状态
            updateSession(session)
            
            Log.d(TAG, "✅ 邮箱密码注册成功")
            Log.d(TAG, "用户ID: ${session.user?.id}")
            Log.d(TAG, "用户邮箱: ${session.user?.email}")
            Log.d(TAG, "========== 邮箱密码注册完成 ===========")
            
            Result.success(session)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 邮箱密码注册失败: ${e.message}", e)
            _sessionStateFlow.value = SessionState.AuthenticationFailed(e)
            Result.failure(SupabaseConnectorException("邮箱密码注册失败", e))
        }
    }
    
    /**
     * 邮箱密码登录
     */
    suspend fun signInWithEmail(
        email: String,
        password: String
    ): Result<UserSession> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始邮箱密码登录 ===========")
            Log.d(TAG, "邮箱: $email")
            
            // 确保连接器已初始化
            ensureInitialized()
            
            val client = requireClient()
            
            // 更新认证状态
            _sessionStateFlow.value = SessionState.Authenticating
            
            // 执行登录
            client.auth.signInWith(Email) {
                this.email = email
                this.password = password
            }
            
            // 获取会话
            val session = client.auth.currentSessionOrNull()
                ?: throw SupabaseConnectorException("登录成功但未获取到会话")
            
            // 更新会话状态
            updateSession(session)
            
            Log.d(TAG, "✅ 邮箱密码登录成功")
            Log.d(TAG, "用户ID: ${session.user?.id}")
            Log.d(TAG, "用户邮箱: ${session.user?.email}")
            Log.d(TAG, "========== 邮箱密码登录完成 ===========")
            
            Result.success(session)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 邮箱密码登录失败: ${e.message}", e)
            _sessionStateFlow.value = SessionState.AuthenticationFailed(e)
            Result.failure(SupabaseConnectorException("邮箱密码登录失败", e))
        }
    }
    
    /**
     * 登出当前用户
     */
    suspend fun signOut(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "========== 开始用户登出 ===========")
            
            val client = _client.get()
            if (client != null) {
                client.auth.signOut()
            }
            
            // 清除会话状态
            clearSession()
            
            Log.d(TAG, "✅ 用户登出成功")
            Log.d(TAG, "========== 用户登出完成 ===========")
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 用户登出失败: ${e.message}", e)
            // 即使登出失败，也清除本地会话状态
            clearSession()
            Result.failure(SupabaseConnectorException("登出失败", e))
        }
    }
    
    /**
     * 刷新当前会话
     */
    suspend fun refreshSession(): Result<UserSession> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "开始刷新会话")
            
            val client = requireClient()
            
            // 刷新会话
            client.auth.refreshCurrentSession()
            
            val session = client.auth.currentSessionOrNull()
                ?: throw SupabaseConnectorException("刷新后未获取到会话")
            
            updateSession(session)
            
            Log.d(TAG, "✅ 会话刷新成功")
            Result.success(session)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 会话刷新失败: ${e.message}", e)
            Result.failure(SupabaseConnectorException("会话刷新失败", e))
        }
    }
    
    /**
     * 获取访问令牌
     */
    suspend fun getAccessToken(): Result<String> = withContext(Dispatchers.IO) {
        try {
            val session = currentSession
                ?: return@withContext Result.failure(SupabaseConnectorException("用户未登录"))
            
            val accessToken = session.accessToken
            if (accessToken.isBlank()) {
                return@withContext Result.failure(SupabaseConnectorException("访问令牌为空"))
            }
            
            Result.success(accessToken)
        } catch (e: Exception) {
            Log.e(TAG, "❌ 获取访问令牌失败: ${e.message}", e)
            Result.failure(SupabaseConnectorException("获取访问令牌失败", e))
        }
    }
    
    /**
     * 验证邮箱格式
     */
    fun isValidEmail(email: String): Boolean {
        return email.matches(SupabaseConstants.EMAIL_REGEX.toRegex())
    }
    
    /**
     * 销毁连接器
     */
    fun destroy() {
        Log.d(TAG, "开始销毁连接器")
        
        // 取消所有协程
        sessionMonitorJob?.cancel()
        connectorScope.cancel()
        
        // 清除状态
        clearSession()
        _client.set(null)
        _isInitialized.set(false)
        _isConnected.set(false)
        _connectionStateFlow.value = ConnectionState.Disconnected
        
        Log.d(TAG, "✅ 连接器销毁完成")
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 验证配置
     */
    private fun validateConfiguration() {
        Log.d(TAG, "开始验证配置")
        
        if (SupabaseConstants.SUPABASE_URL.isBlank() || SupabaseConstants.SUPABASE_ANON_KEY.isBlank()) {
            throw SupabaseConnectorException("Supabase 配置不能为空")
        }
        
        if (SupabaseConstants.SUPABASE_URL.contains("your-project-ref") ||
            SupabaseConstants.SUPABASE_ANON_KEY.contains("your-supabase-anon-key")) {
            throw SupabaseConnectorException("检测到配置占位符，请设置真实的 Supabase 配置")
        }
        
        if (!SupabaseConstants.SUPABASE_URL.startsWith("https://")) {
            throw SupabaseConnectorException("Supabase URL 必须使用 HTTPS")
        }
        
        Log.d(TAG, "✅ 配置验证通过")
    }
    
    /**
     * 获取或创建客户端
     */
    private suspend fun getOrCreateClient(): SupabaseClient {
        return _client.get() ?: run {
            Log.d(TAG, "创建新的 Supabase 客户端")
            SupabaseConfig.client
        }
    }
    
    /**
     * 确保连接器已初始化
     */
    private suspend fun ensureInitialized() {
        if (!_isInitialized.get()) {
            Log.d(TAG, "连接器未初始化，开始自动初始化")
            val result = initialize()
            if (result.isFailure) {
                throw SupabaseConnectorException("连接器初始化失败", result.exceptionOrNull())
            }
        }
    }
    
    /**
     * 获取必需的客户端
     */
    private fun requireClient(): SupabaseClient {
        return _client.get() ?: throw SupabaseConnectorException("Supabase 客户端未初始化")
    }
    
    /**
     * 启动会话监听
     */
    private fun startSessionMonitoring() {
        sessionMonitorJob?.cancel()
        sessionMonitorJob = connectorScope.launch {
            try {
                val client = requireClient()
                
                // 监听会话状态变化
                client.auth.sessionStatus.collect { status ->
                    Log.d(TAG, "会话状态变化: ${status.javaClass.simpleName}")
                    
                    when (status) {
                        is SessionStatus.Authenticated -> {
                            Log.d(TAG, "用户已认证")
                            updateSession(status.session)
                        }
                        is SessionStatus.NotAuthenticated -> {
                            Log.d(TAG, "用户未认证")
                            clearSession()
                        }
                        else -> {
                            Log.d(TAG, "会话状态变化: ${status.javaClass.simpleName}")
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "会话监听失败: ${e.message}", e)
            }
        }
    }
    
    /**
     * 恢复现有会话
     */
    private suspend fun restoreExistingSession() {
        try {
            Log.d(TAG, "尝试恢复现有会话")
            
            val client = requireClient()
            val session = client.auth.currentSessionOrNull()
            
            if (session != null) {
                Log.d(TAG, "✅ 发现现有会话，用户: ${session.user?.email}")
                updateSession(session)
            } else {
                Log.d(TAG, "未发现现有会话")
                clearSession()
            }
        } catch (e: Exception) {
            Log.w(TAG, "恢复会话失败: ${e.message}", e)
            clearSession()
        }
    }
    
    /**
     * 更新会话状态
     */
    private fun updateSession(session: UserSession) {
        _currentSession.set(session)
        _sessionStateFlow.value = SessionState.Authenticated(session)
        Log.d(TAG, "会话状态已更新")
    }
    
    /**
     * 清除会话状态
     */
    private fun clearSession() {
        _currentSession.set(null)
        _sessionStateFlow.value = SessionState.NotAuthenticated
        Log.d(TAG, "会话状态已清除")
    }
}

/**
 * Supabase 连接器异常
 */
class SupabaseConnectorException(
    message: String,
    cause: Throwable? = null
) : Exception(message, cause)