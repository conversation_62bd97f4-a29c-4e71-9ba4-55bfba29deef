
# 邮箱验证码登录功能设计文档
以下所有功能再开发前都要去调用一次Context 7

## 1. 任务列表 (TaskList)

- [ ] **环境搭建**
    - [ ] 在 `build.gradle.kts` 中添加 Supabase Kotlin 依赖。
    - [ ] 在 AndroidManifest.xml 中添加网络权限。
    - [ ] 初始化 Supabase Client。
- [ ] **UI 界面开发**
    - [ ] 创建邮箱输入框。
    - [ ] 创建“发送验证码”按钮。
    - [ ] 创建验证码输入框。
    - [ ] 创建“登录”按钮。
    - [ ] 添加加载和错误提示状态。
- [ ] **ViewModel 开发**
    - [ ] 创建 `LoginViewModel`。
    - [ ] 添加 `email` 和 `otp` 的 StateFlow。
    - [ ] 实现发送验证码的函数 `sendOtp()`。
    - [ ] 实现验证码登录的函数 `signInWithOtp()`。
    - [ ] 管理登录状态 (Loading, Success, Error)。
- [ ] **Supabase 集成**
    - [ ] 调用 `supabase.auth.signInWith(Otp) { email = "..." }` 发送验证码。
    - [ ] 调用 `supabase.auth.verifyEmailOtp(OtpType.Email.MagicLink, email, otp)` 进行登录验证。
- [ ] **测试**
    - [ ] 单元测试 `LoginViewModel`。
    - [ ] UI 测试登录流程。

## 2. 流程图 (Flowchart)

```mermaid
graph TD
    A[用户进入登录页面] --> B{输入邮箱};
    B --> C[点击“发送验证码”];
    C --> D{调用 Supabase 发送邮件};
    D -- 成功 --> E[邮箱收到验证码];
    D -- 失败 --> F[提示错误信息];
    E --> G{输入验证码};
    G --> H[点击“登录”];
    H --> I{调用 Supabase 验证};
    I -- 成功 --> J[登录成功，进入主页];
    I -- 失败 --> K[提示验证码错误];
```

## 3. UML 类图 (Class Diagram)

```mermaid
classDiagram
    class LoginScreen {
        +LoginViewModel
        +UI Elements
    }

    class LoginViewModel {
        -SupabaseAuthRepository
        +email: StateFlow<String>
        +otp: StateFlow<String>
        +loginState: StateFlow<LoginState>
        +sendOtp()
        +signInWithOtp()
    }

    class SupabaseAuthRepository {
        +sendOtp(email: String): Result<Unit>
        +signInWithOtp(email: String, otp: String): Result<Session>
    }

    object SupabaseClient {
        +auth: GoTrue
    }

    LoginScreen --> LoginViewModel
    LoginViewModel --> SupabaseAuthRepository
    SupabaseAuthRepository --> SupabaseClient

    enum LoginState {
        Idle,
        Loading,
        Success,
        Error
    }
```
