# 📧 Supabase 邮件模板使用指南

我已经为你创建了三个不同版本的邮件模板，你可以根据需要选择使用。

## 📁 模板文件说明

### 1. **supabase-email-template.html** (专业版)
- ✨ **特点**：最完整、最美观的模板
- 🎨 **设计**：渐变背景、专业布局、响应式设计
- 📱 **兼容性**：支持桌面端、移动端、暗色模式
- 🔧 **技术**：完整的 HTML/CSS，支持各种邮件客户端

### 2. **supabase-email-template-simple.html** (简化版)  
- ✨ **特点**：简洁清晰，兼容性好
- 🎨 **设计**：干净的布局，重点突出验证码
- 📱 **兼容性**：适用于大多数邮件客户端
- 🔧 **技术**：简化的 HTML/CSS

### 3. **supabase-email-template-text.txt** (文本版)
- ✨ **特点**：纯文本，最高兼容性
- 🎨 **设计**：ASCII 艺术风格
- 📱 **兼容性**：所有邮件客户端都支持
- 🔧 **技术**：纯文本，无 HTML

## 🚀 使用步骤

### 步骤 1：选择模板
根据你的需求选择一个模板：
- 推荐使用 **专业版** (supabase-email-template.html)
- 如果出现显示问题，使用 **简化版**
- 如果仍有问题，使用 **文本版**

### 步骤 2：复制模板代码
1. 打开对应的模板文件
2. 全选并复制内容 (Ctrl+A, Ctrl+C)

### 步骤 3：配置 Supabase
1. 登录 [Supabase Dashboard](https://supabase.com)
2. 选择你的项目
3. 点击 **Authentication** → **Email Templates**
4. 选择要修改的模板（推荐 "Confirm signup"）

### 步骤 4：粘贴模板
1. 在 **"Template"** 区域删除原有内容
2. 粘贴你选择的模板代码
3. 点击 **"Save"** 保存

### 步骤 5：配置邮件主题
在 **"Subject"** 字段中输入：
```
【易记账本】验证码：{{ .Token }}
```

或者更简单：
```
您的验证码：{{ .Token }}
```

## 🎨 自定义建议

### 修改品牌信息
替换模板中的以下内容：
- `💰 易记账本` → 你的应用名称
- `您的财务管理助手` → 你的应用简介
- 调整颜色主题（CSS 中的颜色值）

### 颜色定制
主要颜色变量（在 CSS 中修改）：
```css
/* 主色调 - 紫色渐变 */
background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

/* 验证码背景 - 红色渐变 */
background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

/* 可以改为你喜欢的颜色，比如蓝色：*/
background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
```

## 🧪 测试流程

### 1. 发送测试邮件
在你的应用中：
1. 输入真实邮箱地址
2. 点击发送验证码
3. 检查邮件接收情况

### 2. 检查显示效果
确认邮件中：
- ✅ 验证码正确显示（不是 `{{ .Token }}` 文本）
- ✅ 布局在手机上正常显示
- ✅ 所有链接和样式工作正常

### 3. 多客户端测试
测试不同邮件客户端：
- Gmail（网页版、手机 App）
- Outlook（网页版、桌面版）
- Apple Mail（iOS、macOS）
- 其他常用邮件客户端

## ⚠️ 故障排除

### 问题 1：验证码显示为 `{{ .Token }}`
**原因**：模板配置错误
**解决**：
1. 检查是否选择了正确的模板类型
2. 确保保存了模板设置
3. 重新测试发送

### 问题 2：邮件样式混乱
**原因**：邮件客户端不支持复杂 CSS
**解决**：
1. 切换到简化版模板
2. 或使用纯文本版本

### 问题 3：邮件未收到
**原因**：配置或网络问题
**解决**：
1. 检查垃圾邮件文件夹
2. 确认 Supabase Email Provider 已启用
3. 检查项目配置是否正确

### 问题 4：移动端显示问题
**原因**：响应式样式问题
**解决**：
1. 使用简化版模板
2. 检查 CSS 媒体查询

## 📱 移动端优化

所有模板都包含移动端优化：
- 自适应宽度
- 大号字体（验证码）
- 合适的按钮大小
- 简化的布局

## 🎯 最佳实践

### 1. 验证码安全
- ✅ 明确标明有效期（10分钟）
- ✅ 提醒用户不要分享验证码
- ✅ 说明用途（登录验证）

### 2. 用户体验
- ✅ 清晰的品牌标识
- ✅ 简洁的说明文字
- ✅ 明显的验证码显示

### 3. 技术考虑
- ✅ 兼容多种邮件客户端
- ✅ 响应式设计
- ✅ 快速加载

现在你可以根据这个指南来配置邮件模板了！如果遇到任何问题，可以尝试不同版本的模板。🎉