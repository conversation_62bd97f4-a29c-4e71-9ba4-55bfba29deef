# 登录系统分析

## 路由配置
位置: `common/module-base/src/main/java/com/xiaojinzi/tally/module/base/support/AppRouterConfig.kt`

- `USER_LOGIN = "user/login"` - 默认登录页面路由
- `USER_EMAIL_LOGIN = "user/emailLogin"` - 邮箱登录页面路由

## 登录方式
### 1. 默认登录 (手机登录)
- Activity: `LoginAct` 
- 路径: `common/module-user/src/main/java/com/xiaojinzi/tally/module/user/module/login/`
- 主要功能: 手机号 + 验证码登录

### 2. 邮箱登录
- Activity: `EmailLoginAct`
- 路径: `common/module-user/src/main/java/com/xiaojinzi/tally/module/user/module/email_login/`
- 路由: `AppRouterConfig.USER_EMAIL_LOGIN`

### 3. 第三方登录支持
支持的登录方式在 `SupportLoginMethod` 枚举中定义:
- `WX` - 微信登录
- `Google` - Google登录

## 配置位置
登录方式列表通过 `AppInfoSpi.supportLoginMethodList` 配置
- 基础实现: `BaseAppInfoSpiImpl` (默认返回空列表)
- 开源版实现: `AppOpenSourceInfoSpiImpl` (未覆盖此属性，使用默认空列表)