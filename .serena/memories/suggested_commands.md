# 建议命令

## 构建和开发
```bash
# 构建项目
./gradlew build

# 清理项目
./gradlew clean

# 运行测试
./gradlew test

# 安装调试版本
./gradlew installDebug
```

## 系统命令 (macOS/Darwin)
```bash
# 文件系统操作
ls -la          # 列出文件
find . -name    # 查找文件  
grep -r         # 搜索文件内容
cd              # 切换目录

# Git 操作
git status      # 查看状态
git add .       # 添加所有文件
git commit -m   # 提交
git push        # 推送
git pull        # 拉取
```

## 项目特定命令
由于项目使用 Gradle Wrapper，建议使用 `./gradlew` 而不是全局的 `gradle` 命令。