# Supabase Connector 迁移指南

## 📋 概述

本文档详细说明如何从旧的 `OtpAuthManager` 迁移到基于 PowerSync 最佳实践重构的新架构：
- `SupabaseConnector` - 统一的 Supabase 连接管理器
- `OtpAuthManagerV2` - 增强的 OTP 认证管理器
- `EmailLoginUseCaseV2` - 重构的邮箱登录业务逻辑

## 🏗️ 新架构概述

### 核心组件

1. **SupabaseConnector** - 核心连接器
   - 统一的 Supabase 客户端管理
   - 实时会话状态监听
   - 自动连接管理和重连
   - 统一的错误处理

2. **OtpAuthManagerV2** - 增强的认证管理器
   - 基于 SupabaseConnector 构建
   - 向后兼容的 API
   - 更好的错误处理
   - 实时状态监听

3. **EmailLoginUseCaseV2** - 重构的业务逻辑
   - 利用新的状态流
   - 更好的用户体验
   - 实时连接状态感知

## 🔄 迁移步骤

### 步骤 1：了解新的架构优势

#### 旧架构的问题
```kotlin
// 旧的 OtpAuthManager
class OtpAuthManager {
    // ❌ 每次都重新创建 Supabase 客户端
    // ❌ 没有连接状态管理
    // ❌ 错误处理不统一
    // ❌ 没有会话状态监听
    
    suspend fun sendEmailOtp(email: String): Boolean {
        // 直接调用 Supabase API，没有重试机制
    }
}
```

#### 新架构的优势
```kotlin
// 新的 SupabaseConnector + OtpAuthManagerV2
class SupabaseConnector {
    // ✅ 单例模式，统一管理 Supabase 客户端
    // ✅ 实时连接状态监听
    // ✅ 自动重连机制
    // ✅ 统一的错误处理和重试
    // ✅ 会话状态实时监听
    
    val sessionState: StateFlow<SessionState>
    val connectionState: StateFlow<ConnectionState>
}

class OtpAuthManagerV2 {
    // ✅ 基于 SupabaseConnector 构建
    // ✅ 向后兼容的 API
    // ✅ 更好的错误处理
    // ✅ 实时状态监听
}
```

### 步骤 2：渐进式迁移策略

#### 方案 A：完全迁移（推荐）

1. **替换 OtpAuthManager**
```kotlin
// 旧代码
class EmailLoginUseCaseImpl {
    private val otpAuthManager = OtpAuthManager()
}

// 新代码
class EmailLoginUseCaseImpl {
    private val otpAuthManager = OtpAuthManagerV2.getInstance()
}
```

2. **利用新的状态流**
```kotlin
// 新增：监听会话状态
val sessionStateOb: StateFlow<SupabaseConnector.SessionState> = otpAuthManager.sessionState

// 新增：监听连接状态
val connectionStateOb: StateFlow<SupabaseConnector.ConnectionState> = otpAuthManager.connectionState

// 新增：实时认证状态
val isAuthenticatedStateOb = sessionStateOb.map { 
    it == SupabaseConnector.SessionState.Authenticated 
}
```

3. **增强的错误处理**
```kotlin
// 旧代码
try {
    val success = otpAuthManager.sendEmailOtp(email)
    if (success) {
        // 处理成功
    } else {
        // 处理失败
    }
} catch (e: Exception) {
    // 通用错误处理
}

// 新代码
try {
    val success = otpAuthManager.sendEmailOtp(email)
    // OtpAuthManagerV2 内部已经处理了所有错误情况
    // 只有成功才会返回 true
} catch (e: AuthException) {
    // AuthException 包含用户友好的错误消息
    tip(content = e.message.toStringItemDto())
}
```

#### 方案 B：并行运行（过渡期）

如果需要保持向后兼容，可以同时运行两套系统：

```kotlin
class EmailLoginUseCaseImpl {
    // 保留旧的实现
    private val otpAuthManagerOld = OtpAuthManager()
    
    // 添加新的实现
    private val otpAuthManagerNew = OtpAuthManagerV2.getInstance()
    
    // 使用配置开关决定使用哪个实现
    private val useNewImplementation = true // 可以通过配置控制
    
    suspend fun sendEmailOtp(email: String): Boolean {
        return if (useNewImplementation) {
            otpAuthManagerNew.sendEmailOtp(email)
        } else {
            otpAuthManagerOld.sendEmailOtp(email)
        }
    }
}
```

### 步骤 3：API 对比和迁移

#### 发送 OTP
```kotlin
// 旧 API
val success: Boolean = otpAuthManager.sendEmailOtp(email)

// 新 API（相同）
val success: Boolean = otpAuthManagerV2.sendEmailOtp(email)

// 新 API（兼容模式）
val result: Result<Boolean> = otpAuthManagerV2.sendEmailOtpCompat(email)
```

#### 验证 OTP
```kotlin
// 旧 API
val session: UserSession = otpAuthManager.verifyEmailOtp(email, token)

// 新 API（相同）
val session: UserSession = otpAuthManagerV2.verifyEmailOtp(email, token)

// 新 API（兼容模式）
val result: Result<UserSession> = otpAuthManagerV2.verifyEmailOtpCompat(email, token)
```

#### 获取当前会话
```kotlin
// 旧 API
val session: UserSession? = otpAuthManager.getCurrentSession()

// 新 API（相同）
val session: UserSession? = otpAuthManagerV2.getCurrentSession()

// 新 API（实时状态）
val sessionState: StateFlow<SupabaseConnector.SessionState> = otpAuthManagerV2.sessionState
```

#### 登出
```kotlin
// 旧 API
otpAuthManager.signOut()

// 新 API（相同）
otpAuthManagerV2.signOut()

// 新 API（兼容模式）
val result: Result<Unit> = otpAuthManagerV2.signOutCompat()
```

### 步骤 4：利用新功能

#### 实时会话监听
```kotlin
// 在 UseCase 中监听会话状态
scope.launch {
    otpAuthManagerV2.sessionState.collect { sessionState ->
        when (sessionState) {
            is SupabaseConnector.SessionState.Authenticated -> {
                // 用户已认证
                val user = sessionState.session.user
                updateUI("欢迎 ${user?.email}")
            }
            is SupabaseConnector.SessionState.Unauthenticated -> {
                // 用户未认证
                navigateToLogin()
            }
            is SupabaseConnector.SessionState.Loading -> {
                // 会话加载中
                showLoading()
            }
            is SupabaseConnector.SessionState.Error -> {
                // 会话错误
                showError(sessionState.error.message)
            }
        }
    }
}
```

#### 连接状态监听
```kotlin
// 监听连接状态，提供更好的用户体验
scope.launch {
    otpAuthManagerV2.connectionState.collect { connectionState ->
        when (connectionState) {
            is SupabaseConnector.ConnectionState.Connected -> {
                // 已连接，可以进行操作
                enableLoginButton(true)
            }
            is SupabaseConnector.ConnectionState.Disconnected -> {
                // 断开连接，禁用操作
                enableLoginButton(false)
                showMessage("网络连接断开")
            }
            is SupabaseConnector.ConnectionState.Connecting -> {
                // 连接中
                showMessage("正在连接...")
            }
            is SupabaseConnector.ConnectionState.Error -> {
                // 连接错误
                showError("连接失败: ${connectionState.error.message}")
            }
        }
    }
}
```

#### 会话刷新
```kotlin
// 新功能：手动刷新会话
try {
    val refreshedSession = otpAuthManagerV2.refreshSession()
    // 会话刷新成功
} catch (e: AuthException) {
    // 刷新失败，可能需要重新登录
}
```

#### 获取访问令牌
```kotlin
// 新功能：获取访问令牌（用于 API 调用）
try {
    val accessToken = otpAuthManagerV2.getAccessToken()
    // 使用访问令牌调用 API
} catch (e: AuthException) {
    // 获取令牌失败
}
```

## 🔧 配置和初始化

### 应用启动时初始化
```kotlin
// 在 Application 或 ModuleApplication 中
class CoreModuleApplication : IApplicationLifecycle {
    override fun onCreate(app: Application) {
        // 预初始化 SupabaseConnector
        executeTaskInCoroutinesIgnoreError {
            val connector = SupabaseConnector.getInstance()
            connector.initialize()
        }
    }
}
```

### UseCase 中的初始化
```kotlin
class EmailLoginUseCaseV2Impl : BusinessUseCaseImpl(), EmailLoginUseCaseV2 {
    private val otpAuthManager = OtpAuthManagerV2.getInstance()
    
    init {
        // 确保认证管理器已初始化
        scope.launch {
            val result = otpAuthManager.initialize()
            if (result.isFailure) {
                Log.e(TAG, "认证管理器初始化失败: ${result.exceptionOrNull()?.message}")
            }
        }
    }
}
```

## 🧪 测试策略

### 单元测试
```kotlin
class OtpAuthManagerV2Test {
    
    @Test
    fun `test send email otp success`() = runTest {
        val manager = OtpAuthManagerV2.getInstance()
        
        // 模拟成功场景
        val result = manager.sendEmailOtp("<EMAIL>")
        assertTrue(result)
    }
    
    @Test
    fun `test session state flow`() = runTest {
        val manager = OtpAuthManagerV2.getInstance()
        
        // 测试会话状态流
        val states = mutableListOf<SupabaseConnector.SessionState>()
        val job = launch {
            manager.sessionState.take(3).collect { states.add(it) }
        }
        
        // 触发状态变化
        manager.sendEmailOtp("<EMAIL>")
        manager.verifyEmailOtp("<EMAIL>", "123456")
        
        job.join()
        assertEquals(3, states.size)
    }
}
```

### 集成测试
```kotlin
class EmailLoginIntegrationTest {
    
    @Test
    fun `test complete login flow`() = runTest {
        val useCase = EmailLoginUseCaseV2Impl()
        
        // 测试完整登录流程
        useCase.emailStateOb.emit(TextFieldValue("<EMAIL>"))
        useCase.sendEmailOtp(EmailLoginIntentV2.SendEmailOtp)
        
        // 验证状态变化
        assertTrue(useCase.canSubmitStateOb.first())
        
        useCase.otpCodeStateOb.emit(TextFieldValue("123456"))
        useCase.loginWithEmailOtp(EmailLoginIntentV2.LoginWithEmailOtp(context))
        
        // 验证登录成功
        assertTrue(useCase.isAuthenticatedStateOb.first())
    }
}
```

## 📊 性能优化

### 连接池管理
```kotlin
// SupabaseConnector 内部使用连接池
class SupabaseConnector {
    private val client by lazy {
        createSupabaseClient {
            // 配置连接池
            install(HttpTimeout) {
                requestTimeoutMillis = 30000
                connectTimeoutMillis = 10000
                socketTimeoutMillis = 30000
            }
        }
    }
}
```

### 状态缓存
```kotlin
// 缓存会话状态，避免重复查询
class SupabaseConnector {
    private val _sessionState = MutableStateFlow<SessionState>(SessionState.Loading)
    val sessionState: StateFlow<SessionState> = _sessionState.asStateFlow()
    
    // 缓存当前会话
    private var _currentSession: UserSession? = null
    val currentSession: UserSession? get() = _currentSession
}
```

## 🚨 常见问题和解决方案

### Q1: 迁移后出现 "客户端未初始化" 错误
**解决方案：**
```kotlin
// 确保在使用前初始化
val manager = OtpAuthManagerV2.getInstance()
manager.initialize().getOrThrow()
```

### Q2: 状态流没有及时更新
**解决方案：**
```kotlin
// 确保在正确的协程作用域中收集状态流
scope.launch {
    manager.sessionState.collect { state ->
        // 处理状态变化
    }
}
```

### Q3: 向后兼容性问题
**解决方案：**
```kotlin
// 使用兼容模式 API
val result = manager.sendEmailOtpCompat(email)
result.fold(
    onSuccess = { success -> /* 处理成功 */ },
    onFailure = { error -> /* 处理失败 */ }
)
```

### Q4: 内存泄漏问题
**解决方案：**
```kotlin
// 在适当的时候取消协程和清理资源
class EmailLoginUseCaseV2Impl {
    override fun destroy() {
        super.destroy()
        // 协程会自动取消，无需手动清理
        // SupabaseConnector 是单例，不需要销毁
    }
}
```

## 📈 监控和日志

### 日志配置
```kotlin
// 在 SupabaseConnector 中添加详细日志
class SupabaseConnector {
    companion object {
        private const val TAG = "SupabaseConnector"
        private const val ENABLE_DEBUG_LOGS = BuildConfig.DEBUG
    }
    
    private fun log(message: String) {
        if (ENABLE_DEBUG_LOGS) {
            Log.d(TAG, message)
        }
    }
}
```

### 性能监控
```kotlin
// 添加性能监控
class SupabaseConnector {
    suspend fun sendEmailOtp(email: String): Result<Unit> {
        val startTime = System.currentTimeMillis()
        return try {
            val result = performSendOtp(email)
            val duration = System.currentTimeMillis() - startTime
            log("发送 OTP 耗时: ${duration}ms")
            result
        } catch (e: Exception) {
            val duration = System.currentTimeMillis() - startTime
            log("发送 OTP 失败，耗时: ${duration}ms, 错误: ${e.message}")
            throw e
        }
    }
}
```

## 🎯 最佳实践

1. **渐进式迁移**：先在新功能中使用新架构，然后逐步迁移现有功能

2. **充分测试**：在迁移过程中进行充分的单元测试和集成测试

3. **监控性能**：关注迁移后的性能变化，确保没有性能回退

4. **用户体验**：利用新的状态流提供更好的用户体验

5. **错误处理**：充分利用新架构的统一错误处理机制

6. **文档更新**：及时更新相关文档和注释

## 📚 相关文档

- [SupabaseConnector API 文档](./SupabaseConnector.kt)
- [OtpAuthManagerV2 API 文档](./OtpAuthManagerV2.kt)
- [EmailLoginUseCaseV2 示例](./EmailLoginUseCaseV2.kt)
- [PowerSync Kotlin 最佳实践](https://github.com/powersync-ja/powersync-kotlin)

---

**注意：** 本迁移指南基于 PowerSync Kotlin 项目的最佳实践，旨在提供更稳定、更高效的 Supabase 集成方案。如有问题，请参考相关文档或联系开发团队。