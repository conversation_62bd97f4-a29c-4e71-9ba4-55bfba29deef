package com.xiaojinzi.tally.opensource

import com.xiaojinzi.component.anno.ServiceAnno
import com.xiaojinzi.tally.module.base.spi.AppInfoSpi
import com.xiaojinzi.tally.module.base.spi.BaseAppInfoSpiImpl
import com.xiaojinzi.tally.lib.res.SupportLoginMethod

@ServiceAnno(AppInfoSpi::class)
class AppOpenSourceInfoSpiImpl : BaseAppInfoSpiImpl() {

    override val forOpenSource: Boolean
        get() = true

    override val appLauncherForegroundIconRsd: Int
        get() = R.mipmap.ic_launcher_foreground

    override val appLauncherIconRsd: Int
        get() = R.mipmap.ic_launcher

    override val appIconRsd: Int
        get() = R.drawable.app_icon

    override val officialUrl: String
        get() = "https://yike.icxj.cn"

    override val supportLoginMethodList: List<SupportLoginMethod>
        get() = emptyList() // 开源版本禁用所有第三方登录
    
    /**
     * 开源版本登录功能开关
     * true: 允许登录 (默认)
     * false: 完全禁用登录功能，显示提示信息
     */
    val isLoginEnabled: Boolean
        get() = true // 可以通过配置或环境变量控制

}
