# 账本删除功能部署说明

## 🗑️ 功能概述

实现了带有24小时确认期的账本删除功能，确保用户不会意外丢失重要数据。

### 核心特性

1. **24小时确认期**：删除请求后有24小时的反悔时间
2. **安全检查**：不能删除最后一个账本，删除当前账本需要先切换
3. **状态显示**：清晰显示待删除状态和剩余时间
4. **一键取消**：在确认期内可以轻松取消删除

## 📋 部署步骤

### 步骤1：更新Supabase数据库

在Supabase控制台执行以下SQL脚本：

```sql
-- 执行文件：common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/add_book_delete_fields.sql
```

这将：
- 为 `user_books` 表添加删除相关字段
- 创建相关的数据库函数
- 添加必要的索引

### 步骤2：重新构建应用

由于修改了数据库结构，需要重新构建应用：

```bash
./gradlew :app:opensource:assembleDevDebug
./gradlew :app:opensource:installDevDebug
```

### 步骤3：测试功能

1. **基础删除测试**：
   - 创建多个账本
   - 尝试删除非当前账本
   - 验证24小时确认期

2. **边界情况测试**：
   - 尝试删除最后一个账本（应该被阻止）
   - 删除当前使用的账本（应该提示切换）
   - 取消删除操作

3. **UI状态测试**：
   - 验证待删除账本的显示状态
   - 检查倒计时显示
   - 测试取消删除按钮

## 🎯 功能说明

### 删除流程

1. **请求删除**：
   ```
   用户点击删除 → 安全检查 → 确认对话框 → 标记为待删除
   ```

2. **确认期状态**：
   ```
   待删除状态 → 显示倒计时 → 提供取消选项 → 24小时后自动删除
   ```

3. **取消删除**：
   ```
   点击取消 → 立即恢复正常状态 → 清除删除标记
   ```

### 安全机制

- ✅ **最后账本保护**：不能删除用户的最后一个账本
- ✅ **当前账本检查**：删除当前账本需要先切换到其他账本
- ✅ **确认对话框**：明确说明24小时确认期
- ✅ **状态提示**：清晰显示删除状态和剩余时间

### UI状态

- **正常状态**：正常显示账本信息
- **待删除状态**：
  - 半透明背景
  - "待删除"标签
  - 倒计时显示（如"23小时后删除"）
  - "取消删除"按钮
  - 禁用切换功能

## 🔧 技术实现

### 数据库字段

```sql
-- user_books 表新增字段
is_pending_delete BOOLEAN DEFAULT FALSE
delete_requested_at TIMESTAMP WITH TIME ZONE
```

### 核心方法

```kotlin
// 请求删除
suspend fun requestDeleteBook(bookId: String)

// 取消删除  
suspend fun cancelDeleteBook(bookId: String)

// 确认删除（立即删除）
suspend fun confirmDeleteBook(bookId: String)
```

### Intent类型

```kotlin
data class RequestDeleteBook(val bookId: String) : BookSelectIntent()
data class CancelDeleteBook(val bookId: String) : BookSelectIntent()
data class ConfirmDeleteBook(val bookId: String) : BookSelectIntent()
```

## ⚠️ 注意事项

1. **数据库迁移**：本地数据库版本从3升级到4
2. **Supabase同步**：删除状态会同步到云端
3. **定时清理**：可以实现定时任务自动清理过期的删除请求
4. **数据恢复**：一旦确认删除，数据将无法恢复

## 🚀 后续优化

1. **定时任务**：实现自动清理过期删除请求的后台任务
2. **批量操作**：支持批量删除多个账本
3. **删除原因**：记录删除原因用于数据分析
4. **回收站**：实现软删除和回收站功能

## 📱 用户体验

- **直观操作**：长按或滑动显示删除选项
- **清晰提示**：明确的状态显示和时间倒计时
- **安全保障**：多重确认和24小时反悔期
- **简单取消**：一键取消删除操作

---

部署完成后，用户将拥有一个安全、可靠的账本删除功能！🎉
