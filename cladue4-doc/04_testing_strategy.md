# 4. 测试策略

为确保应用的质量和稳定性，项目采用分层测试策略，遵循测试金字塔模型。

## 4.1. 测试金字塔

```
        /\        <-- UI 测试 (Espresso, Compose Test)
       /  \       
      /----\      <-- 集成测试 (Room, Ktor Client, Hilt)
     /      \     
    /--------\    <-- 单元测试 (JUnit, Mockito, Turbine)
   /__________\   
```

- **单元测试 (70%)**: 关注最小的可测试单元，如单个函数、类、ViewModel 的业务逻辑。速度快，数量多。
- **集成测试 (20%)**: 测试多个组件协同工作，如 Repository 与数据库的交互、UseCase 与 Repository 的集成。
- **UI 测试 (10%)**: 从用户视角验证关键业务流程。速度慢，数量少，只覆盖核心路径。

## 4.2. 单元测试 (Unit Tests)

- **工具**: JUnit 5, Mocki<PERSON>/MockK, Turbine (用于测试 Flow)。
- **测试对象**:
  - **ViewModel**: 验证 UI 状态 (`uiState`) 是否根据业务逻辑正确更新。模拟 (Mock) UseCase 和 Repository。
  - **UseCase**: 验证其是否正确执行了业务规则。模拟 Repository。
  - **Repository**: 验证其是否正确地调用了 DataSource 并处理数据。模拟 DataSource。
  - **工具类/辅助函数**: 验证纯逻辑的正确性。

**示例：ViewModel 测试**
```kotlin
@Test
fun `loadBills should update uiState with bills on success`() = runTest {
    // Given: 准备测试数据和模拟对象的行为
    val bills = listOf(Bill(...))
    whenever(getBillsUseCase()).thenReturn(Result.success(bills))
    
    // When: 执行被测试的方法
    viewModel.loadBills()
    
    // Then: 验证结果是否符合预期
    val finalState = viewModel.uiState.value
    assertThat(finalState.bills).isEqualTo(bills)
    assertThat(finalState.isLoading).isFalse()
}
```

## 4.3. 集成测试 (Integration Tests)

- **工具**: AndroidX Test, Hilt Android Testing, Room Testing, MockWebServer.
- **测试对象**:
  - **数据库 (DAO)**: 使用 Room 的内存数据库 (`inMemoryDatabaseBuilder`) 测试 DAO 的增删改查操作是否正确。
  - **网络层**: 使用 `MockWebServer` 模拟服务器响应，测试 ApiService 和远程 DataSource 是否能正确解析数据。
  - **数据层集成**: 测试 Repository 与真实的（内存）数据库和模拟的网络层交互是否正常。

**示例：DAO 测试**
```kotlin
@Test
fun `insertAndGetBill should retrieve correct bill`() = runTest {
    // Given
    val billEntity = BillEntity(...)
    billDao.insert(billEntity)
    
    // When
    val retrievedBill = billDao.getById(billEntity.id)
    
    // Then
    assertThat(retrievedBill).isEqualTo(billEntity)
}
```

## 4.4. UI 测试 (UI Tests)

- **工具**: Espresso, Jetpack Compose Test (`createComposeRule`).
- **测试对象**:
  - **关键用户流程**: 如登录、创建账单、查看报表等。
  - **Compose Screen**: 验证在给定状态下，UI 是否正确显示。模拟 ViewModel 或直接传入状态。
  - **导航**: 验证用户操作是否能触发正确的页面跳转。

**示例：Compose UI 测试**
```kotlin
@Test
fun `BillListScreen should display empty message when bill list is empty`() {
    // Given
    composeTestRule.setContent {
        BillListScreen(uiState = BillListUiState(bills = emptyList()))
    }
    
    // Then
    composeTestRule
        .onNodeWithText("暂无账单")
        .assertIsDisplayed()
}
```

## 4.5. 测试覆盖率

- **目标**: 整体代码覆盖率 > 80%。
- **工具**: JaCoCo。
- **CI/CD**: 在 CI 流程中集成测试和覆盖率报告，确保每次提交都符合质量标准。

## 4.6. 测试数据

- **测试工厂**: 创建 `TestDataFactory` 对象来生成一致的、可复用的测试数据。
- **Fake 实现**: 对于复杂的依赖，可以创建 Fake 实现（如 `FakeBillRepository`），它使用内存中的数据结构来模拟真实行为，比 Mock 更灵活。
