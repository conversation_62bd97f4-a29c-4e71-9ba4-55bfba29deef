# 5. Supabase 集成指南

本指南全面介绍了在 yike-app 中 Supabase 的使用方法，包括认证、数据库和最佳实践。

## 5.1. 核心理念

Supabase 在本项目中作为核心的后端即服务 (BaaS)，提供数据库、用户认证和数据同步能力。我们遵循 "Supabase as the source of truth" 的原则，云端数据是权威的，本地数据是缓存和离线支持。

## 5.2. 统一连接器: `SupabaseConnector`

为了统一管理与 Supabase 的交互，项目抽象了 `SupabaseConnector` 作为唯一的连接管理器。

- **职责**:
  - 管理 Supabase 客户端的单例实例。
  - 监听和广播会话状态 (`SessionState`) 和网络连接状态 (`ConnectionState`)。
  - 提供自动重连和统一的错误处理机制。
- **使用**: 所有需要与 Supabase 交互的模块（如 `OtpAuthManagerV2`）都应通过 `SupabaseConnector.getInstance()` 获取实例，而不是自行创建客户端。

## 5.3. 用户认证 (Supabase Auth)

项目采用基于邮箱 OTP (一次性密码) 的无密码登录方案。

### 5.3.1. 认证流程管理器: `OtpAuthManagerV2`

- **功能**:
  - 封装发送和验证邮箱 OTP 的逻辑。
  - 提供倒计时功能，防止用户频繁请求验证码。
  - 通过 `Flow<AuthEvent>` 广播认证过程中的关键事件（如 `OtpError`），实现响应式错误处理。

### 5.3.2. 实施流程

1.  **UI 层**: 用户输入邮箱，点击发送。
2.  **ViewModel**: 调用 `OtpAuthManagerV2.sendEmailOtp(email)`。
3.  **`OtpAuthManagerV2`**: 
    - 立即返回 `true` 并启动 60 秒倒计时 (`resendCountdown` StateFlow)。
    - 异步调用 Supabase 发送邮件。
4.  **UI 层**: 监听 `resendCountdown` 更新按钮状态（例如 "重新发送(59s)"）。
5.  **用户**: 输入收到的验证码。
6.  **ViewModel**: 调用 `OtpAuthManagerV2.verifyEmailOtp(email, token)`。
7.  **`OtpAuthManagerV2`**: 调用 Supabase 验证 OTP。如果验证失败，会通过 `authEvents` Flow 发出 `AuthEvent.OtpError` 事件。
8.  **ViewModel/UseCase**: 监听 `authEvents` Flow，处理错误（如 "验证码无效"）并更新 UI。
9.  **登录成功**: Supabase 返回 `UserSession`，`SupabaseConnector` 的 `sessionState` 更新为 `Authenticated`。

### 5.3.3. Supabase 后台配置

- **启用 Email Provider**: 在 `Authentication -> Providers` 中启用 Email。
- **修改邮件模板**: 在 `Authentication -> Email Templates` 中，将默认的 `{{ .ConfirmationURL }}` 替换为 `{{ .Token }}`，以便邮件中直接显示验证码。

## 5.4. 数据库 (Supabase PostgreSQL)

### 5.4.1. Schema 和初始化

- **Schema 定义**: 完整的数据库表结构、函数和策略定义在根目录的 `supabase_database_schema.sql` 文件中。
- **初始化**: 新的 Supabase 项目需要首先在 SQL Editor 中执行此脚本。
- **自动化**: 脚本包含一个触发器，当新用户注册时 (`auth.users` 表插入新行)，会自动为其创建默认的账本、分类和账户。

### 5.4.2. 表结构核心

- `bills`: 账单表，核心数据。
- `books`: 账本表。
- `categories`: 分类表。
- `accounts`: 账户表。
- 所有表都包含 `user_id` 字段，并与 `auth.users.id` 关联。

### 5.4.3. 数据安全: 行级安全 (RLS)

- **强制启用**: 所有核心数据表都已启用 RLS。
- **策略**: `uid() = user_id`。此策略确保用户只能对自己创建的数据进行增删改查操作。这是多租户数据隔离的核心保障。

### 5.4.4. Repository 模式

- **统一接口**: 项目为每个核心表（如 `bills`, `books`）都提供了 `Supabase...Repository` 类。
- **CRUD 操作**: Repository 封装了对 Supabase 数据库的增删改查操作，并返回 `Result<T>`。
- **软删除**: 优先使用软删除 (`is_deleted = true`)，而不是物理删除。

**示例：插入一条账单**
```kotlin
val newBill = SupabaseBill(...)
val result = SupabaseRepositoryManager.billRepository.insert(newBill)
result.fold(
    onSuccess = { Log.d(TAG, "账单创建成功") },
    onFailure = { Log.e(TAG, "创建失败: ${it.message}") }
)
```

## 5.5. 最佳实践和指南

- **状态监听**: 优先监听 `SupabaseConnector` 提供的 `sessionState` 和 `connectionState` 来响应全局状态变化，而不是在需要时手动查询。
- **错误处理**: 除了 `try-catch`，还应监听 `OtpAuthManagerV2` 的 `authEvents` Flow 来处理认证相关的特定错误，提供更精确的用户反馈。
- **金额单位**: 所有与金额相关的字段在数据库中都以 **分** 为单位存储（整数类型），避免浮点数精度问题。
- **倒计时 UI**: `OtpAuthManagerV2` 的 `resendCountdown` StateFlow 专为驱动 UI 倒计时而设计，UI 层应直接收集此 Flow。
