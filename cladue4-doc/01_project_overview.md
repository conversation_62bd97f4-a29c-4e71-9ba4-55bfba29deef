# 1. 项目概述

## 1.1. 项目目的

本项目是一个名为 **yike-app (易记账本)** 的 Android 记账应用，使用 Kotlin 和 Jetpack Compose 开发。旨在为用户提供一个简单、高效、安全的移动记账解决方案。

核心功能包括：
- 多账本管理
- 详细的收支分类
- 资金账户管理
- 数据云端同步与备份
- 报表与统计分析

## 1.2. 技术栈

- **语言**: Kotlin
- **平台**: Android
- **UI 框架**: Jetpack Compose
- **架构**: MVI (Model-View-Intent) / MVVM (Model-View-ViewModel)
- **组件化**: `xiaojinzi/KComponent`
- **依赖注入**: Hilt
- **后端服务**: Supabase
  - **数据库**: Supabase PostgreSQL
  - **认证**: Supabase Auth (邮箱 OTP)
  - **存储**: Supabase Storage (未来可能用于存储账单图片)
- **构建工具**: Gradle

## 1.3. 核心原则

- **模块化**: 功能高度解耦，易于维护和扩展。
- **响应式编程**: 大量使用 Kotlin Flow 和 StateFlow 进行状态管理。
- **单一数据源**: 以 Supabase 作为云端单一数据源，保证数据一致性。
- **安全第一**: 所有数据操作都经过行级安全 (RLS) 策略保护。
- **用户体验优先**: 界面简洁，操作流畅，反馈及时。
