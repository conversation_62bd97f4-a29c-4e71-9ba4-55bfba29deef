# 2. 项目结构

## 2.1. 模块化架构

项目采用严格的模块化架构，以实现高度解耦和可维护性。模块主要分为三层：应用层、功能模块层和基础模块层。

### 2.1.1. 模块依赖关系

依赖关系遵循单向原则，上层模块可以依赖下层模块，禁止反向或同层依赖。

```
   应用层 (app)
       ↓
   功能模块层 (module-*)
       ↓
   基础/核心模块层 (module-base, module-core, lib-res)
```

### 2.1.2. 模块职责详解

```
yike-app/
├── opensource/             # 应用层 - 开源版本
│
├── common/                 # 共享代码
│   ├── module-base/        # 基础模块：提供最基础的工具、路由接口、SPI 定义等，被所有模块依赖。
│   ├── module-core/        # 核心模块：包含核心业务逻辑和数据模型，如 Supabase 连接器、Repository 基类等。
│   ├── module-user/        # 用户模块：处理用户认证（登录、注册）、用户信息管理。
│   ├── module-main/        # 主界面模块：App 的主界面、导航框架。
│   ├── module-datasource/  # 数据源模块：定义数据源接口和实现（本地/远程）。
│   ├── module-widget/      # UI 组件模块：包含通用的 Jetpack Compose UI 组件。
│   ├── module-image-picker/ # 图片选择模块
│   ├── module-image-preview/ # 图片预览模块
│   ├── module-qrcode/      # 二维码模块
│   └── lib-res/            # 资源库：存放全局共享的静态资源，如图片、字符串、主题等。
│
└── build-logic/            # Gradle 构建逻辑：统一管理依赖版本和构建配置。
```

## 2.2. 包结构规范

每个功能模块内部遵循统一的包结构，以分层架构为基础：

```
com.xiaojinzi.tally.{module_name}
├── ui/                 # UI 层：负责界面展示和用户交互
│   ├── view/           # Composable 函数、Screen、View
│   └── viewmodel/      # ViewModel，处理 UI 状态和业务逻辑
├── domain/             # 领域层：纯粹的业务逻辑
│   ├── usecase/        # 用例 (Use Case)，封装单一的业务操作
│   └── model/          # 领域模型 (Business Model)
├── data/               # 数据层：负责数据的获取、存储和转换
│   ├── repository/     # 仓库 (Repository) 实现
│   ├── datasource/     # 数据源接口和实现 (Local/Remote)
│   └── entity/         # 数据实体 (Data Entity)，如数据库表对象
└── di/                 # 依赖注入模块 (Hilt Modules)
```

## 2.3. 关键文件和目录

- `common/build.gradle.kts`: 主构建文件，管理所有 `common` 模块。
- `common/libs.versions.toml`: **[重要]** `Version Catalog` 文件，集中管理所有库的依赖版本。
- `supabase_database_schema.sql`: Supabase 数据库的完整 schema 定义。
- `.kiro/steering/`: 包含项目架构、编码规范等核心设计文档。
