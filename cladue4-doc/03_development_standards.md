# 3. 开发规范

本文档整合了项目的架构原则、代码规范和 UI 开发指南，为所有开发者提供统一的标准。

## 3.1. 架构原则

### 3.1.1. 分层架构

每个功能模块内部都应遵循清晰的三层架构模式：UI 层、业务层 (ViewModel/UseCase)、数据层 (Repository)。

- **UI Layer**: 只负责展示数据和发送用户事件。使用 Jetpack Compose 构建，应为无状态 (Stateless) Composable。
- **ViewModel**: 持有并管理 UI 状态 (`StateFlow`)，调用 UseCase 执行业务逻辑。
- **UseCase**: 封装单一、具体的业务逻辑，可被多个 ViewModel 复用。
- **Repository**: 数据访问的唯一入口，屏蔽数据来源（本地缓存或远程服务器）。它负责协调数据，但不应包含业务逻辑。
- **DataSource**: 负责直接与数据源（数据库、网络 API）交互。

```kotlin
// 数据流向
UI -> ViewModel -> UseCase -> Repository -> DataSource
```

### 3.1.2. 模块间通信

- **严禁模块间直接引用**: 功能模块之间不能直接相互依赖。
- **通过接口通信**: 使用 `KComponent` 提供的服务发现 (SPI) 或路由机制进行通信。
  - **服务接口 (SPI)**: 在 `module-base` 或 `module-core` 中定义接口，在具体的功能模块中实现。Hilt 用于注入实现。
  - **路由 (Router)**: 用于页面跳转，通过统一定义的 URL `hostAndPath` 进行导航。

### 3.1.3. 数据管理

- **Repository 模式**: 所有数据访问必须通过 Repository 进行。
- **单一数据源**: 对于需要持久化的数据，以 Supabase 为云端权威数据源，本地数据库作为缓存和离线支持。
- **数据同步**: 设计明确的数据同步策略（如：启动时同步、定时同步、手动同步），确保本地与云端数据的一致性。

## 3.2. 代码风格与规范

### 3.2.1. 命名

- **类**: `PascalCase`，名词。例如 `BillRepository`, `User`。
- **接口**: `PascalCase`，以 `I` 开头或 `er/able` 结尾。例如 `IBillRepository`, `Calculable`。
- **函数**: `camelCase`，动词。例如 `calculateTotalAmount()`。
- **常量**: `SCREAMING_SNAKE_CASE`。例如 `MAX_BILL_AMOUNT`。
- **Composable 函数**: `PascalCase`，名词性，描述其代表的 UI 组件。例如 `BillListScreen`, `SubmitButton`。

### 3.2.2. 空安全

- **优先使用非空类型**: 尽力避免可空类型。
- **合理处理可空值**: 使用 `?.`、`?:`、`let` 等安全操作符，避免 `!!`。

### 3.2.3. 错误处理

- **使用 `Result` 类型**: 对于可能失败的操作（如网络请求、数据库操作），函数应返回 `Result<T>`。
- **自定义异常**: 定义具体的业务异常类（如 `BillValidationException`），而不是抛出通用 `Exception`。

### 3.2.4. 日志

- **统一 TAG**: 在每个类中定义伴生对象的 `TAG` 常量。
- **分级记录**: 正确使用 `Log.d`, `Log.i`, `Log.w`, `Log.e`。

## 3.3. Jetpack Compose UI 开发规范

### 3.3.1. 状态管理

- **状态提升 (State Hoisting)**: 将状态从子 Composable 提升到其父 Composable 或 ViewModel，使子 Composable 变为无状态，易于预览和重用。
- **ViewModel 持有状态**: UI 的主状态应由 ViewModel 持有，并通过 `StateFlow` 暴露给 UI 层。
- **`remember` vs `rememberSaveable`**: 对简单的、可重新计算的 UI 状态（如动画开关）使用 `remember`。对需要在配置变更（如旋转屏幕）后保留的状态（如表单输入）使用 `rememberSaveable`。

### 3.3.2. Composable 函数设计

- **单一职责**: 每个 Composable 函数应只做一件事。
- **可预览性**: 使用 `@Preview` 注解为 Composable 提供预览，方便快速开发和调试。
- **参数顺序**: `(必需数据, 必需回调, modifier, 可选数据, 可选回调)`。
- **Modifier**: 几乎所有 Composable 都应接受一个 `modifier: Modifier = Modifier` 参数，并将其传递给其根布局元素。

### 3.3.3. 性能优化

- **使用 `key`**: 在 `LazyColumn` / `LazyRow` 的 `items` 中提供稳定的 `key`，以帮助 Compose 优化重组。
- **使用 `@Immutable` 或 `@Stable`**: 为数据类添加注解，帮助 Compose 编译器跳过不必要的重组检查。
- **使用 `derivedStateOf`**: 当一个状态依赖于另一个或多个状态计算而来时，使用 `derivedStateOf` 避免在每次依赖项变化时不必要地重组 UI。

### 3.3.4. 主题和样式

- **使用统一定义**: 颜色、字体、尺寸应在主题中统一定义（如 `YikeColors`, `YikeTypography`, `YikeDimensions`），禁止在 Composable 中硬编码。
- **支持暗色模式**: 设计应同时考虑亮色和暗色模式。
