# 邮箱登录模式改造及账本管理集成

## Core Features

- 邮箱密码登录

- 账本自动选择

- 类别数据管理

- 默认数据初始化

## Tech Stack

{
  "Android": "Kotlin + Gradle + 模块化架构 + Supabase"
}

## Design

保持现有UI设计风格，隐藏OTP验证码登录，改为纯邮箱+密码登录，不需要注册功能

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 隐藏OTP验证码登录，改造成纯邮箱+密码登录界面

[X] 移除注册功能，简化登录流程

[X] 实现邮箱密码登录逻辑，集成Supabase Auth

[X] 选择默认账本功能

[X] 将当前默认类别保存到账本内

[X] 创建默认账户（现金账户）

[X] 扩展SupabaseConnector支持邮箱密码认证

[X] 创建账本相关数据模型和API接口类

[X] 设计并创建Supabase数据库表结构（账本、类别、配置）

[X] 修复编译错误和语法问题

[X] 修复Supabase-kt查询语法和方法冲突问题

[X] 使用Context7查询并修复所有Supabase-kt编译错误

[X] 测试编译通过
