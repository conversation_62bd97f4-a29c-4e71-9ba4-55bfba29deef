-- =============================================
-- 修复 categories 表的 sort_order 字段问题
-- 问题：代码期望 sort_order 字段，但表中实际是 sort 字段
-- =============================================

-- 方案1：为 categories 表添加 sort_order 列（推荐）
-- 这样可以保持向后兼容性，同时满足新代码的需求

-- 检查 sort_order 列是否已存在
DO $$
BEGIN
    -- 如果 sort_order 列不存在，则添加它
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' 
        AND column_name = 'sort_order'
    ) THEN
        -- 添加 sort_order 列
        ALTER TABLE categories ADD COLUMN sort_order INTEGER DEFAULT 0;
        
        -- 将现有的 sort 值复制到 sort_order
        -- 注意：sort 是 BIGINT，sort_order 是 INTEGER，需要处理溢出
        UPDATE categories 
        SET sort_order = CASE 
            WHEN sort > 2147483647 THEN 2147483647  -- INTEGER 最大值
            WHEN sort < -2147483648 THEN -2147483648 -- INTEGER 最小值
            ELSE sort::INTEGER
        END;
        
        RAISE NOTICE 'Added sort_order column to categories table and copied values from sort column';
    ELSE
        RAISE NOTICE 'sort_order column already exists in categories table';
    END IF;
END $$;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(sort_order);

-- 创建触发器函数，保持 sort 和 sort_order 同步
CREATE OR REPLACE FUNCTION sync_categories_sort_fields()
RETURNS TRIGGER AS $$
BEGIN
    -- 当 sort 字段更新时，同步更新 sort_order
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        NEW.sort_order = CASE 
            WHEN NEW.sort > 2147483647 THEN 2147483647
            WHEN NEW.sort < -2147483648 THEN -2147483648
            ELSE NEW.sort::INTEGER
        END;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_sync_categories_sort_fields ON categories;
CREATE TRIGGER trigger_sync_categories_sort_fields
    BEFORE INSERT OR UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION sync_categories_sort_fields();

-- 验证修复结果
DO $$
DECLARE
    sort_order_exists BOOLEAN;
    record_count INTEGER;
BEGIN
    -- 检查 sort_order 列是否存在
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' 
        AND column_name = 'sort_order'
    ) INTO sort_order_exists;
    
    IF sort_order_exists THEN
        -- 获取记录数量
        SELECT COUNT(*) FROM categories INTO record_count;
        RAISE NOTICE 'SUCCESS: sort_order column exists in categories table with % records', record_count;
    ELSE
        RAISE NOTICE 'ERROR: sort_order column still missing from categories table';
    END IF;
END $$;
