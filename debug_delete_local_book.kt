/**
 * 调试工具：删除本地错误账本
 * 目的：删除本地账本 opensourceBook_9eeeb0ef-e537-463f-b981-dfbed880b87a（只有11个类别）
 * 保留正确账本 846021b5-a847-4585-ae17-4a57cad597bf（有50个类别）
 */

import com.xiaojinzi.tally.module.datasource.spi.TallyDataSourceSpiImpl
import com.xiaojinzi.tally.module.datasource.storage.db.tally.TallyDb
import kotlinx.coroutines.runBlocking

suspend fun deleteLocalWrongBook() {
    println("\n========== 删除错误本地账本 ==========")
    
    val wrongBookId = "opensourceBook_9eeeb0ef-e537-463f-b981-dfbed880b87a"
    val correctBookId = "846021b5-a847-4585-ae17-4a57cad597bf"
    
    // 检查账本存在情况
    val allBooks = TallyDb.database.bookDao().getAll()
    println("删除前账本列表:")
    allBooks.forEach { book ->
        println("  - ${book.name}(${book.id})")
    }
    
    // 检查类别分布
    val allCategories = TallyDb.database.categoryDao().getAll()
    val wrongBookCategories = allCategories.filter { it.bookId == wrongBookId }
    val correctBookCategories = allCategories.filter { it.bookId == correctBookId }
    
    println("\n类别分布:")
    println("  错误账本 $wrongBookId: ${wrongBookCategories.size} 个类别")
    println("  正确账本 $correctBookId: ${correctBookCategories.size} 个类别")
    
    // 确认删除
    if (wrongBookCategories.size == 11 && correctBookCategories.size == 50) {
        println("\n✅ 确认删除错误账本及其所有数据...")
        
        // 使用现有的clearAllDataByBookId方法删除账本及其所有相关数据
        val dataSource = TallyDataSourceSpiImpl()
        dataSource.clearAllDataByBookId(wrongBookId)
        
        println("✅ 错误账本删除完成")
        
        // 验证删除结果
        val remainingBooks = TallyDb.database.bookDao().getAll()
        val remainingCategories = TallyDb.database.categoryDao().getAll()
        
        println("\n删除后验证:")
        println("  剩余账本数量: ${remainingBooks.size}")
        remainingBooks.forEach { book ->
            println("    - ${book.name}(${book.id})")
        }
        println("  剩余类别数量: ${remainingCategories.size}")
        
        // 检查是否只剩下正确账本的类别
        val correctBookRemainingCategories = remainingCategories.filter { it.bookId == correctBookId }
        println("  正确账本类别数量: ${correctBookRemainingCategories.size}")
        
        if (correctBookRemainingCategories.size == 50) {
            println("\n🎉 删除成功！现在应用将显示正确的50个类别")
        } else {
            println("\n❌ 删除后类别数量不正确，请检查")
        }
    } else {
        println("\n❌ 类别数量不符合预期，取消删除操作")
        println("  预期：错误账本11个类别，正确账本50个类别")
        println("  实际：错误账本${wrongBookCategories.size}个类别，正确账本${correctBookCategories.size}个类别")
    }
}

// 主函数用于测试
fun main() {
    runBlocking {
        deleteLocalWrongBook()
    }
}