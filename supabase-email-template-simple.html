<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>邮箱验证码</title>
    <style type="text/css">
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 40px 30px;
            text-align: center;
        }
        
        .title {
            color: #333;
            font-size: 20px;
            margin-bottom: 20px;
        }
        
        .message {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .otp-box {
            background: linear-gradient(45deg, #ff6b6b, #ff8787);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        
        .otp-label {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .otp-code {
            font-size: 36px;
            font-weight: bold;
            letter-spacing: 5px;
            font-family: 'Courier New', monospace;
            margin: 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 14px;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #eee;
        }
        
        /* Mobile styles */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .otp-code {
                font-size: 30px;
                letter-spacing: 3px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>💰 易记账本</h1>
            <p>您的财务管理助手</p>
        </div>
        
        <div class="content">
            <h2 class="title">邮箱验证码</h2>
            <p class="message">您正在尝试登录易记账本，请使用下方验证码完成登录：</p>
            
            <div class="otp-box">
                <p class="otp-label">验证码</p>
                <h1 class="otp-code">{{ .Token }}</h1>
            </div>
            
            <div class="warning">
                ⏰ <strong>重要提醒：</strong>验证码有效期为 10 分钟，请尽快使用。<br>
                🔒 请勿将验证码告知他人，保护账户安全。
            </div>
        </div>
        
        <div class="footer">
            此邮件由系统自动发送，请勿回复。<br>
            如有疑问，请联系客服支持。
        </div>
    </div>
</body>
</html>