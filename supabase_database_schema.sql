-- =============================================
-- Supabase 数据库表结构设计
-- 用于记账应用的账本、类别、配置管理
-- =============================================

-- 注意：JWT密钥设置需要在Supabase控制台的设置中配置
-- ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- =============================================
-- 1. 用户账本表 (user_books)
-- =============================================
CREATE TABLE IF NOT EXISTS user_books (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon_name VARCHAR(50) DEFAULT 'book1',
    color VARCHAR(7) DEFAULT '#2196F3', -- 十六进制颜色值
    is_default BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT user_books_name_check CHECK (LENGTH(name) >= 1),
    CONSTRAINT user_books_color_check CHECK (color ~ '^#[0-9A-Fa-f]{6}$')
);

-- 为user_books表创建索引
CREATE INDEX IF NOT EXISTS idx_user_books_user_id ON user_books(user_id);
CREATE INDEX IF NOT EXISTS idx_user_books_is_default ON user_books(user_id, is_default);
CREATE INDEX IF NOT EXISTS idx_user_books_sort ON user_books(user_id, sort_order);

-- =============================================
-- 2. 账本类别表 (book_categories)
-- =============================================
CREATE TABLE IF NOT EXISTS book_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES user_books(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES book_categories(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('income', 'spending', 'transfer')),
    icon_name VARCHAR(50) DEFAULT 'category1',
    color VARCHAR(7) DEFAULT '#4CAF50',
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT book_categories_name_check CHECK (LENGTH(name) >= 1),
    CONSTRAINT book_categories_color_check CHECK (color ~ '^#[0-9A-Fa-f]{6}$'),
    CONSTRAINT book_categories_parent_check CHECK (parent_id != id)
);

-- 为book_categories表创建索引
CREATE INDEX IF NOT EXISTS idx_book_categories_book_id ON book_categories(book_id);
CREATE INDEX IF NOT EXISTS idx_book_categories_user_id ON book_categories(user_id);
CREATE INDEX IF NOT EXISTS idx_book_categories_parent_id ON book_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_book_categories_type ON book_categories(book_id, type);
CREATE INDEX IF NOT EXISTS idx_book_categories_sort ON book_categories(book_id, sort_order);

-- =============================================
-- 3. 用户配置表 (user_configs)
-- =============================================
CREATE TABLE IF NOT EXISTS user_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string' CHECK (config_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 唯一约束：每个用户的配置键唯一
    CONSTRAINT user_configs_unique_key UNIQUE (user_id, config_key)
);

-- 为user_configs表创建索引
CREATE INDEX IF NOT EXISTS idx_user_configs_user_id ON user_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_configs_key ON user_configs(user_id, config_key);

-- =============================================
-- 4. 账本账户表 (book_accounts)
-- =============================================
CREATE TABLE IF NOT EXISTS book_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    book_id UUID NOT NULL REFERENCES user_books(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    type VARCHAR(20) DEFAULT 'cash' CHECK (type IN ('cash', 'bank', 'credit', 'investment', 'other')),
    icon_name VARCHAR(50) DEFAULT 'cash1',
    color VARCHAR(7) DEFAULT '#FF9800',
    balance_init BIGINT DEFAULT 0, -- 以分为单位存储金额
    balance_current BIGINT DEFAULT 0,
    is_default BOOLEAN DEFAULT FALSE,
    is_excluded BOOLEAN DEFAULT FALSE, -- 是否排除在统计之外
    sort_order INTEGER DEFAULT 0,
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT book_accounts_name_check CHECK (LENGTH(name) >= 1),
    CONSTRAINT book_accounts_color_check CHECK (color ~ '^#[0-9A-Fa-f]{6}$')
);

-- 为book_accounts表创建索引
CREATE INDEX IF NOT EXISTS idx_book_accounts_book_id ON book_accounts(book_id);
CREATE INDEX IF NOT EXISTS idx_book_accounts_user_id ON book_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_book_accounts_type ON book_accounts(book_id, type);
CREATE INDEX IF NOT EXISTS idx_book_accounts_sort ON book_accounts(book_id, sort_order);

-- =============================================
-- 行级安全策略 (Row Level Security)
-- =============================================

-- 启用RLS
ALTER TABLE user_books ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_accounts ENABLE ROW LEVEL SECURITY;

-- user_books 表的RLS策略
CREATE POLICY "用户只能访问自己的账本" ON user_books
    FOR ALL USING (auth.uid() = user_id);

-- book_categories 表的RLS策略
CREATE POLICY "用户只能访问自己的类别" ON book_categories
    FOR ALL USING (auth.uid() = user_id);

-- user_configs 表的RLS策略
CREATE POLICY "用户只能访问自己的配置" ON user_configs
    FOR ALL USING (auth.uid() = user_id);

-- book_accounts 表的RLS策略
CREATE POLICY "用户只能访问自己的账户" ON book_accounts
    FOR ALL USING (auth.uid() = user_id);

-- =============================================
-- 触发器：自动更新 updated_at 字段
-- =============================================

-- 创建更新时间戳的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为各表添加更新时间戳触发器
CREATE TRIGGER update_user_books_updated_at 
    BEFORE UPDATE ON user_books 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_categories_updated_at 
    BEFORE UPDATE ON book_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_configs_updated_at 
    BEFORE UPDATE ON user_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_accounts_updated_at 
    BEFORE UPDATE ON book_accounts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- 默认数据插入函数
-- =============================================

-- 创建用户默认账本的函数
CREATE OR REPLACE FUNCTION create_default_book_for_user(p_user_id UUID)
RETURNS UUID AS $$
DECLARE
    v_book_id UUID;
BEGIN
    -- 插入默认账本
    INSERT INTO user_books (user_id, name, description, is_default, is_system, sort_order)
    VALUES (p_user_id, '我的账本', '系统默认创建的账本', TRUE, TRUE, 0)
    RETURNING id INTO v_book_id;
    
    RETURN v_book_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建默认类别的函数
CREATE OR REPLACE FUNCTION create_default_categories_for_book(p_user_id UUID, p_book_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER := 0;
    v_current_time BIGINT := EXTRACT(EPOCH FROM NOW()) * 1000;
BEGIN
    -- 插入默认支出类别
    INSERT INTO book_categories (user_id, book_id, name, type, icon_name, color, sort_order, is_system) VALUES
    (p_user_id, p_book_id, '食品餐饮', 'spending', 'chopsticksFork1', '#FF5722', v_current_time - 1, TRUE),
    (p_user_id, p_book_id, '购物消费', 'spending', 'shopping1', '#E91E63', v_current_time - 2, TRUE),
    (p_user_id, p_book_id, '交通出行', 'spending', 'taxi1', '#2196F3', v_current_time - 3, TRUE),
    (p_user_id, p_book_id, '娱乐休闲', 'spending', 'gamePad1', '#9C27B0', v_current_time - 4, TRUE),
    (p_user_id, p_book_id, '医疗健康', 'spending', 'firstAidKit1', '#F44336', v_current_time - 5, TRUE),
    (p_user_id, p_book_id, '居住缴费', 'spending', 'house1', '#795548', v_current_time - 6, TRUE);
    
    v_count := v_count + 6;
    
    -- 插入默认收入类别
    INSERT INTO book_categories (user_id, book_id, name, type, icon_name, color, sort_order, is_system) VALUES
    (p_user_id, p_book_id, '工资', 'income', 'wage1', '#4CAF50', v_current_time - 7, TRUE),
    (p_user_id, p_book_id, '奖金', 'income', 'bonus1', '#8BC34A', v_current_time - 8, TRUE),
    (p_user_id, p_book_id, '投资收益', 'income', 'invest1', '#00BCD4', v_current_time - 9, TRUE),
    (p_user_id, p_book_id, '其他收入', 'income', 'money1', '#009688', v_current_time - 10, TRUE);
    
    v_count := v_count + 4;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建默认账户的函数
CREATE OR REPLACE FUNCTION create_default_account_for_book(p_user_id UUID, p_book_id UUID)
RETURNS UUID AS $$
DECLARE
    v_account_id UUID;
BEGIN
    -- 插入默认现金账户
    INSERT INTO book_accounts (user_id, book_id, name, type, icon_name, color, balance_init, balance_current, is_default, sort_order)
    VALUES (p_user_id, p_book_id, '现金', 'cash', 'cash1', '#4CAF50', 0, 0, TRUE, 0)
    RETURNING id INTO v_account_id;
    
    RETURN v_account_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 用户注册后自动创建默认数据的函数
CREATE OR REPLACE FUNCTION initialize_user_data()
RETURNS TRIGGER AS $$
DECLARE
    v_book_id UUID;
    v_account_id UUID;
    v_category_count INTEGER;
BEGIN
    -- 创建默认账本
    v_book_id := create_default_book_for_user(NEW.id);
    
    -- 创建默认类别
    v_category_count := create_default_categories_for_book(NEW.id, v_book_id);
    
    -- 创建默认账户
    v_account_id := create_default_account_for_book(NEW.id, v_book_id);
    
    -- 插入用户配置
    INSERT INTO user_configs (user_id, config_key, config_value, config_type, description) VALUES
    (NEW.id, 'default_book_id', v_book_id::TEXT, 'string', '默认选中的账本ID'),
    (NEW.id, 'currency', 'CNY', 'string', '默认货币单位'),
    (NEW.id, 'theme', 'system', 'string', '主题设置'),
    (NEW.id, 'show_assets_tab', 'true', 'boolean', '是否显示资产标签页');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建用户注册触发器
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION initialize_user_data();

-- =============================================
-- 实用查询视图
-- =============================================

-- 用户账本概览视图
CREATE OR REPLACE VIEW user_books_overview AS
SELECT 
    ub.id,
    ub.user_id,
    ub.name,
    ub.description,
    ub.icon_name,
    ub.color,
    ub.is_default,
    ub.is_system,
    ub.sort_order,
    ub.created_at,
    ub.updated_at,
    COUNT(DISTINCT bc.id) as category_count,
    COUNT(DISTINCT ba.id) as account_count
FROM user_books ub
LEFT JOIN book_categories bc ON ub.id = bc.book_id AND bc.is_deleted = FALSE
LEFT JOIN book_accounts ba ON ub.id = ba.book_id AND ba.is_deleted = FALSE
GROUP BY ub.id, ub.user_id, ub.name, ub.description, ub.icon_name, ub.color, 
         ub.is_default, ub.is_system, ub.sort_order, ub.created_at, ub.updated_at;

-- 账本类别树形结构视图
CREATE OR REPLACE VIEW book_categories_tree AS
WITH RECURSIVE category_tree AS (
    -- 根节点（父类别）
    SELECT 
        id, user_id, book_id, parent_id, name, type, icon_name, color,
        sort_order, is_system, is_deleted, created_at, updated_at,
        0 as level,
        ARRAY[sort_order] as sort_path,
        name as full_path
    FROM book_categories 
    WHERE parent_id IS NULL AND is_deleted = FALSE
    
    UNION ALL
    
    -- 子节点
    SELECT 
        bc.id, bc.user_id, bc.book_id, bc.parent_id, bc.name, bc.type, bc.icon_name, bc.color,
        bc.sort_order, bc.is_system, bc.is_deleted, bc.created_at, bc.updated_at,
        ct.level + 1,
        ct.sort_path || bc.sort_order,
        ct.full_path || ' > ' || bc.name
    FROM book_categories bc
    INNER JOIN category_tree ct ON bc.parent_id = ct.id
    WHERE bc.is_deleted = FALSE
)
SELECT * FROM category_tree ORDER BY sort_path;

-- =============================================
-- 数据完整性检查函数
-- =============================================

-- 检查用户是否有默认账本
CREATE OR REPLACE FUNCTION check_user_has_default_book(p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM user_books 
        WHERE user_id = p_user_id AND is_default = TRUE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查账本是否有默认账户
CREATE OR REPLACE FUNCTION check_book_has_default_account(p_book_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM book_accounts 
        WHERE book_id = p_book_id AND is_default = TRUE AND is_deleted = FALSE
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 注释说明
-- =============================================

COMMENT ON TABLE user_books IS '用户账本表，存储用户创建的各个账本信息';
COMMENT ON TABLE book_categories IS '账本类别表，存储每个账本下的收支类别';
COMMENT ON TABLE user_configs IS '用户配置表，存储用户的个性化设置';
COMMENT ON TABLE book_accounts IS '账本账户表，存储每个账本下的资金账户';

COMMENT ON COLUMN user_books.is_default IS '是否为用户的默认账本';
COMMENT ON COLUMN user_books.is_system IS '是否为系统创建的账本';
COMMENT ON COLUMN book_categories.type IS '类别类型：income(收入)、spending(支出)、transfer(转账)';
COMMENT ON COLUMN book_categories.parent_id IS '父类别ID，用于构建类别层级结构';
COMMENT ON COLUMN book_accounts.balance_init IS '账户初始余额，以分为单位';
COMMENT ON COLUMN book_accounts.balance_current IS '账户当前余额，以分为单位';
COMMENT ON COLUMN book_accounts.is_excluded IS '是否在统计中排除此账户';