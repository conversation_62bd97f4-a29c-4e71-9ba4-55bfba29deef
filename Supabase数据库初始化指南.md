# Supabase 数据库初始化指南

## 概述

本指南将帮助您在 Supabase 项目中设置记账应用所需的数据库表结构和相关配置。

## 前置条件

1. 已创建 Supabase 项目
2. 具有项目的数据库访问权限
3. 已启用 Supabase Auth 功能

## 初始化步骤

### 1. 执行数据库脚本

在 Supabase Dashboard 的 SQL Editor 中执行 `supabase_database_schema.sql` 文件中的所有 SQL 语句。

**重要提示：** 请按顺序执行，确保所有表、索引、触发器和函数都正确创建。

### 2. 验证表结构

执行以下查询验证表是否正确创建：

```sql
-- 检查所有表是否存在
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_books', 'book_categories', 'user_configs', 'book_accounts');

-- 检查行级安全策略是否启用
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename IN ('user_books', 'book_categories', 'user_configs', 'book_accounts');
```

### 3. 测试默认数据创建

创建一个测试用户并验证默认数据是否自动创建：

```sql
-- 查看用户的默认账本
SELECT * FROM user_books WHERE user_id = 'your-test-user-id';

-- 查看默认类别
SELECT * FROM book_categories WHERE user_id = 'your-test-user-id';

-- 查看默认账户
SELECT * FROM book_accounts WHERE user_id = 'your-test-user-id';

-- 查看用户配置
SELECT * FROM user_configs WHERE user_id = 'your-test-user-id';
```

## 数据库表结构说明

### 1. user_books (用户账本表)

存储用户创建的各个账本信息。

**主要字段：**
- `id`: 账本唯一标识
- `user_id`: 用户ID（关联auth.users）
- `name`: 账本名称
- `is_default`: 是否为默认账本
- `is_system`: 是否为系统创建

### 2. book_categories (账本类别表)

存储每个账本下的收支类别，支持层级结构。

**主要字段：**
- `id`: 类别唯一标识
- `book_id`: 所属账本ID
- `parent_id`: 父类别ID（支持层级）
- `type`: 类别类型（income/spending/transfer）
- `name`: 类别名称

### 3. book_accounts (账本账户表)

存储每个账本下的资金账户。

**主要字段：**
- `id`: 账户唯一标识
- `book_id`: 所属账本ID
- `name`: 账户名称
- `type`: 账户类型（cash/bank/credit等）
- `balance_init`: 初始余额（以分为单位）
- `balance_current`: 当前余额（以分为单位）

### 4. user_configs (用户配置表)

存储用户的个性化设置。

**主要字段：**
- `config_key`: 配置键
- `config_value`: 配置值
- `config_type`: 配置类型（string/number/boolean/json）

## 安全策略

### 行级安全策略 (RLS)

所有表都启用了行级安全策略，确保：
- 用户只能访问自己的数据
- 数据隔离和安全性

### 权限控制

- 使用 Supabase Auth 进行用户认证
- 通过 `auth.uid()` 函数获取当前用户ID
- 所有数据操作都基于用户ID进行权限验证

## 默认数据

### 新用户注册时自动创建：

1. **默认账本**: "我的账本"
2. **默认类别**:
   - 支出类别：食品餐饮、购物消费、交通出行、娱乐休闲、医疗健康、居住缴费
   - 收入类别：工资、奖金、投资收益、其他收入
3. **默认账户**: 现金账户
4. **用户配置**: 默认账本ID、货币单位、主题设置等

## 实用查询

### 获取用户的所有账本及统计信息

```sql
SELECT * FROM user_books_overview WHERE user_id = auth.uid();
```

### 获取账本的类别树形结构

```sql
SELECT * FROM book_categories_tree 
WHERE user_id = auth.uid() AND book_id = 'your-book-id';
```

### 检查数据完整性

```sql
-- 检查用户是否有默认账本
SELECT check_user_has_default_book(auth.uid());

-- 检查账本是否有默认账户
SELECT check_book_has_default_account('your-book-id');
```

## API 使用示例

### JavaScript/TypeScript 示例

```typescript
import { createClient } from '@supabase/supabase-js'

const supabase = createClient('your-project-url', 'your-anon-key')

// 获取用户账本
const { data: books, error } = await supabase
  .from('user_books')
  .select('*')
  .order('sort_order')

// 获取账本类别
const { data: categories, error } = await supabase
  .from('book_categories')
  .select('*')
  .eq('book_id', bookId)
  .eq('is_deleted', false)
  .order('sort_order')

// 创建新账本
const { data, error } = await supabase
  .from('user_books')
  .insert([
    {
      name: '新账本',
      description: '我的新账本',
      icon_name: 'book2',
      color: '#2196F3'
    }
  ])
```

## 故障排除

### 常见问题

1. **RLS 策略问题**: 确保用户已正确认证，`auth.uid()` 返回有效值
2. **外键约束错误**: 检查关联的用户ID和账本ID是否存在
3. **触发器未执行**: 验证触发器函数是否正确创建

### 调试查询

```sql
-- 检查当前用户ID
SELECT auth.uid();

-- 检查用户的认证状态
SELECT auth.role();

-- 查看表的RLS策略
SELECT * FROM pg_policies WHERE tablename = 'user_books';
```

## 维护建议

1. **定期备份**: 设置自动备份策略
2. **性能监控**: 监控查询性能，必要时添加索引
3. **数据清理**: 定期清理软删除的数据
4. **安全审计**: 定期检查RLS策略和权限设置

## 更新日志

- **v1.0**: 初始版本，包含基础表结构和安全策略
- 支持账本、类别、账户、配置的完整管理
- 自动创建默认数据功能
- 完整的行级安全策略

---

如有问题，请参考 Supabase 官方文档或联系技术支持。