-- Supabase 类别图标修复脚本
-- 修复"其他支出"类别的图标名称从 'other1' 到 'more1'
-- 作者: Claude 4.0 sonnet 🐾

-- 1. 修复"其他支出"类别的图标
UPDATE book_categories 
SET 
    icon_name = 'more1',
    updated_at = NOW()
WHERE 
    name = '其他支出' 
    AND type = 'spending'
    AND icon_name = 'other1'
    AND is_deleted = false;

-- 2. 修复其他可能的图标问题（如果存在）
UPDATE book_categories 
SET 
    icon_name = 'taxi1',
    updated_at = NOW()
WHERE 
    name = '出行交通' 
    AND type = 'spending'
    AND icon_name = 'car1'
    AND is_deleted = false;

UPDATE book_categories 
SET 
    icon_name = 'gamePad1',
    updated_at = NOW()
WHERE 
    name = '休闲娱乐' 
    AND type = 'spending'
    AND icon_name = 'gamepad1'
    AND is_deleted = false;

UPDATE book_categories 
SET 
    icon_name = 'firstAidKit1',
    updated_at = NOW()
WHERE 
    name = '健康医疗' 
    AND type = 'spending'
    AND icon_name = 'medical1'
    AND is_deleted = false;

UPDATE book_categories 
SET 
    icon_name = 'invest1',
    updated_at = NOW()
WHERE 
    name = '投资收益' 
    AND type = 'income'
    AND icon_name = 'investment1'
    AND is_deleted = false;

-- 3. 验证修复结果
SELECT 
    name,
    type,
    icon_name,
    updated_at
FROM book_categories 
WHERE 
    name IN ('其他支出', '出行交通', '休闲娱乐', '健康医疗', '投资收益')
    AND is_deleted = false
ORDER BY name;
