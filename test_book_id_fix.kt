import com.xiaojinzi.tally.lib.res.model.tally.TallyBookModel
import com.xiaojinzi.tally.lib.res.model.tally.TallyCategoryModel
import com.xiaojinzi.tally.lib.res.model.tally.TallyFirstSyncModel

/**
 * 测试账本ID修复效果
 */
fun main() {
    val testUserId = "test-user-123"
    
    println("=== 测试账本ID修复效果 ===")
    
    // 测试账本创建
    val book = TallyBookModel.createForOpenSourceWithUserId(testUserId)
    println("账本ID: ${book.id}")
    println("预期格式: opensourceBook_$testUserId")
    println("是否正确: ${book.id == "opensourceBook_$testUserId"}")
    
    // 测试类别创建
    val category = TallyCategoryModel.createForOpenSourceWithUserId(
        userId = testUserId,
        name = "测试类别",
        type = 1
    )
    println("\n类别账本ID: ${category.bookId}")
    println("预期格式: opensourceBook_$testUserId")
    println("是否正确: ${category.bookId == "opensourceBook_$testUserId"}")
    
    // 测试首次同步数据
    val syncData = TallyFirstSyncModel.createForOpenSourceWithUserId(testUserId)
    println("\n同步数据账本ID: ${syncData.bookList.firstOrNull()?.id}")
    println("预期格式: opensourceBook_$testUserId")
    println("是否正确: ${syncData.bookList.firstOrNull()?.id == "opensourceBook_$testUserId"}")
    
    println("\n=== 测试完成 ===")
}