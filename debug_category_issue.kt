/**
 * 类别显示问题诊断脚本
 * 问题：Supabase同步50个类别到本地，但APP界面只显示11个
 */

import com.xiaojinzi.tally.module.datasource.storage.db.TallyDb
import com.xiaojinzi.tally.module.base.support.AppServices
import kotlinx.coroutines.flow.firstOrNull

/**
 * 诊断类别数据的bookId分布情况
 */
suspend fun debugCategoryBookIdDistribution() {
    println("\n========== 类别数据BookId分布诊断 ==========")
    
    // 获取所有类别数据
    val allCategories = TallyDb.database.categoryDao().getAll()
    println("数据库中总类别数量: ${allCategories.size}")
    
    // 按bookId分组统计
    val categoryByBookId = allCategories.groupBy { it.bookId }
    println("\n类别数据按账本分布:")
    categoryByBookId.forEach { (bookId, categories) ->
        println("  账本ID: $bookId")
        println("    类别数量: ${categories.size}")
        println("    已删除数量: ${categories.count { it.isDeleted }}")
        println("    未删除数量: ${categories.count { !it.isDeleted }}")
        
        // 显示前3个类别名称
        val sampleCategories = categories.take(3)
        sampleCategories.forEach { category ->
            println("      - ${category.name}(${category.type}) deleted=${category.isDeleted}")
        }
        println()
    }
    
    // 获取当前选中账本
    val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
    if (selectedBook != null) {
        println("当前选中账本: ${selectedBook.name}(${selectedBook.id})")
        
        val selectedBookCategories = categoryByBookId[selectedBook.id] ?: emptyList()
        println("选中账本的类别数量: ${selectedBookCategories.size}")
        println("选中账本未删除类别数量: ${selectedBookCategories.count { !it.isDeleted }}")
    } else {
        println("❌ 未选中任何账本")
    }
}

/**
 * 诊断类别数据分布
 */
suspend fun debugCategoryDataDistribution() {
    println("\n========== 类别数据分布诊断 ==========")
    
    // 查询所有类别数据
    val allCategories = TallyDb.database.categoryDao().getAll()
    println("数据库中总类别数量: ${allCategories.size}")
    
    // 按类型分组
    val spendingCategories = allCategories.filter { it.type == "SPENDING" }
    val incomeCategories = allCategories.filter { it.type == "INCOME" }
    
    println("支出类别数量: ${spendingCategories.size}")
    println("收入类别数量: ${incomeCategories.size}")
    
    // 按删除状态分组
    val deletedCategories = allCategories.filter { it.isDeleted }
    val activeCategoriesCount = allCategories.count { !it.isDeleted }
    
    println("已删除类别数量: ${deletedCategories.size}")
    println("未删除类别数量: $activeCategoriesCount")
}

/**
 * 诊断CategorySelect数据流
 */
suspend fun debugCategorySelectDataFlow() {
    println("\n========== CategorySelect数据流诊断 ==========")
    
    // 模拟CategorySelectUseCase的数据获取逻辑
    val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
    if (selectedBook == null) {
        println("❌ 未选中任何账本，这可能是问题根源")
        return
    }
    
    println("当前选中账本: ${selectedBook.name}(${selectedBook.id})")
    
    // 通过subscribeAllCategory获取类别数据
    val allCategoryList = AppServices.tallyDataSourceSpi.subscribeAllCategory(
        bookIdList = listOf(selectedBook.id)
    ).firstOrNull() ?: emptyList()
    
    println("通过subscribeAllCategory获取的类别数量: ${allCategoryList.size}")
    
    // 过滤未删除的类别
    val activeCategories = allCategoryList.filter { !it.isDeleted }
    println("过滤未删除后的类别数量: ${activeCategories.size}")
    
    // 按类型分组
    val spendingCategories = activeCategories.filter { it.type == "SPENDING" }
    val incomeCategories = activeCategories.filter { it.type == "INCOME" }
    
    println("支出类别数量: ${spendingCategories.size}")
    println("收入类别数量: ${incomeCategories.size}")
    
    // 按父类别分组
    val parentCategories = activeCategories.filter { it.parentId == null }
    val subCategories = activeCategories.filter { it.parentId != null }
    
    println("父类别数量: ${parentCategories.size}")
    println("子类别数量: ${subCategories.size}")
    
    // 显示前5个类别
    println("前5个类别:")
    activeCategories.take(5).forEach { category ->
        println("  - ${category.name}(${category.type}) parentId=${category.parentId}")
    }
}

/**
 * 检查Supabase同步的类别数据
 */
suspend fun debugSupabaseSyncedCategories() {
    println("\n========== Supabase同步类别数据检查 ==========")
    
    // 获取所有类别数据
    val allCategories = TallyDb.database.categoryDao().getAll()
    
    // 按创建时间排序，最新的在前面（可能是Supabase同步的）
    val sortedByCreateTime = allCategories.sortedByDescending { it.timeCreate }
    
    println("按创建时间排序的最新50个类别:")
    sortedByCreateTime.take(50).forEachIndexed { index, category ->
        val createTime = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.CHINESE)
            .format(java.util.Date(category.timeCreate))
        println("  ${index + 1}. ${category.name}(${category.type}) bookId=${category.bookId} 创建时间=$createTime")
    }
}

/**
 * 主诊断函数
 */
suspend fun diagnoseCategoryIssue() {
    println("开始诊断类别显示问题...")
    println("问题描述: Supabase同步50个类别到本地，但APP界面只显示11个")
    
    // 获取当前选中账本信息
    val selectedBook = AppServices.tallyDataSourceSpi.selectedBookStateOb.firstOrNull()
    if (selectedBook != null) {
        println("当前选中账本: ${selectedBook.name}(${selectedBook.id})")
    } else {
        println("❌ 未选中任何账本")
        return
    }
    
    // 获取数据库中总类别数量
    val allCategories = TallyDb.database.categoryDao().getAll()
    println("数据库中总类别数量: ${allCategories.size}")
    
    // 通过subscribeAllCategory获取类别数据（模拟CategorySelectUseCase的逻辑）
    val categoryList = AppServices.tallyDataSourceSpi.subscribeAllCategory(
        bookIdList = listOf(selectedBook.id)
    ).firstOrNull() ?: emptyList()
    
    println("通过subscribeAllCategory获取的类别数量: ${categoryList.size}")
    
    // 过滤未删除的类别
    val activeCategories = categoryList.filter { !it.isDeleted }
    println("过滤未删除后的类别数量: ${activeCategories.size}")
    
    // 按父类别分组
    val parentCategories = activeCategories.filter { it.parentId == null }
    println("父类别数量: ${parentCategories.size}")
    
    // 显示前5个类别
    println("前5个类别:")
    activeCategories.take(5).forEach { category ->
        println("  - ${category.name}(${category.type}) parentId=${category.parentId}")
    }
    
    // 执行详细诊断
    debugCategoryBookIdDistribution()
    debugSupabaseSyncedCategories()
}