---
inclusion: manual
---

# 测试策略指导

本文档定义了 yike-app 项目的测试策略、规范和最佳实践。

## 测试金字塔策略

```
        /\
       /  \
      / UI \     <- 少量 UI 测试
     /______\
    /        \
   /Integration\ <- 适量集成测试  
  /__________\
 /            \
/  Unit Tests  \ <- 大量单元测试
/______________\
```

### 测试比例建议
- **单元测试**: 70% - 测试业务逻辑、工具类、数据处理
- **集成测试**: 20% - 测试模块间交互、数据库操作、网络请求
- **UI 测试**: 10% - 测试关键用户流程、界面交互

## 单元测试规范

### ViewModel 测试

```kotlin
@ExtendWith(MockitoExtension::class)
class BillListViewModelTest {
    
    @Mock
    private lateinit var getBillListUseCase: GetBillListUseCase
    
    @Mock
    private lateinit var deleteBillUseCase: DeleteBillUseCase
    
    private lateinit var viewModel: BillListViewModel
    
    @BeforeEach
    fun setup() {
        viewModel = BillListViewModel(getBillListUseCase, deleteBillUseCase)
    }
    
    @Test
    fun `loadBillList should update uiState with bills when successful`() = runTest {
        // Given
        val expectedBills = listOf(
            createTestBill(id = 1L, amount = BigDecimal("100.00")),
            createTestBill(id = 2L, amount = BigDecimal("200.00"))
        )
        whenever(getBillListUseCase()).thenReturn(Result.success(expectedBills))
        
        // When
        viewModel.loadBillList()
        
        // Then
        val uiState = viewModel.uiState.value
        assertThat(uiState.bills).isEqualTo(expectedBills)
        assertThat(uiState.isLoading).isFalse()
        assertThat(uiState.error).isNull()
    }
    
    @Test
    fun `loadBillList should update uiState with error when failed`() = runTest {
        // Given
        val expectedError = "网络错误"
        whenever(getBillListUseCase()).thenReturn(Result.failure(Exception(expectedError)))
        
        // When
        viewModel.loadBillList()
        
        // Then
        val uiState = viewModel.uiState.value
        assertThat(uiState.bills).isEmpty()
        assertThat(uiState.isLoading).isFalse()
        assertThat(uiState.error).isEqualTo(expectedError)
    }
}
```

### Repository 测试

```kotlin
@ExtendWith(MockitoExtension::class)
class BillRepositoryImplTest {
    
    @Mock
    private lateinit var localDataSource: BillLocalDataSource
    
    @Mock
    private lateinit var remoteDataSource: BillRemoteDataSource
    
    @Mock
    private lateinit var networkManager: INetworkManager
    
    private lateinit var repository: BillRepositoryImpl
    
    @BeforeEach
    fun setup() {
        repository = BillRepositoryImpl(localDataSource, remoteDataSource, networkManager)
    }
    
    @Test
    fun `getBillList should return remote data when network available`() = runTest {
        // Given
        val remoteBills = listOf(createTestBill(id = 1L))
        whenever(networkManager.isConnected()).thenReturn(true)
        whenever(remoteDataSource.getBillList()).thenReturn(remoteBills)
        
        // When
        val result = repository.getBillList()
        
        // Then
        assertThat(result).isEqualTo(remoteBills)
        verify(localDataSource).saveBills(remoteBills)
    }
    
    @Test
    fun `getBillList should return local data when network unavailable`() = runTest {
        // Given
        val localBills = listOf(createTestBill(id = 2L))
        whenever(networkManager.isConnected()).thenReturn(false)
        whenever(localDataSource.getBillList()).thenReturn(localBills)
        
        // When
        val result = repository.getBillList()
        
        // Then
        assertThat(result).isEqualTo(localBills)
        verify(remoteDataSource, never()).getBillList()
    }
}
```

### UseCase 测试

```kotlin
@ExtendWith(MockitoExtension::class)
class CalculateTotalAmountUseCaseTest {
    
    @Mock
    private lateinit var billRepository: IBillRepository
    
    private lateinit var useCase: CalculateTotalAmountUseCase
    
    @BeforeEach
    fun setup() {
        useCase = CalculateTotalAmountUseCase(billRepository)
    }
    
    @Test
    fun `invoke should return correct total amount`() = runTest {
        // Given
        val bills = listOf(
            createTestBill(amount = BigDecimal("100.00"), type = BillType.INCOME),
            createTestBill(amount = BigDecimal("50.00"), type = BillType.EXPENSE),
            createTestBill(amount = BigDecimal("200.00"), type = BillType.INCOME)
        )
        whenever(billRepository.getBillList()).thenReturn(bills)
        
        // When
        val result = useCase()
        
        // Then
        assertThat(result).isEqualTo(BigDecimal("250.00")) // 100 - 50 + 200
    }
}
```

## 集成测试规范

### 数据库测试

```kotlin
@RunWith(AndroidJUnit4::class)
@SmallTest
class BillDaoTest {
    
    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()
    
    private lateinit var database: YikeDatabase
    private lateinit var billDao: BillDao
    
    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            YikeDatabase::class.java
        ).allowMainThreadQueries().build()
        
        billDao = database.billDao()
    }
    
    @After
    fun teardown() {
        database.close()
    }
    
    @Test
    fun insertAndGetBill() = runTest {
        // Given
        val bill = createTestBillEntity(id = 1L, amount = BigDecimal("100.00"))
        
        // When
        billDao.insert(bill)
        val retrievedBill = billDao.getBillById(1L)
        
        // Then
        assertThat(retrievedBill).isNotNull()
        assertThat(retrievedBill?.amount).isEqualTo(BigDecimal("100.00"))
    }
    
    @Test
    fun getBillsByDateRange() = runTest {
        // Given
        val startDate = LocalDateTime.of(2024, 1, 1, 0, 0)
        val endDate = LocalDateTime.of(2024, 1, 31, 23, 59)
        val billsInRange = listOf(
            createTestBillEntity(id = 1L, createTime = LocalDateTime.of(2024, 1, 15, 10, 0)),
            createTestBillEntity(id = 2L, createTime = LocalDateTime.of(2024, 1, 20, 15, 30))
        )
        val billOutOfRange = createTestBillEntity(id = 3L, createTime = LocalDateTime.of(2024, 2, 1, 9, 0))
        
        billDao.insertAll(billsInRange + billOutOfRange)
        
        // When
        val result = billDao.getBillsByDateRange(startDate, endDate)
        
        // Then
        assertThat(result).hasSize(2)
        assertThat(result.map { it.id }).containsExactly(1L, 2L)
    }
}
```

### 网络测试

```kotlin
@RunWith(AndroidJUnit4::class)
class BillApiServiceTest {
    
    private lateinit var mockWebServer: MockWebServer
    private lateinit var apiService: BillApiService
    
    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        val retrofit = Retrofit.Builder()
            .baseUrl(mockWebServer.url("/"))
            .addConverterFactory(GsonConverterFactory.create())
            .build()
            
        apiService = retrofit.create(BillApiService::class.java)
    }
    
    @After
    fun teardown() {
        mockWebServer.shutdown()
    }
    
    @Test
    fun getBillList_returnsSuccessfulResponse() = runTest {
        // Given
        val mockResponse = """
            {
                "code": 200,
                "data": [
                    {
                        "id": 1,
                        "amount": "100.00",
                        "description": "测试账单",
                        "createTime": "2024-01-15T10:00:00"
                    }
                ]
            }
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(mockResponse)
        )
        
        // When
        val response = apiService.getBillList()
        
        // Then
        assertThat(response.isSuccessful).isTrue()
        assertThat(response.body()?.data).hasSize(1)
        assertThat(response.body()?.data?.first()?.amount).isEqualTo("100.00")
    }
}
```

## UI 测试规范

### Compose UI 测试

```kotlin
@RunWith(AndroidJUnit4::class)
class BillListScreenTest {
    
    @get:Rule
    val composeTestRule = createComposeRule()
    
    @Test
    fun billListScreen_displaysEmptyState_whenNoBills() {
        // Given
        val emptyUiState = BillListUiState(
            bills = emptyList(),
            isLoading = false
        )
        
        // When
        composeTestRule.setContent {
            BillListScreen(uiState = emptyUiState)
        }
        
        // Then
        composeTestRule
            .onNodeWithText("暂无账单记录")
            .assertIsDisplayed()
    }
    
    @Test
    fun billListScreen_displaysBills_whenDataAvailable() {
        // Given
        val bills = listOf(
            createTestBill(id = 1L, description = "午餐", amount = BigDecimal("25.50")),
            createTestBill(id = 2L, description = "交通", amount = BigDecimal("10.00"))
        )
        val uiState = BillListUiState(bills = bills, isLoading = false)
        
        // When
        composeTestRule.setContent {
            BillListScreen(uiState = uiState)
        }
        
        // Then
        composeTestRule
            .onNodeWithText("午餐")
            .assertIsDisplayed()
            
        composeTestRule
            .onNodeWithText("25.50")
            .assertIsDisplayed()
            
        composeTestRule
            .onNodeWithText("交通")
            .assertIsDisplayed()
    }
    
    @Test
    fun billListScreen_navigatesToAddBill_whenFabClicked() {
        // Given
        var addBillClicked = false
        val uiState = BillListUiState()
        
        // When
        composeTestRule.setContent {
            BillListScreen(
                uiState = uiState,
                onAddBillClick = { addBillClicked = true }
            )
        }
        
        composeTestRule
            .onNodeWithContentDescription("添加账单")
            .performClick()
        
        // Then
        assertThat(addBillClicked).isTrue()
    }
}
```

### 端到端测试

```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class BillE2ETest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun addBill_completesSuccessfully() {
        // 导航到添加账单页面
        onView(withId(R.id.fab_add_bill))
            .perform(click())
        
        // 输入账单信息
        onView(withId(R.id.et_amount))
            .perform(typeText("100.00"))
            
        onView(withId(R.id.et_description))
            .perform(typeText("测试账单"))
        
        // 保存账单
        onView(withId(R.id.btn_save))
            .perform(click())
        
        // 验证返回到列表页面并显示新账单
        onView(withText("测试账单"))
            .check(matches(isDisplayed()))
            
        onView(withText("100.00"))
            .check(matches(isDisplayed()))
    }
}
```

## 测试数据管理

### 测试数据工厂

```kotlin
object TestDataFactory {
    
    fun createTestBill(
        id: Long = 1L,
        amount: BigDecimal = BigDecimal("100.00"),
        description: String = "测试账单",
        type: BillType = BillType.EXPENSE,
        createTime: LocalDateTime = LocalDateTime.now()
    ): Bill {
        return Bill(
            id = id,
            amount = amount,
            description = description,
            type = type,
            createTime = createTime
        )
    }
    
    fun createTestBillEntity(
        id: Long = 1L,
        amount: BigDecimal = BigDecimal("100.00"),
        description: String = "测试账单",
        type: BillType = BillType.EXPENSE,
        createTime: LocalDateTime = LocalDateTime.now()
    ): BillEntity {
        return BillEntity(
            id = id,
            amount = amount,
            description = description,
            type = type,
            createTime = createTime
        )
    }
    
    fun createTestUser(
        id: Long = 1L,
        username: String = "testuser",
        email: String = "<EMAIL>"
    ): User {
        return User(
            id = id,
            username = username,
            email = email
        )
    }
}
```

### Mock 数据管理

```kotlin
class FakeBillRepository : IBillRepository {
    
    private val bills = mutableListOf<Bill>()
    private var shouldReturnError = false
    
    fun setShouldReturnError(shouldReturn: Boolean) {
        shouldReturnError = shouldReturn
    }
    
    override suspend fun getBillList(): List<Bill> {
        if (shouldReturnError) {
            throw Exception("测试错误")
        }
        return bills.toList()
    }
    
    override suspend fun saveBill(bill: Bill): Result<Unit> {
        if (shouldReturnError) {
            return Result.failure(Exception("保存失败"))
        }
        bills.add(bill)
        return Result.success(Unit)
    }
    
    override suspend fun deleteBill(billId: Long): Result<Unit> {
        if (shouldReturnError) {
            return Result.failure(Exception("删除失败"))
        }
        bills.removeAll { it.id == billId }
        return Result.success(Unit)
    }
    
    fun addTestBill(bill: Bill) {
        bills.add(bill)
    }
    
    fun clearBills() {
        bills.clear()
    }
}
```

## 测试覆盖率要求

### 覆盖率目标
- **整体代码覆盖率**: ≥ 80%
- **业务逻辑层**: ≥ 90%
- **数据层**: ≥ 85%
- **UI 层**: ≥ 60%

### 覆盖率检查

```gradle
// 在 build.gradle.kts 中配置
android {
    buildTypes {
        debug {
            enableUnitTestCoverage = true
            enableAndroidTestCoverage = true
        }
    }
}

// 添加 JaCoCo 插件
apply(plugin = "jacoco")

tasks.register<JacocoReport>("jacocoTestReport") {
    dependsOn("testDebugUnitTest", "createDebugCoverageReport")
    
    reports {
        xml.required.set(true)
        html.required.set(true)
    }
    
    executionData.setFrom(fileTree(buildDir).include(
        "jacoco/testDebugUnitTest.exec",
        "outputs/code_coverage/debugAndroidTest/connected/**/*.ec"
    ))
}
```

## CI/CD 集成

### GitHub Actions 配置

```yaml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        
    - name: Run unit tests
      run: ./gradlew testDebugUnitTest
      
    - name: Run instrumented tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: ./gradlew connectedDebugAndroidTest
        
    - name: Generate test report
      run: ./gradlew jacocoTestReport
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
```

---

遵循这些测试策略将确保 yike-app 的代码质量和稳定性，为用户提供可靠的记账服务。