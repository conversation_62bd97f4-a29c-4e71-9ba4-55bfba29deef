---
inclusion: fileMatch
fileMatchPattern: '**/*Compose*|**/*Screen*|**/*View*|**/ui/**'
---

# Compose UI 开发规范

本文档定义了 guichai-app 项目中 Jetpack Compose UI 开发的最佳实践和规范。

## Compose 版本参考
#[[file:common/libs.versions.toml]]

## Composable 函数设计原则

### 命名规范

```kotlin
// ✅ 正确：使用 PascalCase，名词性
@Composable
fun BillListScreen()

@Composable  
fun BillItemCard()

@Composable
fun AmountInputField()

// ❌ 错误
@Composable
fun billList()

@Composable
fun showBill()
```

### 函数签名规范

```kotlin
// ✅ 正确：参数顺序和默认值
@Composable
fun BillItemCard(
    bill: Bill,
    onClick: (Bill) -> Unit,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    showActions: Boolean = true
) {
    // 实现
}
```

**参数顺序原则：**
1. 必需的数据参数
2. 必需的回调参数  
3. `modifier: Modifier = Modifier`
4. 可选的配置参数（带默认值）

## 状态管理最佳实践

### State Hoisting（状态提升）

```kotlin
// ✅ 正确：状态提升到合适的层级
@Composable
fun BillInputScreen(
    viewModel: BillInputViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    BillInputContent(
        amount = uiState.amount,
        description = uiState.description,
        onAmountChange = viewModel::updateAmount,
        onDescriptionChange = viewModel::updateDescription,
        onSave = viewModel::saveBill
    )
}

@Composable
private fun BillInputContent(
    amount: String,
    description: String,
    onAmountChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onSave: () -> Unit,
    modifier: Modifier = Modifier
) {
    // UI 实现
}
```

### 本地状态管理

```kotlin
// ✅ 正确：使用 remember 管理本地状态
@Composable
fun BillListScreen() {
    var searchQuery by remember { mutableStateOf("") }
    var showFilterDialog by remember { mutableStateOf(false) }
    
    // UI 实现
}

// ✅ 正确：使用 rememberSaveable 保存重要状态
@Composable
fun BillInputScreen() {
    var amount by rememberSaveable { mutableStateOf("") }
    var description by rememberSaveable { mutableStateOf("") }
    
    // UI 实现
}
```

## 副作用处理规范

### LaunchedEffect 使用

```kotlin
// ✅ 正确：处理一次性副作用
@Composable
fun BillDetailScreen(
    billId: Long,
    viewModel: BillDetailViewModel = hiltViewModel()
) {
    LaunchedEffect(billId) {
        viewModel.loadBillDetail(billId)
    }
    
    // UI 实现
}
```

### DisposableEffect 使用

```kotlin
// ✅ 正确：处理需要清理的副作用
@Composable
fun BillListScreen() {
    val lifecycleOwner = LocalLifecycleOwner.current
    
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 刷新数据
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}
```

## 性能优化指导

### 重组优化

```kotlin
// ✅ 正确：使用 derivedStateOf 避免不必要的重组
@Composable
fun BillListScreen(
    bills: List<Bill>,
    searchQuery: String
) {
    val filteredBills by remember(bills, searchQuery) {
        derivedStateOf {
            if (searchQuery.isEmpty()) {
                bills
            } else {
                bills.filter { it.description.contains(searchQuery, ignoreCase = true) }
            }
        }
    }
    
    LazyColumn {
        items(filteredBills, key = { it.id }) { bill ->
            BillItemCard(bill = bill)
        }
    }
}
```

### 稳定性优化

```kotlin
// ✅ 正确：使用 @Stable 注解
@Stable
data class BillUiState(
    val bills: List<Bill> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

// ✅ 正确：使用 @Immutable 注解
@Immutable
data class Bill(
    val id: Long,
    val amount: BigDecimal,
    val description: String,
    val createTime: LocalDateTime
)
```

### 列表性能优化

```kotlin
// ✅ 正确：LazyColumn 性能优化
@Composable
fun BillListScreen(bills: List<Bill>) {
    LazyColumn(
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(
            items = bills,
            key = { bill -> bill.id } // 重要：提供稳定的 key
        ) { bill ->
            BillItemCard(
                bill = bill,
                modifier = Modifier.animateItemPlacement() // 动画优化
            )
        }
    }
}
```

## 主题和样式系统

### 颜色系统

```kotlin
// 定义应用颜色主题
object YikeColors {
    val Primary = Color(0xFF1976D2)
    val PrimaryVariant = Color(0xFF1565C0)
    val Secondary = Color(0xFF03DAC6)
    val Background = Color(0xFFF5F5F5)
    val Surface = Color(0xFFFFFFFF)
    val Error = Color(0xFFB00020)
    
    // 记账应用特定颜色
    val Income = Color(0xFF4CAF50)  // 收入绿色
    val Expense = Color(0xFFF44336) // 支出红色
    val Transfer = Color(0xFF2196F3) // 转账蓝色
}

// 使用颜色
@Composable
fun AmountText(
    amount: BigDecimal,
    type: BillType,
    modifier: Modifier = Modifier
) {
    val color = when (type) {
        BillType.INCOME -> YikeColors.Income
        BillType.EXPENSE -> YikeColors.Expense
        BillType.TRANSFER -> YikeColors.Transfer
    }
    
    Text(
        text = amount.toString(),
        color = color,
        modifier = modifier
    )
}
```

### 字体系统

```kotlin
// 定义字体主题
object YikeTypography {
    val h1 = TextStyle(
        fontSize = 32.sp,
        fontWeight = FontWeight.Bold,
        lineHeight = 40.sp
    )
    
    val body1 = TextStyle(
        fontSize = 16.sp,
        fontWeight = FontWeight.Normal,
        lineHeight = 24.sp
    )
    
    val caption = TextStyle(
        fontSize = 12.sp,
        fontWeight = FontWeight.Normal,
        lineHeight = 16.sp
    )
    
    // 金额专用字体
    val amount = TextStyle(
        fontSize = 24.sp,
        fontWeight = FontWeight.Medium,
        fontFamily = FontFamily.Monospace // 等宽字体便于对齐
    )
}
```

### 尺寸系统

```kotlin
object YikeDimensions {
    val paddingSmall = 8.dp
    val paddingMedium = 16.dp
    val paddingLarge = 24.dp
    
    val cornerRadius = 8.dp
    val cardElevation = 4.dp
    
    val iconSize = 24.dp
    val buttonHeight = 48.dp
}
```

## 组件设计规范

### 可重用组件

```kotlin
// ✅ 正确：设计可重用的组件
@Composable
fun YikeButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    loading: Boolean = false,
    variant: ButtonVariant = ButtonVariant.Primary
) {
    Button(
        onClick = onClick,
        enabled = enabled && !loading,
        modifier = modifier.height(YikeDimensions.buttonHeight),
        colors = ButtonDefaults.buttonColors(
            backgroundColor = when (variant) {
                ButtonVariant.Primary -> YikeColors.Primary
                ButtonVariant.Secondary -> YikeColors.Secondary
            }
        )
    ) {
        if (loading) {
            CircularProgressIndicator(
                modifier = Modifier.size(16.dp),
                color = Color.White
            )
        } else {
            Text(text = text)
        }
    }
}

enum class ButtonVariant {
    Primary, Secondary
}
```

### 输入组件

```kotlin
// ✅ 正确：金额输入组件
@Composable
fun AmountInputField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: String = "金额",
    isError: Boolean = false,
    errorMessage: String? = null
) {
    Column(modifier = modifier) {
        OutlinedTextField(
            value = value,
            onValueChange = { newValue ->
                // 只允许数字和小数点
                if (newValue.matches(Regex("^\\d*\\.?\\d*$"))) {
                    onValueChange(newValue)
                }
            },
            label = { Text(label) },
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Done
            ),
            isError = isError,
            singleLine = true,
            modifier = Modifier.fillMaxWidth()
        )
        
        if (isError && errorMessage != null) {
            Text(
                text = errorMessage,
                color = MaterialTheme.colors.error,
                style = MaterialTheme.typography.caption,
                modifier = Modifier.padding(start = 16.dp, top = 4.dp)
            )
        }
    }
}
```

## 导航和路由

### 使用 KComponent 路由

```kotlin
// ✅ 正确：Compose 中使用路由
@Composable
fun BillListScreen() {
    // ... UI 实现
    
    FloatingActionButton(
        onClick = {
            Router.with(LocalContext.current)
                .hostAndPath("app://bill/add")
                .forward()
        }
    ) {
        Icon(Icons.Default.Add, contentDescription = "添加账单")
    }
}
```

## 测试支持

### Compose 测试

```kotlin
// 测试 Composable 函数
@Test
fun billItemCard_displaysCorrectInfo() {
    val testBill = Bill(
        id = 1L,
        amount = BigDecimal("100.00"),
        description = "测试账单",
        createTime = LocalDateTime.now()
    )
    
    composeTestRule.setContent {
        BillItemCard(
            bill = testBill,
            onClick = { }
        )
    }
    
    composeTestRule
        .onNodeWithText("100.00")
        .assertIsDisplayed()
        
    composeTestRule
        .onNodeWithText("测试账单")
        .assertIsDisplayed()
}
```

## 可访问性指导

### 语义化标签

```kotlin
// ✅ 正确：添加可访问性支持
@Composable
fun BillItemCard(
    bill: Bill,
    onClick: (Bill) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clickable { onClick(bill) }
            .semantics {
                contentDescription = "账单：${bill.description}，金额：${bill.amount}"
                role = Role.Button
            }
    ) {
        // UI 实现
    }
}
```

---

遵循这些 Compose 开发规范将确保 UI 代码的一致性、性能和可维护性，为用户提供流畅的记账体验。