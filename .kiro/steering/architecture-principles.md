# 架构设计原则

本文档定义了 guichai-app 项目的架构设计原则和模块化开发指导。

## 项目模块结构参考
#[[file:common/settings.gradle.kts]]

## 模块化架构原则

### 模块职责划分

基于项目现有结构，各模块职责如下：

```
guichai-app/
├── app/                    # 应用层
│   ├── opensource/         # 开源版本应用
│   ├── domestic/          # 国内版本应用  
│   └── abroad/            # 海外版本应用
├── module-core/           # 核心模块（基础功能）
├── module-user/           # 用户模块
├── module-main/           # 主界面模块
├── module-datasource/     # 数据源模块
├── module-network/        # 网络模块
├── module-base/           # 基础组件模块
├── module-widget/         # UI 组件模块
├── module-image-preview/  # 图片预览模块
├── module-image-picker/   # 图片选择模块
├── module-qrcode/         # 二维码模块
└── lib-res/              # 资源库
```

### 依赖方向规则

遵循严格的依赖方向，避免循环依赖：

```
应用层 (app)
    ↓
功能模块层 (module-*)
    ↓
基础模块层 (module-base, lib-res)
    ↓
核心模块层 (module-core)
```

**禁止的依赖关系：**
- ❌ 基础模块不能依赖功能模块
- ❌ 功能模块之间不能相互依赖（通过接口通信）
- ❌ 核心模块不能依赖任何业务模块

## 分层架构设计

### 三层架构模式

每个功能模块内部采用三层架构：

```kotlin
// UI 层 - 负责界面展示和用户交互
@Composable
fun BillListScreen(
    viewModel: BillListViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    // UI 实现
}

// 业务层 - 负责业务逻辑处理
class BillListViewModel @Inject constructor(
    private val getBillListUseCase: GetBillListUseCase
) : ViewModel() {
    // 业务逻辑实现
}

// 数据层 - 负责数据获取和存储
class BillRepositoryImpl @Inject constructor(
    private val localDataSource: BillLocalDataSource,
    private val remoteDataSource: BillRemoteDataSource
) : IBillRepository {
    // 数据操作实现
}
```

### 数据流向

```
UI Layer → ViewModel → UseCase → Repository → DataSource
    ↑                                              ↓
    ←←←←←←←←←← Data Flow ←←←←←←←←←←←←←←←←←←←←←←←←
```

## xiaojinzi 库使用规范

### KComponent 路由使用

```kotlin
// 路由定义
@RouterAnno(
    hostAndPath = "app://bill/list"
)
class BillListAct : ComponentActivity()

// 路由跳转
Router.with(context)
    .hostAndPath("app://bill/list")
    .putString("userId", userId)
    .forward()
```

### AndroidSupport 库使用

```kotlin
// 使用 ktx 扩展
class BillViewModel : ViewModel() {
    
    // 使用协程扩展
    fun loadBillList() {
        launchIgnoreError {
            val bills = billRepository.getBillList()
            _uiState.value = _uiState.value.copy(bills = bills)
        }
    }
}
```

### AndroidReactive 使用

```kotlin
// 响应式数据流
class BillRepository @Inject constructor() {
    
    private val _billListFlow = MutableSharedFlow<List<Bill>>()
    val billListFlow: SharedFlow<List<Bill>> = _billListFlow.asSharedFlow()
    
    suspend fun refreshBillList() {
        val bills = remoteDataSource.getBillList()
        localDataSource.saveBills(bills)
        _billListFlow.emit(bills)
    }
}
```

## 模块间通信规范

### 接口定义

```kotlin
// 在 module-core 中定义接口
interface IUserService {
    suspend fun getCurrentUser(): User?
    suspend fun login(username: String, password: String): Result<User>
}

// 在 module-user 中实现接口
@Singleton
class UserServiceImpl @Inject constructor() : IUserService {
    override suspend fun getCurrentUser(): User? {
        // 实现逻辑
    }
}
```

### 依赖注入配置

```kotlin
// 在 module-user 的 di 包中
@Module
@InstallIn(SingletonComponent::class)
abstract class UserModule {
    
    @Binds
    abstract fun bindUserService(
        userServiceImpl: UserServiceImpl
    ): IUserService
}
```

## 版本管理策略

### 多版本构建配置

基于项目的 flavor 配置：

```kotlin
// 开发环境配置
productFlavors {
    create("dev") {
        dimension = "env"
        buildConfigField("String", "baseNetworkUrl", "\"http://192.168.199.244:8080/api\"")
    }
    
    create("prd") {
        dimension = "env"  
        buildConfigField("String", "baseNetworkUrl", "\"https://yike.icxj.cn/api\"")
    }
}
```

### 版本特定代码处理

```kotlin
// 使用 BuildConfig 区分环境
class NetworkConfig {
    companion object {
        val BASE_URL = BuildConfig.baseNetworkUrl
        val IS_DEBUG = BuildConfig.DEBUG
    }
}

// 版本特定功能
class FeatureManager {
    fun isFeatureEnabled(feature: String): Boolean {
        return when (BuildConfig.FLAVOR) {
            "opensource" -> openSourceFeatures.contains(feature)
            "domestic" -> domesticFeatures.contains(feature)
            "abroad" -> abroadFeatures.contains(feature)
            else -> false
        }
    }
}
```

## 数据管理原则

### Repository 模式

```kotlin
interface IBillRepository {
    suspend fun getBillList(): List<Bill>
    suspend fun saveBill(bill: Bill): Result<Unit>
    suspend fun deleteBill(billId: Long): Result<Unit>
}

@Singleton
class BillRepositoryImpl @Inject constructor(
    private val localDataSource: BillLocalDataSource,
    private val remoteDataSource: BillRemoteDataSource,
    private val networkManager: INetworkManager
) : IBillRepository {
    
    override suspend fun getBillList(): List<Bill> {
        return if (networkManager.isConnected()) {
            try {
                val remoteBills = remoteDataSource.getBillList()
                localDataSource.saveBills(remoteBills)
                remoteBills
            } catch (e: Exception) {
                localDataSource.getBillList()
            }
        } else {
            localDataSource.getBillList()
        }
    }
}
```

### 数据同步策略

```kotlin
class DataSyncManager @Inject constructor(
    private val billRepository: IBillRepository,
    private val userRepository: IUserRepository
) {
    
    suspend fun syncAllData() {
        coroutineScope {
            launch { billRepository.syncWithRemote() }
            launch { userRepository.syncWithRemote() }
        }
    }
}
```

## 错误处理架构

### 统一错误处理

```kotlin
sealed class AppError : Exception() {
    object NetworkError : AppError()
    object AuthenticationError : AppError()
    data class BusinessError(val code: Int, override val message: String) : AppError()
}

class ErrorHandler @Inject constructor() {
    
    fun handleError(error: Throwable): String {
        return when (error) {
            is AppError.NetworkError -> "网络连接失败，请检查网络设置"
            is AppError.AuthenticationError -> "登录已过期，请重新登录"
            is AppError.BusinessError -> error.message
            else -> "未知错误，请稍后重试"
        }
    }
}
```

## 测试架构支持

### 依赖注入测试配置

```kotlin
@Module
@TestInstallIn(
    components = [SingletonComponent::class],
    replaces = [DataModule::class]
)
object TestDataModule {
    
    @Provides
    @Singleton
    fun provideTestBillRepository(): IBillRepository {
        return FakeBillRepository()
    }
}
```

### 测试数据管理

```kotlin
class FakeBillRepository : IBillRepository {
    private val bills = mutableListOf<Bill>()
    
    override suspend fun getBillList(): List<Bill> = bills.toList()
    
    override suspend fun saveBill(bill: Bill): Result<Unit> {
        bills.add(bill)
        return Result.success(Unit)
    }
}
```

---

遵循这些架构原则将确保项目的可扩展性、可维护性和可测试性，为 yike-app 的长期发展提供坚实的架构基础。