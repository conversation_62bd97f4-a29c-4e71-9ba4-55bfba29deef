# 代码规范与风格指南

本文档定义了 guichai-app 项目的 Kotlin/Android 代码规范和风格指南。

## 项目技术栈参考
#[[file:common/libs.versions.toml]]

## 命名规范

### 类和接口命名
- **类名**：使用 PascalCase，名词性，描述清晰
  ```kotlin
  // ✅ 正确
  class UserAccountManager
  class BillCalculationService
  
  // ❌ 错误
  class userManager
  class calculate
  ```

- **接口命名**：使用 PascalCase，通常以 I 开头或以 able/er 结尾
  ```kotlin
  // ✅ 正确
  interface IBillRepository
  interface Calculable
  interface DataSyncer
  ```

### 函数命名
- 使用 camelCase，动词性，表达明确的行为
  ```kotlin
  // ✅ 正确
  fun calculateTotalAmount()
  fun saveBillToDatabase()
  fun validateUserInput()
  
  // ❌ 错误
  fun Calculate()
  fun save()
  fun check()
  ```

### 变量命名
- **局部变量**：camelCase，简洁但有意义
  ```kotlin
  // ✅ 正确
  val totalAmount = calculateTotal()
  val userBillList = getUserBills()
  
  // ❌ 错误
  val ta = calculateTotal()
  val list = getUserBills()
  ```

- **常量**：SCREAMING_SNAKE_CASE
  ```kotlin
  // ✅ 正确
  const val MAX_BILL_AMOUNT = 999999.99
  const val DEFAULT_CURRENCY = "CNY"
  ```

## 包结构规范

基于项目模块化架构，遵循以下包结构：

```
com.xiaojinzi.tally.{module}
├── ui/                 # UI 层
│   ├── view/          # Compose UI 组件
│   └── viewmodel/     # ViewModel
├── domain/            # 业务逻辑层
│   ├── usecase/      # 用例
│   └── model/        # 业务模型
├── data/             # 数据层
│   ├── repository/   # 仓库实现
│   ├── datasource/   # 数据源
│   └── entity/       # 数据实体
└── di/               # 依赖注入配置
```

## 依赖注入规范 (Hilt)

### Module 定义
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object DataModule {
    
    @Provides
    @Singleton
    fun provideBillRepository(
        localDataSource: BillLocalDataSource,
        remoteDataSource: BillRemoteDataSource
    ): IBillRepository = BillRepositoryImpl(localDataSource, remoteDataSource)
}
```

### Repository 注入
```kotlin
@Singleton
class BillRepositoryImpl @Inject constructor(
    private val localDataSource: BillLocalDataSource,
    private val remoteDataSource: BillRemoteDataSource
) : IBillRepository {
    // 实现
}
```

## 注释和文档规范

### 类注释
```kotlin
/**
 * 账单计算服务
 * 
 * 负责处理各种账单相关的计算逻辑，包括：
 * - 总金额计算
 * - 税费计算
 * - 汇率转换
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class BillCalculationService
```

### 函数注释
```kotlin
/**
 * 计算账单总金额
 * 
 * @param bills 账单列表
 * @param includeTax 是否包含税费
 * @return 计算后的总金额，保留两位小数
 * @throws IllegalArgumentException 当账单列表为空时抛出
 */
fun calculateTotalAmount(bills: List<Bill>, includeTax: Boolean = true): BigDecimal
```

## 错误处理规范

### 异常处理
```kotlin
// ✅ 正确：具体的异常类型
class BillValidationException(message: String) : Exception(message)

// ✅ 正确：使用 Result 类型处理可能失败的操作
suspend fun saveBill(bill: Bill): Result<Unit> {
    return try {
        billRepository.save(bill)
        Result.success(Unit)
    } catch (e: Exception) {
        Result.failure(e)
    }
}
```

### 空值处理
```kotlin
// ✅ 正确：使用安全调用和 Elvis 操作符
val userName = user?.name ?: "未知用户"

// ✅ 正确：使用 let 进行空值检查
bill?.let { validBill ->
    saveBill(validBill)
}
```

## 代码格式化规则

### 缩进和空格
- 使用 4 个空格缩进，不使用 Tab
- 操作符前后添加空格
- 逗号后添加空格

### 行长度
- 每行最大长度 120 字符
- 超长行使用合理的换行策略

### 大括号
```kotlin
// ✅ 正确
if (condition) {
    doSomething()
} else {
    doSomethingElse()
}

// ✅ 正确：单行可省略大括号
if (condition) doSomething()
```

## 日志记录规范

### 日志级别使用
```kotlin
// 调试信息
Log.d(TAG, "用户点击了保存按钮")

// 重要信息
Log.i(TAG, "账单保存成功，ID: $billId")

// 警告信息
Log.w(TAG, "网络请求超时，使用缓存数据")

// 错误信息
Log.e(TAG, "保存账单失败", exception)
```

### TAG 定义
```kotlin
class BillViewModel : ViewModel() {
    companion object {
        private const val TAG = "BillViewModel"
    }
}
```

## 性能相关规范

### 避免内存泄漏
```kotlin
// ✅ 正确：在 ViewModel 中取消协程
class BillViewModel : ViewModel() {
    private val viewModelJob = SupervisorJob()
    private val viewModelScope = CoroutineScope(Dispatchers.Main + viewModelJob)
    
    override fun onCleared() {
        super.onCleared()
        viewModelJob.cancel()
    }
}
```

### 数据类优化
```kotlin
// ✅ 正确：使用 data class
data class Bill(
    val id: Long,
    val amount: BigDecimal,
    val description: String,
    val createTime: LocalDateTime
)
```

---

遵循这些规范将确保代码的一致性、可读性和可维护性，为 yike-app 项目的长期发展奠定坚实基础。