# 图标修复部署说明

## 🔧 修复内容

修复了以下分类的图标问题：

1. **出行交通**：`car1` → `taxi1`
2. **休闲娱乐**：`gamepad1` → `gamePad1`
3. **健康医疗**：`medical1` → `firstAidKit1`
4. **投资收益**：`investment1` → `invest1`
5. **游戏娱乐**（子类别）：`gamepad1` → `gamePad1`

## 📋 部署步骤

### 步骤1：更新SQL函数

在Supabase控制台的SQL编辑器中，重新执行以下两个文件：

**A. 更新增强版函数：**
```
common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/create_default_categories_enhanced.sql
```

**B. 更新初始化函数（安全版本）：**
```
supabase_schema_deploy_safe.sql
```

**或者如果遇到权限问题，可以跳过完整的 supabase_database_schema.sql**

**C. 更新用户数据初始化函数：**
```
common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/init_user_data.sql
```

这将更新所有相关函数，确保新创建的账本使用正确的图标。

### 步骤2：修复现有数据

在Supabase控制台的SQL编辑器中，执行以下修复脚本：

```
common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/fix_category_icons.sql
```

这将修复已经存在的分类数据中的图标问题。

### 步骤3：验证修复结果

执行修复脚本后，会自动显示验证结果，包括：
- 修复的分类列表
- 修复的总数量

## 🎯 预期结果

修复完成后：

1. **新建账本**：会自动使用正确的图标
2. **现有账本**：已存在的分类图标会被更新为正确的图标
3. **图标显示**：应用中的分类图标应该正确显示

## 📱 测试建议

1. 在Supabase控制台执行修复脚本
2. 在应用中刷新或重新进入分类页面
3. 检查以下分类的图标是否正确：
   - 出行交通 → 应显示出租车图标
   - 休闲娱乐 → 应显示游戏手柄图标
   - 健康医疗 → 应显示急救包图标
   - 投资收益 → 应显示投资图标
   - 游戏娱乐 → 应显示游戏手柄图标

## ⚠️ 注意事项

- 修复脚本是安全的，只会更新图标字段
- 不会影响分类的其他数据
- 如果某些分类不存在，修复脚本会跳过，不会报错
