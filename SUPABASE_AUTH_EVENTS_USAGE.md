# Supabase 认证事件监听使用指南

## 📋 概述

本指南介绍如何使用 Supabase-kt 3.2.2+ 版本中的 `Flow<AuthEvent>` 系统来监听 OTP 验证回调和其他认证事件，替代传统的重试机制。

## 🚀 快速开始

### 1. 基本事件监听

```kotlin
class EmailLoginUseCaseV2Impl : BusinessUseCaseImpl(), EmailLoginUseCaseV2 {
    private val otpAuthManager = OtpAuthManagerV2.getInstance()
    
    override fun onStart() {
        super.onStart()
        
        // 监听认证事件
        otpAuthManager.authEvents.onEach { event ->
            when (event) {
                is AuthEvent.OtpError -> {
                    Log.w(TAG, "OTP 错误: 错误代码=${event.errorCode}")
                    handleOtpError(event)
                }
                is AuthEvent.RefreshFailure -> {
                    Log.w(TAG, "会话刷新失败: ${event.cause?.message}")
                    handleRefreshFailure(event)
                }
                else -> {
                    Log.d(TAG, "收到认证事件: ${event.javaClass.simpleName}")
                }
            }
        }.launchIn(scope)
    }
    
    private fun handleOtpError(event: AuthEvent.OtpError) {
        // 处理 OTP 错误，例如显示错误消息给用户
        val errorMessage = when (event.errorCode) {
            "invalid_token" -> "验证码无效，请重新输入"
            "expired_token" -> "验证码已过期，请重新获取"
            "too_many_attempts" -> "尝试次数过多，请稍后再试"
            else -> "验证失败: ${event.errorCode}"
        }
        
        // 更新 UI 状态
        _errorMessage.value = errorMessage
        _isLoading.value = false
    }
    
    private fun handleRefreshFailure(event: AuthEvent.RefreshFailure) {
        // 处理会话刷新失败
        Log.w(TAG, "会话刷新失败，可能需要重新登录")
        
        // 清除本地会话状态
        clearUserSession()
        
        // 导航到登录页面
        navigateToLogin()
    }
}
```

### 2. 在 ViewModel 中使用

```kotlin
class LoginViewModel : ViewModel() {
    private val otpAuthManager = OtpAuthManagerV2.getInstance()
    
    private val _uiState = MutableStateFlow(LoginUiState())
    val uiState = _uiState.asStateFlow()
    
    init {
        // 监听认证事件
        otpAuthManager.authEvents
            .onEach { event ->
                when (event) {
                    is AuthEvent.OtpError -> {
                        _uiState.update { it.copy(
                            isLoading = false,
                            errorMessage = getOtpErrorMessage(event.errorCode)
                        )}
                    }
                    else -> {
                        Log.d(TAG, "收到认证事件: ${event.javaClass.simpleName}")
                    }
                }
            }
            .launchIn(viewModelScope)
    }
    
    private fun getOtpErrorMessage(errorCode: String): String {
        return when (errorCode) {
            "invalid_token" -> "验证码错误"
            "expired_token" -> "验证码已过期"
            "too_many_attempts" -> "尝试次数过多"
            else -> "验证失败"
        }
    }
}
```

### 3. 完整的登录流程示例

```kotlin
class EmailLoginUseCaseV2Impl : BusinessUseCaseImpl(), EmailLoginUseCaseV2 {
    private val otpAuthManager = OtpAuthManagerV2.getInstance()
    
    private val _loginState = MutableStateFlow(LoginState.Idle)
    val loginState = _loginState.asStateFlow()
    
    override fun onStart() {
        super.onStart()
        
        // 监听认证事件
        otpAuthManager.authEvents
            .onEach { event ->
                handleAuthEvent(event)
            }
            .launchIn(scope)
    }
    
    override suspend fun sendOtp(email: String) {
        try {
            _loginState.value = LoginState.SendingOtp
            
            val success = otpAuthManager.sendEmailOtp(email)
            if (success) {
                _loginState.value = LoginState.OtpSent
            }
        } catch (e: Exception) {
            _loginState.value = LoginState.Error(e.message ?: "发送失败")
        }
    }
    
    override suspend fun verifyOtp(email: String, token: String) {
        try {
            _loginState.value = LoginState.VerifyingOtp
            
            val session = otpAuthManager.verifyEmailOtp(email, token)
            _loginState.value = LoginState.Success(session)
        } catch (e: Exception) {
            // 注意：这里的异常可能会被 AuthEvent 事件补充或替代
            _loginState.value = LoginState.Error(e.message ?: "验证失败")
        }
    }
    
    private fun handleAuthEvent(event: AuthEvent) {
        when (event) {
            is AuthEvent.OtpError -> {
                Log.w(TAG, "收到 OTP 错误事件: ${event.errorCode}")
                
                // 根据错误代码更新状态
                val errorMessage = when (event.errorCode) {
                    "invalid_token" -> "验证码无效"
                    "expired_token" -> "验证码已过期"
                    "too_many_attempts" -> "尝试次数过多，请稍后再试"
                    else -> "验证失败: ${event.errorCode}"
                }
                
                _loginState.value = LoginState.Error(errorMessage)
            }
            
            is AuthEvent.RefreshFailure -> {
                Log.w(TAG, "会话刷新失败: ${event.cause?.message}")
                // 处理会话刷新失败
            }
            
            else -> {
                Log.d(TAG, "收到其他认证事件: ${event.javaClass.simpleName}")
            }
        }
    }
}

sealed class LoginState {
    object Idle : LoginState()
    object SendingOtp : LoginState()
    object OtpSent : LoginState()
    object VerifyingOtp : LoginState()
    data class Success(val session: UserSession) : LoginState()
    data class Error(val message: String) : LoginState()
}
```

## 🔧 可用的认证事件类型

### AuthEvent.OtpError
- **用途**: OTP 验证过程中的错误
- **属性**: `errorCode: String` - 错误代码
- **常见错误代码**:
  - `invalid_token`: 验证码无效
  - `expired_token`: 验证码已过期
  - `too_many_attempts`: 尝试次数过多

### AuthEvent.RefreshFailure
- **用途**: 会话刷新失败
- **属性**: `cause: Throwable?` - 失败原因
- **处理建议**: 清除本地会话，引导用户重新登录

### 其他事件
- Supabase-kt 可能会添加更多事件类型
- 建议使用 `else` 分支处理未知事件

## 📱 UI 集成示例

### Compose UI

```kotlin
@Composable
fun LoginScreen(
    viewModel: LoginViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(Unit) {
        // 监听认证事件并显示 Toast
        viewModel.authEvents.collect { event ->
            when (event) {
                is AuthEvent.OtpError -> {
                    // 显示错误 Toast
                    showToast("验证失败: ${event.errorCode}")
                }
            }
        }
    }
    
    // UI 内容...
}
```

## ⚠️ 注意事项

1. **事件监听生命周期**: 确保在适当的生命周期中启动和停止事件监听
2. **错误处理**: 事件监听是对异常处理的补充，不是替代
3. **线程安全**: 事件回调在协程中执行，注意线程安全
4. **内存泄漏**: 使用 `launchIn(scope)` 确保协程正确取消

## 🔄 迁移指南

### 从重试机制迁移到事件监听

**之前 (重试机制)**:
```kotlin
// 旧的重试逻辑
repeat(3) { attempt ->
    try {
        return otpAuthManager.verifyEmailOtp(email, token)
    } catch (e: Exception) {
        if (attempt == 2) throw e
        delay(1000)
    }
}
```

**现在 (事件监听)**:
```kotlin
// 新的事件监听
otpAuthManager.authEvents.onEach { event ->
    when (event) {
        is AuthEvent.OtpError -> {
            // 实时处理 OTP 错误
            handleOtpError(event.errorCode)
        }
    }
}.launchIn(scope)

// 直接调用验证
val session = otpAuthManager.verifyEmailOtp(email, token)
```

## 🎯 最佳实践

1. **集中处理**: 在一个地方处理所有认证事件
2. **用户友好**: 将技术错误代码转换为用户友好的消息
3. **状态管理**: 结合事件监听和状态管理提供流畅的用户体验
4. **日志记录**: 记录所有认证事件以便调试
5. **测试**: 为事件处理逻辑编写单元测试

## 🐛 故障排除

### 事件未触发
- 确保 Supabase-kt 版本 >= 3.2.0
- 检查事件监听是否正确启动
- 验证协程作用域是否活跃

### 重复事件
- 检查是否多次启动事件监听
- 使用 `distinctUntilChanged()` 过滤重复事件

### 内存泄漏
- 确保使用正确的协程作用域
- 在组件销毁时取消事件监听

---

**更新日期**: 2025-01-21  
**Supabase-kt 版本**: 3.2.2+  
**作者**: Claude 4.0 sonnet 🐾