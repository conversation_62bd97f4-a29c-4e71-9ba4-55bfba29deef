# Supabase 邮箱 OTP 登录集成修复方案

## 1. 产品概述
本项目需要修复和完善 Supabase 邮箱验证码登录功能。当前项目已配置 Supabase 3.0.2 依赖，但缺少核心配置文件和实现代码。用户可以通过邮箱接收验证码进行无密码登录。

## 2. 核心功能

### 2.1 用户角色
| 角色 | 注册方式 | 核心权限 |
|------|----------|----------|
| 默认用户 | 邮箱验证码登录 | 可使用应用的所有基础功能 |

### 2.2 功能模块
我们的邮箱登录需求包含以下主要页面：
1. **邮箱登录页面**：邮箱输入、验证码发送、验证码输入、登录验证

### 2.3 页面详情
| 页面名称 | 模块名称 | 功能描述 |
|----------|----------|----------|
| 邮箱登录页面 | 邮箱输入模块 | 输入邮箱地址，实时验证邮箱格式 |
| 邮箱登录页面 | 验证码发送模块 | 发送邮箱验证码，60秒防重复发送倒计时 |
| 邮箱登录页面 | 验证码输入模块 | 输入6位数字验证码 |
| 邮箱登录页面 | 登录验证模块 | 验证邮箱和验证码，完成登录流程 |

## 3. 核心流程
用户操作流程：
1. 用户进入邮箱登录页面
2. 输入有效邮箱地址
3. 点击发送验证码按钮
4. 系统调用 Supabase API 发送邮箱验证码
5. 用户在邮箱中查收验证码
6. 输入6位验证码
7. 点击登录按钮
8. 系统验证邮箱和验证码
9. 登录成功后跳转到主页面

```mermaid
graph TD
    A[邮箱登录页面] --> B[输入邮箱]
    B --> C[发送验证码]
    C --> D[输入验证码]
    D --> E[验证登录]
    E --> F[主页面]
```

## 4. 用户界面设计
### 4.1 设计风格
- 主色调：Material Design 3 主题色彩
- 按钮样式：圆角按钮，Material 3 风格
- 字体：系统默认字体，标题16sp，正文14sp
- 布局风格：卡片式布局，垂直排列
- 图标风格：Material Icons

### 4.2 页面设计概览
| 页面名称 | 模块名称 | UI元素 |
|----------|----------|--------|
| 邮箱登录页面 | 邮箱输入模块 | OutlinedTextField，邮箱图标，实时验证提示 |
| 邮箱登录页面 | 验证码发送模块 | FilledButton，倒计时文本，加载状态 |
| 邮箱登录页面 | 验证码输入模块 | OutlinedTextField，数字键盘，6位限制 |
| 邮箱登录页面 | 登录验证模块 | FilledButton，加载动画，错误提示 |

### 4.3 响应式设计
采用移动端优先设计，支持不同屏幕尺寸的Android设备，使用Compose响应式布局。