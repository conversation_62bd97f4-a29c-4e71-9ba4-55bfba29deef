# Supabase Kotlin 邮箱 OTP 验证实施指南

## 📋 概述

本项目已完成 Supabase Kotlin 邮箱 OTP 验证功能的集成。该功能允许用户通过邮箱接收验证码进行无密码登录。

## 🏗️ 架构概述

### 核心组件

1. **SupabaseConfig** - Supabase 客户端配置
2. **OtpAuthManager** - OTP 认证管理器
3. **EmailLoginUseCase** - 邮箱登录业务逻辑
4. **EmailLoginViewModel** - UI 状态管理
5. **EmailLoginViews** - UI 界面组件

### 文件结构

```
common/
├── module-base/
│   └── build.gradle.kts                 # 添加了 Supabase 依赖
├── module-core/
│   └── src/main/java/com/yike/core/supabase/
│       ├── SupabaseConfig.kt           # Supabase 客户端配置
│       ├── SupabaseConstants.kt        # 配置常量
│       └── OtpAuthManager.kt           # OTP 认证管理器
└── module-user/
    └── src/main/java/com/xiaojinzi/tally/module/user/module/email_login/
        ├── domain/
        │   └── EmailLoginUseCase.kt     # 业务逻辑层
        └── view/
            ├── EmailLoginViewModel.kt   # 视图模型
            ├── EmailLoginViews.kt       # UI 组件
            └── EmailLoginAct.kt         # Activity
```

## ⚙️ 配置步骤

### 1. Supabase 项目配置

在开始使用之前，你需要：

1. **创建 Supabase 项目**
   - 访问 [supabase.com](https://supabase.com)
   - 创建新项目或使用现有项目

2. **获取项目信息**
   - 项目 URL: `https://your-project-ref.supabase.co`
   - anon 密钥: 在项目设置中找到

3. **配置项目密钥**
   
   编辑文件：`/common/module-core/src/main/java/com/yike/core/supabase/SupabaseConstants.kt`
   
   ```kotlin
   object SupabaseConstants {
       // 替换为你的实际项目信息
       const val SUPABASE_URL = "https://your-project-ref.supabase.co"
       const val SUPABASE_ANON_KEY = "your-anon-key-here"
   }
   ```

### 2. 邮件模板配置

⚠️ **重要：必须配置邮件模板才能发送验证码**

1. **进入 Supabase Dashboard**
   - 登录你的 Supabase 项目
   - 导航到 `Authentication` → `Email Templates`

2. **修改邮件模板**
   - 选择 "Confirm signup" 或创建新的模板
   - 将默认的 `{{ .ConfirmationURL }}` 替换为 `{{ .Token }}`

3. **推荐的邮件模板**：
   ```html
   <h2>邮箱验证码</h2>
   <p>您的验证码是：<strong style="font-size: 24px; color: #4F46E5;">{{ .Token }}</strong></p>
   <p>验证码有效期为 10 分钟，请尽快使用。</p>
   <p>如果您没有请求此验证码，请忽略此邮件。</p>
   ```

### 3. 启用邮箱认证

1. 在 Supabase Dashboard 中
2. 进入 `Authentication` → `Providers`
3. 确保 `Email` 提供商已启用
4. 配置 OTP 设置：
   - OTP expiry: 600 seconds (10 minutes)
   - OTP length: 6 digits

## 🚀 使用方法

### 启动邮箱登录界面

使用应用内路由系统导航到邮箱登录页面：

```kotlin
// 在你的代码中使用路由跳转
AppRouterUserApi::class
    .routeApi()
    .toEmailLoginView(context = context)
```

### 主要功能

1. **邮箱输入验证** - 实时验证邮箱格式
2. **发送验证码** - 60秒防重复发送
3. **验证码输入** - 6位数字验证码
4. **自动登录** - 验证成功后自动跳转到主界面

## 🔧 API 使用示例

### 直接使用 OtpAuthManager

```kotlin
val otpAuthManager = OtpAuthManager()

// 发送邮箱验证码
val sendResult = otpAuthManager.sendEmailOtp("<EMAIL>")
sendResult.fold(
    onSuccess = { 
        println("验证码发送成功") 
    },
    onFailure = { exception ->
        println("发送失败: ${exception.message}")
    }
)

// 验证 OTP
val verifyResult = otpAuthManager.verifyEmailOtp("<EMAIL>", "123456")
verifyResult.fold(
    onSuccess = { userSession ->
        println("登录成功: ${userSession?.user?.email}")
    },
    onFailure = { exception ->
        println("验证失败: ${exception.message}")
    }
)
```

## 🧪 测试

### 测试前准备

1. 确保已正确配置 Supabase 项目信息
2. 确保已配置邮件模板
3. 使用真实邮箱地址进行测试

### 测试流程

1. **构建项目**
   ```bash
   ./gradlew assembleDebug
   ```

2. **启动应用**
   - 安装应用到设备/模拟器
   - 导航到邮箱登录页面

3. **功能测试**
   - [ ] 输入无效邮箱 → 应显示格式错误
   - [ ] 输入有效邮箱 → 发送按钮变为可用
   - [ ] 点击发送验证码 → 应显示倒计时
   - [ ] 检查邮箱 → 应收到6位验证码
   - [ ] 输入正确验证码 → 应成功登录
   - [ ] 输入错误验证码 → 应显示错误信息

## ❗ 故障排除

### 常见问题

1. **验证码未收到**
   - 检查 Supabase 项目配置是否正确
   - 确认邮件模板已正确设置 `{{ .Token }}`
   - 检查垃圾邮件文件夹

2. **编译错误**
   - 确保所有依赖已正确添加
   - 运行 `./gradlew clean build`

3. **运行时异常**
   - 检查 SupabaseConstants 中的 URL 和 Key 是否正确
   - 确保设备有网络连接

### 调试技巧

1. **启用调试日志**
   ```kotlin
   // 在 SupabaseConfig 中添加
   install(Auth) {
       alwaysRefreshToken = true
   }
   ```

2. **检查网络请求**
   - 使用 Charles Proxy 或 Flipper 监控网络请求
   - 检查请求是否正确发送到 Supabase

## 📝 开发注意事项

1. **安全性**
   - anon key 可以暴露在客户端
   - 真实项目应使用环境变量管理密钥

2. **用户体验**
   - 提供清晰的错误提示
   - 合理的重试机制
   - 适当的加载状态

3. **扩展性**
   - 现有架构支持添加其他认证方式
   - 可以轻松集成 Supabase 其他功能

## 🔗 相关资源

- [Supabase Auth 文档](https://supabase.com/docs/guides/auth)
- [Supabase Kotlin 客户端文档](https://github.com/supabase-community/supabase-kt)
- [邮箱无密码登录指南](https://supabase.com/docs/guides/auth/auth-email-passwordless)

---

**实施完成日期**: 2025年7月21日
**版本**: 1.0.0
**作者**: Claude 4.0 sonnet 🐾