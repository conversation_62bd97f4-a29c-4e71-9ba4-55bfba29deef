<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱验证码 - 易记账本</title>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <style type="text/css">
        /* Reset styles */
        * {
            box-sizing: border-box;
        }
        
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }
        
        /* Main styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f5f7fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        
        .email-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px 30px;
            text-align: center;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #ffffff;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .tagline {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin: 8px 0 0 0;
        }
        
        .email-body {
            padding: 50px 30px;
        }
        
        .greeting {
            font-size: 20px;
            color: #2d3748;
            margin-bottom: 10px;
            text-align: center;
        }
        
        .message {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 35px;
            text-align: center;
            line-height: 1.5;
        }
        
        .otp-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            box-shadow: 0 10px 25px rgba(240, 147, 251, 0.3);
        }
        
        .otp-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 15px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .otp-code {
            font-size: 42px;
            font-weight: bold;
            color: #ffffff;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            margin: 0;
            padding: 10px 0;
        }
        
        .expiry-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .expiry-info .icon {
            font-size: 20px;
            margin-right: 8px;
        }
        
        .expiry-text {
            font-size: 14px;
            color: #856404;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .security-notice {
            background-color: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 30px 0;
        }
        
        .security-title {
            font-size: 16px;
            color: #4c51bf;
            font-weight: 600;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
        }
        
        .security-text {
            font-size: 14px;
            color: #5a67d8;
            margin: 0;
            line-height: 1.5;
        }
        
        .email-footer {
            background-color: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .footer-text {
            font-size: 12px;
            color: #a0aec0;
            margin: 0;
            line-height: 1.5;
        }
        
        .footer-links {
            margin: 15px 0 0 0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
            font-size: 12px;
        }
        
        /* Mobile responsiveness */
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding-left: 20px !important;
                padding-right: 20px !important;
            }
            
            .otp-code {
                font-size: 36px !important;
                letter-spacing: 4px !important;
            }
            
            .greeting {
                font-size: 18px !important;
            }
            
            .message {
                font-size: 14px !important;
            }
        }
        
        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a202c !important;
            }
            
            .email-body {
                background-color: #1a202c !important;
            }
            
            .greeting {
                color: #e2e8f0 !important;
            }
            
            .message {
                color: #a0aec0 !important;
            }
            
            .email-footer {
                background-color: #2d3748 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <h1 class="logo">💰 易记账本</h1>
            <p class="tagline">您的智能财务管理助手</p>
        </div>
        
        <!-- Body -->
        <div class="email-body">
            <h2 class="greeting">邮箱验证码</h2>
            <p class="message">
                您正在尝试登录易记账本，请使用以下验证码完成登录操作。
            </p>
            
            <!-- OTP Container -->
            <div class="otp-container">
                <p class="otp-label">您的验证码</p>
                <h1 class="otp-code">{{ .Token }}</h1>
            </div>
            
            <!-- Expiry Info -->
            <div class="expiry-info">
                <p class="expiry-text">
                    <span class="icon">⏰</span>
                    验证码有效期为 <strong>10 分钟</strong>，请尽快使用
                </p>
            </div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <p class="security-title">
                    🔒 安全提示
                </p>
                <p class="security-text">
                    • 请勿将验证码分享给任何人<br>
                    • 如果您没有请求此验证码，请忽略此邮件<br>
                    • 验证码仅用于本次登录，过期后自动失效
                </p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="email-footer">
            <p class="footer-text">
                此邮件由易记账本系统自动发送，请勿直接回复。<br>
                如有疑问，请联系我们的客服支持。
            </p>
            <div class="footer-links">
                <a href="#">帮助中心</a>
                <a href="#">隐私政策</a>
                <a href="#">联系我们</a>
            </div>
        </div>
    </div>
</body>
</html>