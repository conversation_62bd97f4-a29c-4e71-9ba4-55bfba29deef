<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码输入框测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 14px;
        }
        
        .email {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 30px;
        }
        
        .otp-container {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-bottom: 30px;
        }
        
        .otp-input {
            width: 48px;
            height: 48px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            color: #333;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .otp-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .otp-input.filled {
            border-color: #667eea;
            background: white;
        }
        
        .login-btn {
            width: 100%;
            height: 48px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .resend {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
        }
        
        .resend:hover {
            text-decoration: underline;
        }
        
        .tip {
            color: #999;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">📱</div>
        <h1>输入验证码</h1>
        <div class="subtitle">我们已发送验证码到</div>
        <div class="email"><EMAIL></div>
        
        <div class="otp-container">
            <input type="text" class="otp-input" maxlength="1" data-index="0">
            <input type="text" class="otp-input" maxlength="1" data-index="1">
            <input type="text" class="otp-input" maxlength="1" data-index="2">
            <input type="text" class="otp-input" maxlength="1" data-index="3">
            <input type="text" class="otp-input" maxlength="1" data-index="4">
            <input type="text" class="otp-input" maxlength="1" data-index="5">
        </div>
        
        <button class="login-btn" id="loginBtn" disabled>登录</button>
        
        <a href="#" class="resend">重新发送验证码</a>
        
        <div class="tip">
            没有收到验证码？请检查邮箱垃圾箱或重新发送
        </div>
    </div>

    <script>
        const otpInputs = document.querySelectorAll('.otp-input');
        const loginBtn = document.getElementById('loginBtn');
        
        otpInputs.forEach((input, index) => {
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                
                // 只允许数字
                if (!/^\d*$/.test(value)) {
                    e.target.value = '';
                    return;
                }
                
                // 添加填充样式
                if (value) {
                    e.target.classList.add('filled');
                    // 自动跳转到下一个输入框
                    if (index < otpInputs.length - 1) {
                        otpInputs[index + 1].focus();
                    }
                } else {
                    e.target.classList.remove('filled');
                }
                
                // 检查是否所有输入框都已填写
                checkComplete();
            });
            
            input.addEventListener('keydown', (e) => {
                // 退格键处理
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    otpInputs[index - 1].focus();
                    otpInputs[index - 1].value = '';
                    otpInputs[index - 1].classList.remove('filled');
                    checkComplete();
                }
            });
            
            input.addEventListener('paste', (e) => {
                e.preventDefault();
                const paste = e.clipboardData.getData('text');
                const digits = paste.replace(/\D/g, '').slice(0, 6);
                
                digits.split('').forEach((digit, i) => {
                    if (otpInputs[i]) {
                        otpInputs[i].value = digit;
                        otpInputs[i].classList.add('filled');
                    }
                });
                
                checkComplete();
            });
        });
        
        function checkComplete() {
            const values = Array.from(otpInputs).map(input => input.value);
            const isComplete = values.every(value => value !== '');
            
            loginBtn.disabled = !isComplete;
            
            if (isComplete) {
                console.log('验证码:', values.join(''));
            }
        }
        
        loginBtn.addEventListener('click', () => {
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            alert(`验证码: ${otp}\n登录功能演示完成！`);
        });
        
        // 自动聚焦第一个输入框
        otpInputs[0].focus();
    </script>
</body>
</html>