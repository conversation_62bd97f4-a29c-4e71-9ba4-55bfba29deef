-- =============================================
-- 安全部署脚本 - 只更新函数，不修改表结构
-- =============================================

-- 创建默认类别的函数（修复图标版本）
CREATE OR REPLACE FUNCTION create_default_categories_for_book(p_user_id UUID, p_book_id UUID)
RETURNS INTEGER AS $$
DECLARE
    v_count INTEGER := 0;
    v_current_time BIGINT := EXTRACT(EPOCH FROM NOW()) * 1000;
BEGIN
    -- 插入默认支出类别（使用修复后的图标）
    INSERT INTO book_categories (user_id, book_id, name, type, icon_name, color, sort_order, is_system) VALUES
    (p_user_id, p_book_id, '食品餐饮', 'spending', 'chopsticksFork1', '#FF5722', v_current_time - 1, TRUE),
    (p_user_id, p_book_id, '购物消费', 'spending', 'shopping1', '#E91E63', v_current_time - 2, TRUE),
    (p_user_id, p_book_id, '交通出行', 'spending', 'taxi1', '#2196F3', v_current_time - 3, TRUE),
    (p_user_id, p_book_id, '娱乐休闲', 'spending', 'gamePad1', '#9C27B0', v_current_time - 4, TRUE),
    (p_user_id, p_book_id, '医疗健康', 'spending', 'firstAidKit1', '#F44336', v_current_time - 5, TRUE),
    (p_user_id, p_book_id, '居住缴费', 'spending', 'house1', '#795548', v_current_time - 6, TRUE);
    
    v_count := v_count + 6;
    
    -- 插入默认收入类别（使用修复后的图标）
    INSERT INTO book_categories (user_id, book_id, name, type, icon_name, color, sort_order, is_system) VALUES
    (p_user_id, p_book_id, '工资', 'income', 'wage1', '#4CAF50', v_current_time - 7, TRUE),
    (p_user_id, p_book_id, '奖金', 'income', 'bonus1', '#8BC34A', v_current_time - 8, TRUE),
    (p_user_id, p_book_id, '投资收益', 'income', 'invest1', '#00BCD4', v_current_time - 9, TRUE),
    (p_user_id, p_book_id, '其他收入', 'income', 'money1', '#009688', v_current_time - 10, TRUE);
    
    v_count := v_count + 4;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 验证函数更新成功
SELECT 'create_default_categories_for_book 函数更新成功' as status;

-- 显示修复的图标
SELECT 
    '图标修复完成' as status,
    '交通出行: taxi1, 娱乐休闲: gamePad1, 医疗健康: firstAidKit1, 投资收益: invest1' as fixed_icons;
