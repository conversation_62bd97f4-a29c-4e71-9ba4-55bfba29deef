# Supabase 邮箱登录配置指南

## 问题修复总结

### 1. RuntimeException 修复
原因：`SupabaseConstants.kt` 中使用了占位符值，导致 Supabase 客户端初始化失败。

**修复内容：**
- 在 `SupabaseConfig.kt` 中添加了配置验证和错误处理
- 提供了更清晰的错误信息，指导用户正确配置
- 防止应用因配置错误而崩溃

### 2. 默认登录页面修改
**修改内容：**
- 将 `NotLoginRouterInterceptor.kt` 中的默认路由从 `toLoginView`（手机登录）改为 `toEmailLoginView`（邮箱登录）
- 现在用户未登录时会直接跳转到邮箱登录页面

## Supabase 配置步骤

### 1. 创建 Supabase 项目
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 创建新项目或选择现有项目
3. 等待项目初始化完成

### 2. 获取项目配置信息
1. 在 Supabase Dashboard 中，进入项目设置
2. 点击 **Settings** > **API**
3. 复制以下信息：
   - **Project URL**（项目 URL）
   - **anon public** key（匿名公钥）

### 3. 配置应用
编辑文件：`common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/SupabaseConstants.kt`

```kotlin
object SupabaseConstants {
    
    // 替换为你的 Supabase 项目 URL
    const val SUPABASE_URL = "https://your-project-ref.supabase.co"
    
    // 替换为你的 Supabase anon 密钥
    const val SUPABASE_ANON_KEY = "your-actual-anon-key-here"
    
    // ... 其他配置保持不变
}
```

### 4. 配置邮箱认证
1. 在 Supabase Dashboard 中，进入 **Authentication** > **Settings**
2. 在 **Auth Providers** 部分，确保 **Email** 已启用
3. 配置邮箱模板（可选）：
   - 进入 **Auth** > **Email Templates**
   - 自定义确认邮件和重置密码邮件模板

### 5. 测试配置
1. 重新编译项目：`./gradlew clean build`
2. 运行应用
3. 触发登录流程，应该会跳转到邮箱登录页面
4. 尝试发送 OTP 验证码

## 功能特性

### 邮箱登录流程
1. **邮箱验证**：输入邮箱地址，系统验证格式
2. **发送 OTP**：点击发送验证码，系统发送 6 位数字验证码到邮箱
3. **验证登录**：输入收到的验证码完成登录
4. **防重复发送**：60 秒内不能重复发送验证码

### 错误处理
- 配置错误时会显示清晰的错误信息
- 网络错误时会提示用户检查网络连接
- 邮箱格式错误时会提示正确格式

## 注意事项

1. **安全性**：anon key 可以安全地暴露在客户端，但不要泄露 service_role key
2. **邮箱配置**：确保 Supabase 项目中已正确配置 SMTP 设置
3. **域名验证**：如果使用自定义域名，需要在 Supabase 中配置重定向 URL
4. **测试环境**：建议先在测试环境中验证配置正确性

## 故障排除

### 常见问题

**Q: 应用启动时崩溃，提示 Supabase 初始化失败**
A: 检查 `SupabaseConstants.kt` 中的配置是否正确，确保没有使用占位符值。

**Q: 发送验证码失败**
A: 
1. 检查网络连接
2. 确认 Supabase 项目中邮箱认证已启用
3. 检查 SMTP 配置是否正确

**Q: 验证码收不到**
A:
1. 检查垃圾邮件文件夹
2. 确认邮箱地址输入正确
3. 检查 Supabase 项目的邮件发送配置

**Q: 登录后立即退出**
A: 检查用户会话管理逻辑，确保登录状态正确保存。

---

**配置完成后，用户在未登录状态下会自动跳转到邮箱登录页面，享受更便捷的登录体验！** 🎉