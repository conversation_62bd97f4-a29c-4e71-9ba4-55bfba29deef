# Supabase 数据库集成指南

## 📋 概述

本指南介绍如何在 Yike 记账应用中使用 Supabase 数据库系统。该系统提供了完整的云端数据存储和同步功能，支持用户数据的安全管理和高效查询。

## 🏗️ 架构设计

### 数据表结构
- **users** - 用户信息缓存表
- **books** - 账本表
- **categories** - 分类表
- **accounts** - 账户表
- **bills** - 账单表（核心表）
- **bill_images** - 账单图片表

### 技术栈
- **Supabase** - 云端 PostgreSQL 数据库
- **Kotlin Coroutines** - 异步操作
- **Kotlinx Serialization** - 数据序列化
- **Row Level Security (RLS)** - 数据安全

## 🚀 快速开始

### 1. 数据库初始化

首先在 Supabase 控制台中执行 SQL 脚本：

```sql
-- 执行 create_tables.sql 脚本
-- 位置: common/module-core/src/main/java/com/xiaojinzi/tally/module/core/supabase/sql/create_tables.sql
```

### 2. 配置检查

确保 Supabase 配置正确：

```kotlin
// 检查 SupabaseConstants.kt 中的配置
object SupabaseConstants {
    const val SUPABASE_URL = "https://your-project-ref.supabase.co"
    const val SUPABASE_ANON_KEY = "your-actual-anon-key"
}
```

### 3. 初始化 Repository

```kotlin
// 在应用启动时初始化
SupabaseRepositoryManager.initializeAll()

// 健康检查
SupabaseRepositoryManager.healthCheck().fold(
    onSuccess = { status -> Log.d("Health", "状态: $status") },
    onFailure = { error -> Log.e("Health", "检查失败: ${error.message}") }
)
```

## 💾 基础 CRUD 操作

### 插入数据

```kotlin
// 创建账单
val bill = SupabaseBill(
    id = generateId(),
    userId = "user-123",
    bookId = "book-456",
    type = "normal",
    time = System.currentTimeMillis(),
    amount = 5000, // 50.00元（以分为单位）
    note = "午餐费用",
    timeCreate = System.currentTimeMillis()
)

// 插入单条记录
SupabaseRepositoryManager.billRepository.insert(bill).fold(
    onSuccess = { insertedBill ->
        Log.d("Insert", "插入成功: ${insertedBill.note}")
    },
    onFailure = { error ->
        Log.e("Insert", "插入失败: ${error.message}")
    }
)

// 批量插入
val bills = listOf(bill1, bill2, bill3)
SupabaseRepositoryManager.billRepository.insertBatch(bills).fold(
    onSuccess = { insertedBills ->
        Log.d("BatchInsert", "批量插入成功: ${insertedBills.size} 条")
    },
    onFailure = { error ->
        Log.e("BatchInsert", "批量插入失败: ${error.message}")
    }
)
```

### 查询数据

```kotlin
// 根据 ID 查询
SupabaseRepositoryManager.billRepository.getById("bill-123").fold(
    onSuccess = { bill ->
        Log.d("Query", "查询结果: ${bill?.note}")
    },
    onFailure = { error ->
        Log.e("Query", "查询失败: ${error.message}")
    }
)

// 根据用户 ID 查询
SupabaseRepositoryManager.billRepository.getByUserId(
    userId = "user-123",
    limit = 50,
    offset = 0
).fold(
    onSuccess = { bills ->
        Log.d("Query", "查询到 ${bills.size} 条账单")
    },
    onFailure = { error ->
        Log.e("Query", "查询失败: ${error.message}")
    }
)

// 复杂查询：时间范围内的账单
SupabaseRepositoryManager.billRepository.getBillsByTimeRange(
    userId = "user-123",
    startTime = startTime,
    endTime = endTime,
    bookId = "book-456", // 可选
    categoryId = "category-789", // 可选
    limit = 100
).fold(
    onSuccess = { bills ->
        Log.d("Query", "时间范围查询: ${bills.size} 条")
    },
    onFailure = { error ->
        Log.e("Query", "查询失败: ${error.message}")
    }
)
```

### 更新数据

```kotlin
// 更新账单
val updates = mapOf(
    "note" to "更新后的备注",
    "amount" to 6000L
)

SupabaseRepositoryManager.billRepository.update("bill-123", updates).fold(
    onSuccess = { updatedBill ->
        Log.d("Update", "更新成功: ${updatedBill?.note}")
    },
    onFailure = { error ->
        Log.e("Update", "更新失败: ${error.message}")
    }
)
```

### 删除数据

```kotlin
// 软删除（推荐）
SupabaseRepositoryManager.billRepository.softDelete("bill-123").fold(
    onSuccess = { success ->
        Log.d("Delete", "软删除成功: $success")
    },
    onFailure = { error ->
        Log.e("Delete", "软删除失败: ${error.message}")
    }
)

// 物理删除（谨慎使用）
SupabaseRepositoryManager.billRepository.hardDelete("bill-123").fold(
    onSuccess = { success ->
        Log.d("Delete", "物理删除成功: $success")
    },
    onFailure = { error ->
        Log.e("Delete", "物理删除失败: ${error.message}")
    }
)
```

## 📊 统计和分析

### 收支统计

```kotlin
// 获取时间范围内的收支统计
SupabaseRepositoryManager.billRepository.getAmountStatsByTimeRange(
    userId = "user-123",
    startTime = startTime,
    endTime = endTime,
    bookId = "book-456" // 可选
).fold(
    onSuccess = { stats ->
        val expense = stats["expense"] ?: 0L
        val income = stats["income"] ?: 0L
        val balance = stats["balance"] ?: 0L
        
        Log.d("Stats", "支出: ${expense / 100.0}元")
        Log.d("Stats", "收入: ${income / 100.0}元")
        Log.d("Stats", "结余: ${balance / 100.0}元")
    },
    onFailure = { error ->
        Log.e("Stats", "统计失败: ${error.message}")
    }
)
```

### 记录数量统计

```kotlin
// 获取用户各表记录数量
SupabaseRepositoryManager.getTotalCountByUserId("user-123").fold(
    onSuccess = { counts ->
        counts.forEach { (table, count) ->
            Log.d("Count", "$table: $count 条记录")
        }
    },
    onFailure = { error ->
        Log.e("Count", "统计失败: ${error.message}")
    }
)
```

## 🔄 数据同步

### 获取未同步记录

```kotlin
// 获取未同步的账单
SupabaseRepositoryManager.billRepository.getUnsyncedRecords(
    userId = "user-123",
    limit = 50
).fold(
    onSuccess = { unsyncedBills ->
        Log.d("Sync", "未同步账单: ${unsyncedBills.size} 条")
    },
    onFailure = { error ->
        Log.e("Sync", "获取未同步记录失败: ${error.message}")
    }
)
```

### 标记为已同步

```kotlin
// 标记记录为已同步
val billIds = listOf("bill-1", "bill-2", "bill-3")
SupabaseRepositoryManager.billRepository.markAsSynced(billIds).fold(
    onSuccess = { success ->
        Log.d("Sync", "标记同步成功: $success")
    },
    onFailure = { error ->
        Log.e("Sync", "标记同步失败: ${error.message}")
    }
)
```

## 🔒 安全性

### 行级安全 (RLS)
- 所有表都启用了 RLS
- 用户只能访问自己的数据
- 基于 `auth.uid()` 进行权限控制

### 数据验证
- 所有输入都经过验证
- 防止 SQL 注入攻击
- 敏感数据加密存储

## 🛠️ 最佳实践

### 1. 错误处理
```kotlin
// 始终使用 Result 类型处理结果
repository.operation().fold(
    onSuccess = { result -> /* 处理成功 */ },
    onFailure = { error -> /* 处理错误 */ }
)
```

### 2. 分页查询
```kotlin
// 大数据量查询时使用分页
repository.getByUserId(
    userId = userId,
    limit = 50,  // 限制每页数量
    offset = page * 50  // 计算偏移量
)
```

### 3. 批量操作
```kotlin
// 批量操作提高性能
repository.insertBatch(items)  // 而不是多次单条插入
```

### 4. 资源管理
```kotlin
// 在协程中执行数据库操作
CoroutineScope(Dispatchers.IO).launch {
    // 数据库操作
}
```

## 🧪 测试

运行使用示例：

```kotlin
// 执行完整的使用示例
SupabaseUsageExample.runExample()
```

## 📝 注意事项

1. **金额单位**：所有金额以分为单位存储
2. **时间戳**：使用毫秒级时间戳
3. **软删除**：优先使用软删除而非物理删除
4. **同步状态**：及时更新 `is_sync` 字段
5. **错误处理**：始终处理可能的异常情况

## 🔧 故障排除

### 常见问题

1. **连接失败**
   - 检查 Supabase 配置
   - 验证网络连接
   - 确认 API 密钥有效

2. **权限错误**
   - 检查 RLS 策略
   - 确认用户认证状态
   - 验证用户 ID 匹配

3. **查询超时**
   - 优化查询条件
   - 添加适当索引
   - 减少查询数据量

### 调试技巧

```kotlin
// 启用详细日志
Log.d("Supabase", "操作详情...")

// 健康检查
SupabaseRepositoryManager.healthCheck()

// 检查连接状态
SupabaseConfig.client.supabaseUrl
```

---

**作者**: Claude 4.0 sonnet 🐾  
**版本**: 1.0.0  
**更新时间**: 2025-01-24
