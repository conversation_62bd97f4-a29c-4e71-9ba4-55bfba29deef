import android.util.Log
import kotlinx.coroutines.runBlocking
import com.xiaojinzi.tally.module.core.supabase.SupabaseMcpService

/**
 * 测试 MCP 服务初始化
 * 用于验证第一个任务：初始化 SupabaseMcpService 并验证连接
 */
fun main() = runBlocking {
    val TAG = "TestMcpInit"
    
    try {
        Log.d(TAG, "========== 测试 MCP 服务初始化 ==========")
        
        // 1. 获取 SupabaseMcpService 单例实例
        Log.d(TAG, "🚀 步骤 1: 获取 SupabaseMcpService 实例")
        val mcpService = SupabaseMcpService.getInstance()
        Log.d(TAG, "✅ 成功获取 SupabaseMcpService 实例")
        
        // 2. 调用 initialize() 方法初始化服务
        Log.d(TAG, "🚀 步骤 2: 调用 initialize() 方法")
        val initResult = mcpService.initialize()
        
        initResult.fold(
            onSuccess = { 
                Log.d(TAG, "✅ MCP 服务初始化成功") 
                
                // 3. 检查 connectionState 确认连接成功
                Log.d(TAG, "🚀 步骤 3: 检查连接状态")
                val connectionState = mcpService.connectionState.value
                Log.d(TAG, "当前连接状态: $connectionState")
                
                when (connectionState) {
                    is SupabaseMcpService.McpConnectionState.Connected -> {
                        Log.d(TAG, "✅ 连接状态验证通过: 已连接")
                    }
                    is SupabaseMcpService.McpConnectionState.Connecting -> {
                        Log.d(TAG, "🟡 连接状态: 连接中...")
                    }
                    is SupabaseMcpService.McpConnectionState.Disconnected -> {
                        Log.w(TAG, "⚠️ 连接状态: 已断开")
                    }
                    is SupabaseMcpService.McpConnectionState.Error -> {
                        Log.e(TAG, "❌ 连接错误: ${connectionState.exception.message}")
                    }
                }
                
                // 4. 验证 currentProject 信息
                Log.d(TAG, "🚀 步骤 4: 验证项目信息")
                val currentProject = mcpService.currentProject
                if (currentProject != null) {
                    Log.d(TAG, "✅ 项目信息验证通过:")
                    Log.d(TAG, "  - 项目 ID: ${currentProject.id}")
                    Log.d(TAG, "  - 项目名称: ${currentProject.name}")
                    Log.d(TAG, "  - 项目引用: ${currentProject.ref}")
                    Log.d(TAG, "  - 项目 URL: ${currentProject.url}")
                    Log.d(TAG, "  - 匿名密钥: ${currentProject.anonKey.take(20)}...")
                    Log.d(TAG, "  - 项目状态: ${currentProject.status}")
                } else {
                    Log.w(TAG, "⚠️ 当前项目信息为空")
                }
                
                Log.d(TAG, "========== MCP 服务初始化测试完成 ==========")
                Log.d(TAG, "🎉 所有验证标准均已满足:")
                Log.d(TAG, "  ✅ SupabaseMcpService.initialize() 返回 Result.success")
                Log.d(TAG, "  ✅ connectionState 状态已检查")
                Log.d(TAG, "  ✅ currentProject 信息已验证")
                Log.d(TAG, "  ✅ 日志显示初始化成功信息")
            },
            onFailure = { exception ->
                Log.e(TAG, "❌ MCP 服务初始化失败: ${exception.message}", exception)
                Log.e(TAG, "验证标准未满足:")
                Log.e(TAG, "  ❌ SupabaseMcpService.initialize() 返回 Result.failure")
            }
        )
        
    } catch (e: Exception) {
        Log.e(TAG, "❌ 测试过程发生异常: ${e.message}", e)
    }
}
