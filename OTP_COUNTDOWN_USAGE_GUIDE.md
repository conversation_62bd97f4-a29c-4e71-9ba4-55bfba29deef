# OTP 发送倒计时功能使用指南

## 概述

本指南介绍了 OtpAuthManagerV2 中新增的倒计时功能，实现了**方案一：无需等回调，直接提示发送成功并添加一分钟的重新发送倒计时**。

## 功能特性

### ✅ 已实现的功能

1. **立即成功提示**：调用 `sendEmailOtp()` 成功后立即返回 `true`，无需等待邮件发送回调
2. **60秒倒计时**：发送成功后自动启动60秒倒计时，防止频繁发送
3. **状态监听**：提供 `resendCountdown` StateFlow 供UI层监听倒计时状态
4. **重复发送保护**：倒计时期间阻止重复发送请求
5. **资源管理**：自动管理倒计时任务的生命周期

## 核心API

### 1. 倒计时状态监听

```kotlin
val otpManager = OtpAuthManagerV2.getInstance()

// 监听倒计时状态
otpManager.resendCountdown.collect { countdown ->
    when (countdown) {
        0 -> {
            // 可以重新发送
            updateUI(enabled = true, text = "发送验证码")
        }
        else -> {
            // 倒计时中
            updateUI(enabled = false, text = "重新发送 (${countdown}s)")
        }
    }
}
```

### 2. 发送验证码

```kotlin
try {
    // 检查是否可以发送
    if (!otpManager.canResend) {
        showMessage("请等待 ${otpManager.resendCountdown.value} 秒后再重新发送")
        return
    }
    
    // 发送验证码
    val success = otpManager.sendEmailOtp(email)
    
    if (success) {
        // 立即显示成功提示
        showSuccess("验证码已发送到您的邮箱，请查收")
        // 倒计时会自动开始，UI通过监听 resendCountdown 更新
    }
    
} catch (e: AuthException) {
    showError(e.message ?: "发送失败")
}
```

### 3. 手动取消倒计时（可选）

```kotlin
// 在某些特殊情况下可以手动取消倒计时
otpManager.cancelResendCountdown()
```

## 使用示例

### Android View 系统示例

```kotlin
class LoginActivity : AppCompatActivity() {
    private val otpManager = OtpAuthManagerV2.getInstance()
    private lateinit var sendButton: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 监听倒计时状态
        lifecycleScope.launch {
            otpManager.resendCountdown.collect { countdown ->
                runOnUiThread {
                    sendButton.isEnabled = countdown == 0
                    sendButton.text = if (countdown == 0) {
                        "发送验证码"
                    } else {
                        "重新发送 (${countdown}s)"
                    }
                }
            }
        }
        
        sendButton.setOnClickListener {
            sendOtp()
        }
    }
    
    private fun sendOtp() {
        lifecycleScope.launch {
            try {
                showLoading(true)
                val success = otpManager.sendEmailOtp(emailEditText.text.toString())
                
                if (success) {
                    Toast.makeText(this@LoginActivity, "验证码已发送，请查收邮箱", Toast.LENGTH_LONG).show()
                }
            } catch (e: AuthException) {
                Toast.makeText(this@LoginActivity, e.message, Toast.LENGTH_SHORT).show()
            } finally {
                showLoading(false)
            }
        }
    }
}
```

### Jetpack Compose 示例

```kotlin
@Composable
fun OtpSendingScreen(
    email: String,
    onEmailChange: (String) -> Unit
) {
    val otpManager = remember { OtpAuthManagerV2.getInstance() }
    val countdown by otpManager.resendCountdown.collectAsState()
    val canResend = countdown == 0
    
    var isLoading by remember { mutableStateOf(false) }
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        OutlinedTextField(
            value = email,
            onValueChange = onEmailChange,
            label = { Text("邮箱地址") },
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = {
                scope.launch {
                    try {
                        isLoading = true
                        val success = otpManager.sendEmailOtp(email)
                        
                        if (success) {
                            snackbarHostState.showSnackbar(
                                message = "验证码已发送到您的邮箱，请查收",
                                duration = SnackbarDuration.Long
                            )
                        }
                    } catch (e: AuthException) {
                        snackbarHostState.showSnackbar(
                            message = e.message ?: "发送失败",
                            duration = SnackbarDuration.Short
                        )
                    } finally {
                        isLoading = false
                    }
                }
            },
            enabled = canResend && !isLoading && email.isNotBlank(),
            modifier = Modifier.fillMaxWidth()
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            Text(
                text = when {
                    isLoading -> "发送中..."
                    canResend -> "发送验证码"
                    else -> "重新发送 (${countdown}s)"
                }
            )
        }
        
        SnackbarHost(hostState = snackbarHostState)
    }
}
```

## 最佳实践

### 1. 用户体验优化

- **立即反馈**：发送成功后立即显示成功提示，不要让用户等待
- **清晰状态**：按钮文本应清楚显示当前状态（发送中、倒计时、可发送）
- **错误处理**：提供友好的错误提示信息

### 2. 性能优化

- **生命周期管理**：在 Activity/Fragment 销毁时取消监听
- **避免内存泄漏**：使用 `lifecycleScope` 或 `viewModelScope`
- **合理使用**：不要频繁创建 OtpAuthManagerV2 实例，使用单例

### 3. 错误处理

```kotlin
try {
    val success = otpManager.sendEmailOtp(email)
    // 处理成功情况
} catch (e: AuthException) {
    when {
        e.message?.contains("请等待") == true -> {
            // 倒计时中的错误，显示剩余时间
        }
        e.message?.contains("邮箱格式") == true -> {
            // 邮箱格式错误
        }
        e.message?.contains("网络") == true -> {
            // 网络错误
        }
        else -> {
            // 其他错误
        }
    }
}
```

## 技术实现细节

### 倒计时机制

- 使用 `MutableStateFlow<Int>` 管理倒计时状态
- 通过 `CoroutineScope` 和 `delay()` 实现倒计时
- 自动取消之前的倒计时任务，避免重复计时

### 线程安全

- 所有操作都在 `Dispatchers.IO` 上下文中执行
- 使用 `SupervisorJob` 确保异常不会影响其他协程
- StateFlow 天然支持多线程安全访问

### 资源管理

- 在 `destroy()` 方法中自动清理所有资源
- 倒计时任务会在管理器销毁时自动取消
- 支持手动取消倒计时

## 注意事项

1. **单例使用**：OtpAuthManagerV2 是单例，确保在应用中只有一个实例
2. **生命周期**：记得在适当的时机调用 `destroy()` 清理资源
3. **网络状态**：发送失败时倒计时不会启动，用户可以立即重试
4. **倒计时时长**：当前固定为60秒，如需修改可在 `startResendCountdown()` 方法中调整

## 更新日志

### v2.1.0 (2025-01-21)
- ✅ 新增倒计时功能
- ✅ 实现方案一：立即成功提示 + 60秒倒计时
- ✅ 添加 `resendCountdown` StateFlow
- ✅ 添加 `canResend` 属性
- ✅ 添加 `cancelResendCountdown()` 方法
- ✅ 完善资源管理和异常处理

---

**作者**: Claude 4.0 sonnet 🐾  
**日期**: 2025-01-21  
**版本**: v2.1.0